[{"id": "ADLT#8:0", "sentence": "Love Al Di La", "postag": ["VBP", "NNP", "NNP", "NNP"], "head": [0, 4, 4, 1], "deprel": ["root", "compound", "compound", "obj"], "triples": [{"uid": "ADLT#8:0-0", "target_tags": "Love\\O Al\\B Di\\I La\\I", "opinion_tags": "Love\\B Al\\O Di\\O La\\O", "sentiment": "positive"}]}, {"id": "ADLT#8:1", "sentence": "I recommend this place to everyone .", "postag": ["PRP", "VBP", "DT", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "obl", "punct"], "triples": [{"uid": "ADLT#8:1-0", "target_tags": "I\\O recommend\\O this\\O place\\B to\\O everyone\\O .\\O", "opinion_tags": "I\\O recommend\\B this\\O place\\O to\\O everyone\\O .\\O", "sentiment": "positive"}]}, {"id": "ADLT#8:2", "sentence": "Great food .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "ADLT#8:2-0", "target_tags": "Great\\O food\\B .\\O", "opinion_tags": "Great\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "ADLT#2:1", "sentence": "The pastas are incredible , the risottos ( particularly the sepia ) are fantastic and the braised rabbit is amazing .", "postag": ["DT", "NNS", "VBP", "JJ", ",", "DT", "NNS", "-LRB-", "RB", "DT", "NN", "-RRB-", "VBP", "JJ", "CC", "DT", "VBN", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 14, 7, 14, 11, 11, 11, 7, 11, 14, 4, 20, 18, 18, 20, 20, 14, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "punct", "advmod", "det", "appos", "punct", "cop", "conj", "cc", "det", "amod", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "ADLT#2:1-0", "target_tags": "The\\O pastas\\B are\\O incredible\\O ,\\O the\\O risottos\\O (\\O particularly\\O the\\O sepia\\O )\\O are\\O fantastic\\O and\\O the\\O braised\\O rabbit\\O is\\O amazing\\O .\\O", "opinion_tags": "The\\O pastas\\O are\\O incredible\\B ,\\O the\\O risottos\\O (\\O particularly\\O the\\O sepia\\O )\\O are\\O fantastic\\O and\\O the\\O braised\\O rabbit\\O is\\O amazing\\O .\\O", "sentiment": "positive"}, {"uid": "ADLT#2:1-1", "target_tags": "The\\O pastas\\O are\\O incredible\\O ,\\O the\\O risottos\\B (\\O particularly\\O the\\O sepia\\O )\\O are\\O fantastic\\O and\\O the\\O braised\\O rabbit\\O is\\O amazing\\O .\\O", "opinion_tags": "The\\O pastas\\O are\\O incredible\\O ,\\O the\\O risottos\\O (\\O particularly\\O the\\O sepia\\O )\\O are\\O fantastic\\B and\\O the\\O braised\\O rabbit\\O is\\O amazing\\O .\\O", "sentiment": "positive"}, {"uid": "ADLT#2:1-2", "target_tags": "The\\O pastas\\O are\\O incredible\\O ,\\O the\\O risottos\\O (\\O particularly\\O the\\O sepia\\B )\\O are\\O fantastic\\O and\\O the\\O braised\\O rabbit\\O is\\O amazing\\O .\\O", "opinion_tags": "The\\O pastas\\O are\\O incredible\\O ,\\O the\\O risottos\\O (\\O particularly\\O the\\O sepia\\O )\\O are\\O fantastic\\B and\\O the\\O braised\\O rabbit\\O is\\O amazing\\O .\\O", "sentiment": "positive"}, {"uid": "ADLT#2:1-3", "target_tags": "The\\O pastas\\O are\\O incredible\\O ,\\O the\\O risottos\\O (\\O particularly\\O the\\O sepia\\O )\\O are\\O fantastic\\O and\\O the\\O braised\\B rabbit\\I is\\O amazing\\O .\\O", "opinion_tags": "The\\O pastas\\O are\\O incredible\\O ,\\O the\\O risottos\\O (\\O particularly\\O the\\O sepia\\O )\\O are\\O fantastic\\O and\\O the\\O braised\\O rabbit\\O is\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "BFC#4:1", "sentence": "The food here was mediocre at best .", "postag": ["DT", "NN", "RB", "VBD", "JJ", "RB", "JJS", "."], "head": [2, 5, 2, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "advmod", "cop", "root", "case", "obl", "punct"], "triples": [{"uid": "BFC#4:1-0", "target_tags": "The\\O food\\B here\\O was\\O mediocre\\O at\\O best\\O .\\O", "opinion_tags": "The\\O food\\O here\\O was\\O mediocre\\B at\\O best\\O .\\O", "sentiment": "negative"}]}, {"id": "BFC#4:2", "sentence": "It was totally overpriced - fish and chips was about $ 15 ... .", "postag": ["PRP", "VBD", "RB", "JJ", "HYPH", "NNS", "CC", "NNS", "VBD", "RB", "$", "CD", ",", "."], "head": [6, 6, 6, 6, 6, 0, 11, 11, 11, 11, 6, 11, 6, 6], "deprel": ["nsubj", "cop", "advmod", "amod", "punct", "root", "cc", "nsubj", "cop", "advmod", "conj", "nummod", "punct", "punct"], "triples": [{"uid": "BFC#4:2-0", "target_tags": "It\\O was\\O totally\\O overpriced\\O -\\O fish\\B and\\I chips\\I was\\O about\\O $\\O 15\\O ...\\O .\\O", "opinion_tags": "It\\O was\\O totally\\O overpriced\\B -\\O fish\\O and\\O chips\\O was\\O about\\O $\\O 15\\O ...\\O .\\O", "sentiment": "negative"}]}, {"id": "BHD#9:0", "sentence": "Tasty Dog !", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "BHD#9:0-0", "target_tags": "Tasty\\O Dog\\B !\\O", "opinion_tags": "Tasty\\B Dog\\O !\\O", "sentiment": "positive"}]}, {"id": "BHD#9:1", "sentence": "An awesome organic dog , and a conscious eco friendly establishment .", "postag": ["DT", "JJ", "JJ", "NN", ",", "CC", "DT", "JJ", "JJ", "JJ", "NN", "."], "head": [4, 4, 4, 0, 11, 11, 11, 11, 11, 11, 4, 4], "deprel": ["det", "amod", "amod", "root", "punct", "cc", "det", "amod", "amod", "amod", "conj", "punct"], "triples": [{"uid": "BHD#9:1-0", "target_tags": "An\\O awesome\\O organic\\O dog\\B ,\\O and\\O a\\O conscious\\O eco\\O friendly\\O establishment\\O .\\O", "opinion_tags": "An\\O awesome\\O organic\\B dog\\O ,\\O and\\O a\\O conscious\\O eco\\O friendly\\O establishment\\O .\\O", "sentiment": "positive"}, {"uid": "BHD#9:1-1", "target_tags": "An\\O awesome\\O organic\\O dog\\O ,\\O and\\O a\\O conscious\\O eco\\O friendly\\O establishment\\B .\\O", "opinion_tags": "An\\O awesome\\O organic\\O dog\\O ,\\O and\\O a\\O conscious\\O eco\\B friendly\\I establishment\\O .\\O", "sentiment": "positive"}]}, {"id": "BzG#1:1", "sentence": "But the best pork souvlaki I ever had is the main thing .", "postag": ["CC", "DT", "JJS", "NN", "NN", "PRP", "RB", "VBD", "VBZ", "DT", "JJ", "NN", "."], "head": [12, 5, 5, 5, 12, 8, 8, 5, 12, 12, 12, 0, 12], "deprel": ["cc", "det", "amod", "compound", "nsubj", "nsubj", "advmod", "acl:relcl", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "BzG#1:1-0", "target_tags": "But\\O the\\O best\\O pork\\B souvlaki\\I I\\O ever\\O had\\O is\\O the\\O main\\O thing\\O .\\O", "opinion_tags": "But\\O the\\O best\\B pork\\O souvlaki\\O I\\O ever\\O had\\O is\\O the\\O main\\O thing\\O .\\O", "sentiment": "positive"}]}, {"id": "EVPK#10:0", "sentence": "Super YUMMY Pizza !", "postag": ["RB", "JJ", "NN", "."], "head": [2, 3, 0, 3], "deprel": ["advmod", "amod", "root", "punct"], "triples": [{"uid": "EVPK#10:0-0", "target_tags": "Super\\O YUMMY\\O Pizza\\B !\\O", "opinion_tags": "Super\\O YUMMY\\B Pizza\\O !\\O", "sentiment": "positive"}]}, {"id": "EVPK#10:1", "sentence": "I was visiting New York City with a friend and we discovered this really warm and inviting restaurant .", "postag": ["PRP", "VBD", "VBG", "NNP", "NNP", "NNP", "IN", "DT", "NN", "CC", "PRP", "VBD", "DT", "RB", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 3, 12, 12, 3, 18, 15, 18, 17, 15, 12, 3], "deprel": ["nsubj", "aux", "root", "compound", "compound", "obj", "case", "det", "obl", "cc", "nsubj", "conj", "det", "advmod", "amod", "cc", "conj", "obj", "punct"], "triples": [{"uid": "EVPK#10:1-0", "target_tags": "I\\O was\\O visiting\\O New\\O York\\O City\\O with\\O a\\O friend\\O and\\O we\\O discovered\\O this\\O really\\O warm\\O and\\O inviting\\O restaurant\\B .\\O", "opinion_tags": "I\\O was\\O visiting\\O New\\O York\\O City\\O with\\O a\\O friend\\O and\\O we\\O discovered\\O this\\O really\\O warm\\O and\\O inviting\\B restaurant\\O .\\O", "sentiment": "positive"}]}, {"id": "EVPK#10:2", "sentence": "I LOOOVE their eggplant pizza , as well as their pastas !", "postag": ["PRP", "VBP", "PRP$", "NN", "NN", ",", "RB", "RB", "IN", "PRP$", "NNS", "."], "head": [2, 0, 5, 5, 2, 11, 11, 7, 7, 11, 5, 2], "deprel": ["nsubj", "root", "nmod:poss", "compound", "obj", "punct", "cc", "fixed", "fixed", "nmod:poss", "conj", "punct"], "triples": [{"uid": "EVPK#10:2-0", "target_tags": "I\\O LOOOVE\\O their\\O eggplant\\B pizza\\I ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O", "opinion_tags": "I\\O LOOOVE\\B their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O", "sentiment": "positive"}, {"uid": "EVPK#10:2-1", "target_tags": "I\\O LOOOVE\\O their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\B !\\O", "opinion_tags": "I\\O LOOOVE\\B their\\O eggplant\\O pizza\\O ,\\O as\\O well\\O as\\O their\\O pastas\\O !\\O", "sentiment": "positive"}]}, {"id": "EVPK#10:3", "sentence": "We had half/half pizza , mine was eggplant and my friend had the buffalo and it was sooo huge for a small size pizza !", "postag": ["PRP", "VBD", "JJ", "NN", ",", "PRP", "VBD", "JJ", "CC", "PRP$", "NN", "VBD", "DT", "NN", "CC", "PRP", "VBD", "RB", "JJ", "IN", "DT", "JJ", "NN", "NN", "."], "head": [2, 0, 4, 2, 2, 8, 8, 2, 12, 11, 12, 2, 14, 12, 19, 19, 19, 19, 2, 24, 24, 24, 24, 19, 2], "deprel": ["nsubj", "root", "amod", "obj", "punct", "nsubj", "cop", "parataxis", "cc", "nmod:poss", "nsubj", "conj", "det", "obj", "cc", "nsubj", "cop", "advmod", "conj", "case", "det", "amod", "compound", "obl", "punct"], "triples": [{"uid": "EVPK#10:3-0", "target_tags": "We\\O had\\O half/half\\B pizza\\I ,\\O mine\\O was\\O eggplant\\O and\\O my\\O friend\\O had\\O the\\O buffalo\\O and\\O it\\O was\\O sooo\\O huge\\O for\\O a\\O small\\O size\\O pizza\\O !\\O", "opinion_tags": "We\\O had\\O half/half\\O pizza\\O ,\\O mine\\O was\\O eggplant\\O and\\O my\\O friend\\O had\\O the\\O buffalo\\O and\\O it\\O was\\O sooo\\O huge\\B for\\O a\\O small\\O size\\O pizza\\O !\\O", "sentiment": "positive"}]}, {"id": "Z#3:0", "sentence": "Excellent food , although the interior could use some help .", "postag": ["JJ", "NN", ",", "IN", "DT", "NN", "MD", "VB", "DT", "NN", "."], "head": [2, 0, 2, 8, 6, 8, 8, 2, 10, 8, 2], "deprel": ["amod", "root", "punct", "mark", "det", "nsubj", "aux", "advcl", "det", "obj", "punct"], "triples": [{"uid": "Z#3:0-0", "target_tags": "Excellent\\O food\\B ,\\O although\\O the\\O interior\\O could\\O use\\O some\\O help\\O .\\O", "opinion_tags": "Excellent\\B food\\O ,\\O although\\O the\\O interior\\O could\\O use\\O some\\O help\\O .\\O", "sentiment": "positive"}, {"uid": "Z#3:0-1", "target_tags": "Excellent\\O food\\O ,\\O although\\O the\\O interior\\B could\\O use\\O some\\O help\\O .\\O", "opinion_tags": "Excellent\\O food\\O ,\\O although\\O the\\O interior\\O could\\O use\\O some\\O help\\B .\\O", "sentiment": "negative"}]}, {"id": "Z#3:2", "sentence": "I paid just about $ 60 for a good meal , though : )", "postag": ["PRP", "VBD", "RB", "RB", "$", "CD", "IN", "DT", "JJ", "NN", ",", "RB", ":", "-RRB-"], "head": [2, 0, 5, 5, 2, 5, 10, 10, 10, 2, 2, 2, 2, 2], "deprel": ["nsubj", "root", "advmod", "advmod", "obj", "nummod", "case", "det", "amod", "obl", "punct", "advmod", "punct", "punct"], "triples": [{"uid": "Z#3:2-0", "target_tags": "I\\O paid\\O just\\O about\\O $\\O 60\\O for\\O a\\O good\\O meal\\B ,\\O though\\O :\\O )\\O", "opinion_tags": "I\\O paid\\O just\\O about\\O $\\O 60\\O for\\O a\\O good\\B meal\\O ,\\O though\\O :\\O )\\O", "sentiment": "positive"}]}, {"id": "Z#3:3", "sentence": "Great sake !", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "Z#3:3-0", "target_tags": "Great\\O sake\\B !\\O", "opinion_tags": "Great\\B sake\\O !\\O", "sentiment": "positive"}]}, {"id": "Y#7:0", "sentence": "Reliable , Fresh Sushi", "postag": ["JJ", ",", "JJ", "NN"], "head": [4, 4, 4, 0], "deprel": ["amod", "punct", "amod", "root"], "triples": [{"uid": "Y#7:0-0", "target_tags": "Reliable\\O ,\\O Fresh\\O Sushi\\B", "opinion_tags": "Reliable\\B ,\\O Fresh\\B Sushi\\O", "sentiment": "positive"}]}, {"id": "Y#7:2", "sentence": "The sashimi is always fresh and the rolls are innovative and delicious .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "DT", "NNS", "VBP", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 10, 8, 10, 10, 5, 12, 10, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "det", "nsubj", "cop", "conj", "cc", "conj", "punct"], "triples": [{"uid": "Y#7:2-0", "target_tags": "The\\O sashimi\\B is\\O always\\O fresh\\O and\\O the\\O rolls\\O are\\O innovative\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O sashimi\\O is\\O always\\O fresh\\B and\\O the\\O rolls\\O are\\O innovative\\O and\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "Y#7:2-1", "target_tags": "The\\O sashimi\\O is\\O always\\O fresh\\O and\\O the\\O rolls\\B are\\O innovative\\O and\\O delicious\\O .\\O", "opinion_tags": "The\\O sashimi\\O is\\O always\\O fresh\\O and\\O the\\O rolls\\O are\\O innovative\\B and\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "Y#7:3", "sentence": "Have never had a problem with service save a missing rice once .", "postag": ["VBP", "RB", "VBN", "DT", "NN", "IN", "NN", "VB", "DT", "JJ", "NN", "RB", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 11, 11, 8, 8, 3], "deprel": ["aux", "advmod", "root", "det", "obj", "mark", "nsubj", "acl", "det", "amod", "obj", "advmod", "punct"], "triples": [{"uid": "Y#7:3-0", "target_tags": "Have\\O never\\O had\\O a\\O problem\\O with\\O service\\B save\\O a\\O missing\\O rice\\O once\\O .\\O", "opinion_tags": "Have\\O never\\O had\\O a\\O problem\\B with\\O service\\O save\\O a\\O missing\\O rice\\O once\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#7:4", "sentence": "Delivery can be spot on or lacking depending on the weather and the day of the week .", "postag": ["NN", "MD", "VB", "RB", "IN", "CC", "VBG", "VBG", "IN", "DT", "NN", "CC", "DT", "NN", "IN", "DT", "NN", "."], "head": [3, 3, 4, 5, 0, 7, 5, 11, 11, 11, 5, 14, 14, 11, 17, 17, 14, 3], "deprel": ["nsubj", "aux", "cop", "advmod", "root", "cc", "conj", "case", "case", "det", "obl", "cc", "det", "conj", "case", "det", "nmod", "punct"], "triples": [{"uid": "Y#7:4-0", "target_tags": "Delivery\\B can\\O be\\O spot\\O on\\O or\\O lacking\\O depending\\O on\\O the\\O weather\\O and\\O the\\O day\\O of\\O the\\O week\\O .\\O", "opinion_tags": "Delivery\\O can\\O be\\O spot\\O on\\O or\\O lacking\\B depending\\O on\\O the\\O weather\\O and\\O the\\O day\\O of\\O the\\O week\\O .\\O", "sentiment": "negative"}]}, {"id": "Y#7:5", "sentence": "Delivery guy sometimes get upset if you do n't tip more than 10 % .", "postag": ["NN", "NN", "RB", "VBP", "JJ", "IN", "PRP", "VBP", "RB", "VB", "JJR", "IN", "CD", "NN", "."], "head": [2, 4, 4, 0, 4, 10, 10, 10, 10, 4, 13, 11, 14, 10, 4], "deprel": ["compound", "nsubj", "advmod", "root", "xcomp", "mark", "nsubj", "aux", "advmod", "advcl", "advmod", "fixed", "nummod", "obj", "punct"], "triples": [{"uid": "Y#7:5-0", "target_tags": "Delivery\\B guy\\I sometimes\\O get\\O upset\\O if\\O you\\O do\\O n't\\O tip\\O more\\O than\\O 10\\O %\\O .\\O", "opinion_tags": "Delivery\\O guy\\O sometimes\\O get\\O upset\\B if\\O you\\O do\\O n't\\O tip\\O more\\O than\\O 10\\O %\\O .\\O", "sentiment": "negative"}]}, {"id": "Y#3:0", "sentence": "Best . Sushi . Ever .", "postag": ["JJS", ".", "NN", ".", "RB", "."], "head": [3, 3, 0, 3, 3, 3], "deprel": ["amod", "punct", "root", "punct", "advmod", "punct"], "triples": [{"uid": "Y#3:0-0", "target_tags": "Best\\O .\\O Sushi\\B .\\O Ever\\O .\\O", "opinion_tags": "Best\\B .\\O Sushi\\O .\\O Ever\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#3:1", "sentence": "This place has ruined me for neighborhood sushi .", "postag": ["DT", "NN", "VBZ", "VBN", "PRP", "IN", "NN", "NN", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 4], "deprel": ["det", "nsubj", "aux", "root", "obj", "case", "compound", "obl", "punct"], "triples": [{"uid": "Y#3:1-0", "target_tags": "This\\O place\\O has\\O ruined\\O me\\O for\\O neighborhood\\O sushi\\B .\\O", "opinion_tags": "This\\O place\\O has\\O ruined\\B me\\O for\\O neighborhood\\O sushi\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#3:3", "sentence": "Excellent sashimi , and the millennium roll is beyond delicious .", "postag": ["JJ", "NN", ",", "CC", "DT", "NN", "NN", "VBZ", "IN", "JJ", "."], "head": [2, 10, 7, 7, 7, 7, 2, 10, 10, 0, 2], "deprel": ["amod", "nsubj", "punct", "cc", "det", "compound", "conj", "cop", "case", "root", "punct"], "triples": [{"uid": "Y#3:3-0", "target_tags": "Excellent\\O sashimi\\B ,\\O and\\O the\\O millennium\\O roll\\O is\\O beyond\\O delicious\\O .\\O", "opinion_tags": "Excellent\\B sashimi\\O ,\\O and\\O the\\O millennium\\O roll\\O is\\O beyond\\O delicious\\O .\\O", "sentiment": "positive"}, {"uid": "Y#3:3-1", "target_tags": "Excellent\\O sashimi\\O ,\\O and\\O the\\O millennium\\B roll\\I is\\O beyond\\O delicious\\O .\\O", "opinion_tags": "Excellent\\O sashimi\\O ,\\O and\\O the\\O millennium\\O roll\\O is\\O beyond\\O delicious\\B .\\O", "sentiment": "positive"}]}, {"id": "Y#2:1", "sentence": "The place is a bit hidden away , but once you get there , it 's all worth it .", "postag": ["DT", "NN", "VBZ", "DT", "NN", "VBN", "RB", ",", "CC", "IN", "PRP", "VBP", "RB", ",", "PRP", "VBZ", "RB", "JJ", "PRP", "."], "head": [2, 6, 6, 5, 6, 0, 6, 18, 18, 12, 12, 18, 12, 18, 18, 18, 18, 6, 18, 6], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "advmod", "punct", "cc", "mark", "nsubj", "advcl", "advmod", "punct", "nsubj", "cop", "advmod", "conj", "obj", "punct"], "triples": [{"uid": "Y#2:1-0", "target_tags": "The\\O place\\B is\\O a\\O bit\\O hidden\\O away\\O ,\\O but\\O once\\O you\\O get\\O there\\O ,\\O it\\O 's\\O all\\O worth\\O it\\O .\\O", "opinion_tags": "The\\O place\\O is\\O a\\O bit\\O hidden\\B away\\I ,\\O but\\O once\\O you\\O get\\O there\\O ,\\O it\\O 's\\O all\\O worth\\B it\\O .\\O", "sentiment": "positive"}]}, {"id": "WE#9:3", "sentence": "The waiter was attentive , the food was delicious and the views of the city were great .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", "CC", "DT", "NNS", "IN", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 17, 12, 17, 15, 15, 12, 17, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "conj", "cc", "det", "nsubj", "case", "det", "nmod", "cop", "conj", "punct"], "triples": [{"uid": "WE#9:3-0", "target_tags": "The\\O waiter\\B was\\O attentive\\O ,\\O the\\O food\\O was\\O delicious\\O and\\O the\\O views\\O of\\O the\\O city\\O were\\O great\\O .\\O", "opinion_tags": "The\\O waiter\\O was\\O attentive\\B ,\\O the\\O food\\O was\\O delicious\\O and\\O the\\O views\\O of\\O the\\O city\\O were\\O great\\O .\\O", "sentiment": "positive"}, {"uid": "WE#9:3-1", "target_tags": "The\\O waiter\\O was\\O attentive\\O ,\\O the\\O food\\B was\\O delicious\\O and\\O the\\O views\\O of\\O the\\O city\\O were\\O great\\O .\\O", "opinion_tags": "The\\O waiter\\O was\\O attentive\\O ,\\O the\\O food\\O was\\O delicious\\B and\\O the\\O views\\O of\\O the\\O city\\O were\\O great\\O .\\O", "sentiment": "positive"}, {"uid": "WE#9:3-2", "target_tags": "The\\O waiter\\O was\\O attentive\\O ,\\O the\\O food\\O was\\O delicious\\O and\\O the\\O views\\B of\\I the\\I city\\I were\\O great\\O .\\O", "opinion_tags": "The\\O waiter\\O was\\O attentive\\O ,\\O the\\O food\\O was\\O delicious\\O and\\O the\\O views\\O of\\O the\\O city\\O were\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "TR#3:0", "sentence": "Great place to relax and enjoy your dinner", "postag": ["JJ", "NN", "TO", "VB", "CC", "VB", "PRP$", "NN"], "head": [2, 0, 4, 2, 6, 4, 8, 6], "deprel": ["amod", "root", "mark", "acl", "cc", "conj", "nmod:poss", "obj"], "triples": [{"uid": "TR#3:0-0", "target_tags": "Great\\O place\\B to\\O relax\\O and\\O enjoy\\O your\\O dinner\\O", "opinion_tags": "Great\\B place\\O to\\O relax\\O and\\O enjoy\\O your\\O dinner\\O", "sentiment": "positive"}]}, {"id": "TR#3:3", "sentence": "Good food : my favorite is the seafood spaghetti .", "postag": ["JJ", "NN", ":", "PRP$", "NN", "VBZ", "DT", "NN", "NN", "."], "head": [2, 0, 2, 5, 9, 9, 9, 9, 2, 2], "deprel": ["amod", "root", "punct", "nmod:poss", "nsubj", "cop", "det", "compound", "parataxis", "punct"], "triples": [{"uid": "TR#3:3-0", "target_tags": "Good\\O food\\B :\\O my\\O favorite\\O is\\O the\\O seafood\\O spaghetti\\O .\\O", "opinion_tags": "Good\\B food\\O :\\O my\\O favorite\\O is\\O the\\O seafood\\O spaghetti\\O .\\O", "sentiment": "positive"}, {"uid": "TR#3:3-1", "target_tags": "Good\\O food\\O :\\O my\\O favorite\\O is\\O the\\O seafood\\B spaghetti\\I .\\O", "opinion_tags": "Good\\O food\\O :\\O my\\O favorite\\B is\\O the\\O seafood\\O spaghetti\\O .\\O", "sentiment": "positive"}]}, {"id": "TM#7:0", "sentence": "Excellent food for great prices", "postag": ["JJ", "NN", "IN", "JJ", "NNS"], "head": [2, 0, 5, 5, 2], "deprel": ["amod", "root", "case", "amod", "nmod"], "triples": [{"uid": "TM#7:0-0", "target_tags": "Excellent\\O food\\B for\\O great\\O prices\\O", "opinion_tags": "Excellent\\B food\\O for\\O great\\O prices\\O", "sentiment": "positive"}]}, {"id": "TM#7:2", "sentence": "The wait staff is very courteous and accomodating .", "postag": ["DT", "NN", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 8, 6, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"], "triples": [{"uid": "TM#7:2-0", "target_tags": "The\\O wait\\B staff\\I is\\O very\\O courteous\\O and\\O accomodating\\O .\\O", "opinion_tags": "The\\O wait\\O staff\\O is\\O very\\O courteous\\B and\\O accomodating\\B .\\O", "sentiment": "positive"}]}, {"id": "TM#7:3", "sentence": "The space is limited so be prepared to wait up to 45 minutes - 1 hour , but be richly rewarded when you savor the delicious indo-chinese food .", "postag": ["DT", "NN", "VBZ", "VBN", "RB", "VB", "VBN", "TO", "VB", "RP", "IN", "CD", "NNS", "SYM", "CD", "NN", ",", "CC", "VB", "RB", "VBN", "WRB", "PRP", "VBP", "DT", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 7, 9, 13, 13, 9, 16, 16, 13, 21, 21, 21, 21, 4, 24, 24, 21, 28, 28, 28, 24, 4], "deprel": ["det", "nsubj:pass", "aux:pass", "root", "advmod", "aux:pass", "conj", "mark", "xcomp", "compound:prt", "case", "nummod", "obl", "case", "nummod", "nmod", "punct", "cc", "aux:pass", "advmod", "conj", "mark", "nsubj", "advcl", "det", "amod", "amod", "obj", "punct"], "triples": [{"uid": "TM#7:3-0", "target_tags": "The\\O space\\B is\\O limited\\O so\\O be\\O prepared\\O to\\O wait\\O up\\O to\\O 45\\O minutes\\O -\\O 1\\O hour\\O ,\\O but\\O be\\O richly\\O rewarded\\O when\\O you\\O savor\\O the\\O delicious\\O indo-chinese\\O food\\O .\\O", "opinion_tags": "The\\O space\\O is\\O limited\\B so\\O be\\O prepared\\O to\\O wait\\O up\\O to\\O 45\\O minutes\\O -\\O 1\\O hour\\O ,\\O but\\O be\\O richly\\O rewarded\\O when\\O you\\O savor\\O the\\O delicious\\O indo-chinese\\O food\\O .\\O", "sentiment": "negative"}, {"uid": "TM#7:3-1", "target_tags": "The\\O space\\O is\\O limited\\O so\\O be\\O prepared\\O to\\O wait\\O up\\O to\\O 45\\O minutes\\O -\\O 1\\O hour\\O ,\\O but\\O be\\O richly\\O rewarded\\O when\\O you\\O savor\\O the\\O delicious\\O indo-chinese\\B food\\I .\\O", "opinion_tags": "The\\O space\\O is\\O limited\\O so\\O be\\O prepared\\O to\\O wait\\O up\\O to\\O 45\\O minutes\\O -\\O 1\\O hour\\O ,\\O but\\O be\\O richly\\O rewarded\\O when\\O you\\O savor\\O the\\O delicious\\B indo-chinese\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "RL#7:1", "sentence": "my favorite place lol", "postag": ["PRP$", "JJ", "NN", "UH"], "head": [3, 3, 0, 3], "deprel": ["nmod:poss", "amod", "root", "discourse"], "triples": [{"uid": "RL#7:1-0", "target_tags": "my\\O favorite\\O place\\B lol\\O", "opinion_tags": "my\\O favorite\\B place\\O lol\\O", "sentiment": "positive"}]}, {"id": "RL#7:2", "sentence": "i love their chicken pasta cant remember the name but is sooo good", "postag": ["PRP", "VBP", "PRP$", "NN", "NN", "MD", "VB", "DT", "NN", "CC", "VBZ", "RB", "JJ"], "head": [2, 0, 5, 5, 2, 7, 2, 9, 7, 13, 13, 13, 2], "deprel": ["nsubj", "root", "nmod:poss", "compound", "obj", "aux", "ccomp", "det", "obj", "cc", "cop", "advmod", "conj"], "triples": [{"uid": "RL#7:2-0", "target_tags": "i\\O love\\O their\\O chicken\\B pasta\\I cant\\O remember\\O the\\O name\\O but\\O is\\O sooo\\O good\\O", "opinion_tags": "i\\O love\\B their\\O chicken\\O pasta\\O cant\\O remember\\O the\\O name\\O but\\O is\\O sooo\\O good\\O", "sentiment": "positive"}]}, {"id": "RL#3:1", "sentence": "im not necessarily fanatical about this place , but it was a fun time for low pirces .", "postag": ["VBZ", "RB", "RB", "JJ", "IN", "DT", "NN", ",", "CC", "PRP", "VBD", "DT", "JJ", "NN", "IN", "JJ", "NNS", "."], "head": [4, 4, 4, 0, 7, 7, 4, 14, 14, 14, 14, 14, 14, 4, 17, 17, 14, 4], "deprel": ["cop", "advmod", "advmod", "root", "case", "det", "obl", "punct", "cc", "nsubj", "cop", "det", "amod", "conj", "case", "amod", "nmod", "punct"], "triples": [{"uid": "RL#3:1-0", "target_tags": "im\\O not\\O necessarily\\O fanatical\\O about\\O this\\O place\\B ,\\O but\\O it\\O was\\O a\\O fun\\O time\\O for\\O low\\O pirces\\O .\\O", "opinion_tags": "im\\O not\\O necessarily\\O fanatical\\B about\\O this\\O place\\O ,\\O but\\O it\\O was\\O a\\O fun\\O time\\O for\\O low\\O pirces\\O .\\O", "sentiment": "positive"}]}, {"id": "RL#3:2", "sentence": "lobster was good , nothing spectacular .", "postag": ["NN", "VBD", "JJ", ",", "NN", "JJ", "."], "head": [3, 3, 0, 3, 3, 5, 3], "deprel": ["nsubj", "cop", "root", "punct", "parataxis", "amod", "punct"], "triples": [{"uid": "RL#3:2-0", "target_tags": "lobster\\B was\\O good\\O ,\\O nothing\\O spectacular\\O .\\O", "opinion_tags": "lobster\\O was\\O good\\B ,\\O nothing\\B spectacular\\I .\\O", "sentiment": "neutral"}]}, {"id": "RL#3:3", "sentence": "its just a fun place to go , not a five star restaraunt .", "postag": ["PRP$", "RB", "DT", "JJ", "NN", "TO", "VB", ",", "RB", "DT", "CD", "NN", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 13, 13, 13, 12, 13, 5, 5], "deprel": ["nmod:poss", "advmod", "det", "amod", "root", "mark", "acl", "punct", "advmod", "det", "nummod", "compound", "conj", "punct"], "triples": [{"uid": "RL#3:3-0", "target_tags": "its\\O just\\O a\\O fun\\O place\\O to\\O go\\O ,\\O not\\O a\\O five\\O star\\O restaraunt\\B .\\O", "opinion_tags": "its\\O just\\O a\\O fun\\O place\\O to\\O go\\O ,\\O not\\O a\\O five\\B star\\I restaraunt\\O .\\O", "sentiment": "neutral"}]}, {"id": "PP#9:1", "sentence": "I think the pizza is so overrated and was under cooked .", "postag": ["PRP", "VBP", "DT", "NN", "VBZ", "RB", "JJ", "CC", "VBD", "IN", "VBN", "."], "head": [2, 0, 4, 7, 7, 7, 2, 11, 11, 11, 7, 2], "deprel": ["nsubj", "root", "det", "nsubj", "cop", "advmod", "ccomp", "cc", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "PP#9:1-0", "target_tags": "I\\O think\\O the\\O pizza\\B is\\O so\\O overrated\\O and\\O was\\O under\\O cooked\\O .\\O", "opinion_tags": "I\\O think\\O the\\O pizza\\O is\\O so\\O overrated\\B and\\O was\\O under\\B cooked\\I .\\O", "sentiment": "negative"}]}, {"id": "PP#9:2", "sentence": "Had no flavor and the staff is rude and not attentive .", "postag": ["VBD", "DT", "NN", "CC", "DT", "NN", "VBZ", "JJ", "CC", "RB", "JJ", "."], "head": [0, 3, 1, 8, 6, 8, 8, 1, 11, 11, 8, 1], "deprel": ["root", "det", "obj", "cc", "det", "nsubj", "cop", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "PP#9:2-0", "target_tags": "Had\\O no\\O flavor\\O and\\O the\\O staff\\B is\\O rude\\O and\\O not\\O attentive\\O .\\O", "opinion_tags": "Had\\O no\\O flavor\\O and\\O the\\O staff\\O is\\O rude\\B and\\O not\\B attentive\\I .\\O", "sentiment": "negative"}]}, {"id": "PP#4:0", "sentence": "I love this place", "postag": ["PRP", "VBP", "DT", "NN"], "head": [2, 0, 4, 2], "deprel": ["nsubj", "root", "det", "obj"], "triples": [{"uid": "PP#4:0-0", "target_tags": "I\\O love\\O this\\O place\\B", "opinion_tags": "I\\O love\\B this\\O place\\O", "sentiment": "positive"}]}, {"id": "PP#4:2", "sentence": "The service was quick and friendly .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "PP#4:2-0", "target_tags": "The\\O service\\B was\\O quick\\O and\\O friendly\\O .\\O", "opinion_tags": "The\\O service\\O was\\O quick\\B and\\O friendly\\B .\\O", "sentiment": "positive"}]}, {"id": "PP#4:4", "sentence": "I thought the restaurant was nice and clean .", "postag": ["PRP", "VBD", "DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 0, 4, 6, 6, 2, 8, 6, 2], "deprel": ["nsubj", "root", "det", "nsubj", "cop", "ccomp", "cc", "conj", "punct"], "triples": [{"uid": "PP#4:4-0", "target_tags": "I\\O thought\\O the\\O restaurant\\B was\\O nice\\O and\\O clean\\O .\\O", "opinion_tags": "I\\O thought\\O the\\O restaurant\\O was\\O nice\\B and\\O clean\\B .\\O", "sentiment": "positive"}]}, {"id": "PP#4:5", "sentence": "I ordered the vitello alla marsala and I was pretty impressed .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "NN", "CC", "PRP", "VBD", "RB", "JJ", "."], "head": [2, 0, 6, 6, 6, 2, 11, 11, 11, 11, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "compound", "obj", "cc", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "PP#4:5-0", "target_tags": "I\\O ordered\\O the\\O vitello\\B alla\\I marsala\\I and\\O I\\O was\\O pretty\\O impressed\\O .\\O", "opinion_tags": "I\\O ordered\\O the\\O vitello\\O alla\\O marsala\\O and\\O I\\O was\\O pretty\\O impressed\\B .\\O", "sentiment": "positive"}]}, {"id": "PP#4:6", "sentence": "The veal and the mushrooms were cooked perfectly .", "postag": ["DT", "NN", "CC", "DT", "NNS", "VBD", "VBN", "RB", "."], "head": [2, 7, 5, 5, 2, 7, 0, 7, 7], "deprel": ["det", "nsubj:pass", "cc", "det", "conj", "aux:pass", "root", "advmod", "punct"], "triples": [{"uid": "PP#4:6-0", "target_tags": "The\\O veal\\B and\\O the\\O mushrooms\\O were\\O cooked\\O perfectly\\O .\\O", "opinion_tags": "The\\O veal\\O and\\O the\\O mushrooms\\O were\\O cooked\\O perfectly\\B .\\O", "sentiment": "positive"}, {"uid": "PP#4:6-1", "target_tags": "The\\O veal\\O and\\O the\\O mushrooms\\B were\\O cooked\\O perfectly\\O .\\O", "opinion_tags": "The\\O veal\\O and\\O the\\O mushrooms\\O were\\O cooked\\O perfectly\\B .\\O", "sentiment": "positive"}]}, {"id": "PP#4:7", "sentence": "The potato balls were not dry at all ... in fact it was buttery .", "postag": ["DT", "NN", "NNS", "VBD", "RB", "JJ", "IN", "DT", ",", "IN", "NN", "PRP", "VBD", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 8, 6, 6, 11, 14, 14, 14, 6, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "case", "obl", "punct", "case", "obl", "nsubj", "cop", "parataxis", "punct"], "triples": [{"uid": "PP#4:7-0", "target_tags": "The\\O potato\\B balls\\I were\\O not\\O dry\\O at\\O all\\O ...\\O in\\O fact\\O it\\O was\\O buttery\\O .\\O", "opinion_tags": "The\\O potato\\O balls\\O were\\O not\\B dry\\I at\\O all\\O ...\\O in\\O fact\\O it\\O was\\O buttery\\B .\\O", "sentiment": "positive"}]}, {"id": "P#1:0", "sentence": "WORST PLACE ON <PERSON><PERSON><PERSON> STREET IN BROOKLYN", "postag": ["JJS", "NN", "IN", "NNP", "NNP", "IN", "NNP"], "head": [2, 0, 5, 5, 2, 7, 5], "deprel": ["amod", "root", "case", "compound", "nmod", "case", "nmod"], "triples": [{"uid": "P#1:0-0", "target_tags": "WORST\\O PLACE\\B ON\\O SMITH\\O STREET\\O IN\\O BROOKLYN\\O", "opinion_tags": "WORST\\B PLACE\\O ON\\O SMITH\\O STREET\\O IN\\O BROOKLYN\\O", "sentiment": "negative"}]}, {"id": "P#1:1", "sentence": "Very immature bartender , didnt know how to make specific drinks , service was so slowwwww , the food was not fresh or warm , waitresses were busy flirting with men at the bar and werent very attentive to all the customers .", "postag": ["RB", "JJ", "NN", ",", "MD", "VB", "WRB", "TO", "VB", "JJ", "NNS", ",", "NN", "VBD", "RB", "JJ", ",", "DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", ",", "NNS", "VBD", "JJ", "VBG", "IN", "NNS", "IN", "DT", "NN", "CC", "VBD", "RB", "JJ", "IN", "PDT", "DT", "NNS", "."], "head": [2, 3, 0, 3, 6, 3, 9, 9, 6, 11, 9, 3, 16, 16, 16, 3, 3, 19, 22, 22, 22, 16, 24, 22, 3, 28, 28, 3, 28, 31, 29, 34, 34, 29, 38, 38, 38, 22, 42, 42, 42, 38, 3], "deprel": ["advmod", "amod", "root", "punct", "aux", "parataxis", "mark", "mark", "ccomp", "amod", "obj", "punct", "nsubj", "cop", "advmod", "parataxis", "punct", "det", "nsubj", "cop", "advmod", "conj", "cc", "conj", "punct", "nsubj", "cop", "parataxis", "xcomp", "case", "obl", "case", "det", "obl", "cc", "cop", "advmod", "conj", "case", "det:predet", "det", "obl", "punct"], "triples": [{"uid": "P#1:1-0", "target_tags": "Very\\O immature\\O bartender\\B ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\O was\\O so\\O slowwwww\\O ,\\O the\\O food\\O was\\O not\\O fresh\\O or\\O warm\\O ,\\O waitresses\\O were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\O very\\O attentive\\O to\\O all\\O the\\O customers\\O .\\O", "opinion_tags": "Very\\O immature\\B bartender\\O ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\O was\\O so\\O slowwwww\\O ,\\O the\\O food\\O was\\O not\\O fresh\\O or\\O warm\\O ,\\O waitresses\\O were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\O very\\O attentive\\O to\\O all\\O the\\O customers\\O .\\O", "sentiment": "negative"}, {"uid": "P#1:1-1", "target_tags": "Very\\O immature\\O bartender\\O ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\B was\\O so\\O slowwwww\\O ,\\O the\\O food\\O was\\O not\\O fresh\\O or\\O warm\\O ,\\O waitresses\\O were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\O very\\O attentive\\O to\\O all\\O the\\O customers\\O .\\O", "opinion_tags": "Very\\O immature\\O bartender\\O ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\O was\\O so\\O slowwwww\\B ,\\O the\\O food\\O was\\O not\\O fresh\\O or\\O warm\\O ,\\O waitresses\\O were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\O very\\O attentive\\O to\\O all\\O the\\O customers\\O .\\O", "sentiment": "negative"}, {"uid": "P#1:1-2", "target_tags": "Very\\O immature\\O bartender\\O ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\O was\\O so\\O slowwwww\\O ,\\O the\\O food\\B was\\O not\\O fresh\\O or\\O warm\\O ,\\O waitresses\\O were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\O very\\O attentive\\O to\\O all\\O the\\O customers\\O .\\O", "opinion_tags": "Very\\O immature\\O bartender\\O ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\O was\\O so\\O slowwwww\\O ,\\O the\\O food\\O was\\O not\\B fresh\\I or\\I warm\\I ,\\O waitresses\\O were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\O very\\O attentive\\O to\\O all\\O the\\O customers\\O .\\O", "sentiment": "negative"}, {"uid": "P#1:1-3", "target_tags": "Very\\O immature\\O bartender\\O ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\O was\\O so\\O slowwwww\\O ,\\O the\\O food\\O was\\O not\\O fresh\\O or\\O warm\\O ,\\O waitresses\\B were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\O very\\O attentive\\O to\\O all\\O the\\O customers\\O .\\O", "opinion_tags": "Very\\O immature\\O bartender\\O ,\\O didnt\\O know\\O how\\O to\\O make\\O specific\\O drinks\\O ,\\O service\\O was\\O so\\O slowwwww\\O ,\\O the\\O food\\O was\\O not\\O fresh\\O or\\O warm\\O ,\\O waitresses\\O were\\O busy\\O flirting\\O with\\O men\\O at\\O the\\O bar\\O and\\O werent\\B very\\I attentive\\I to\\O all\\O the\\O customers\\O .\\O", "sentiment": "negative"}]}, {"id": "P#1:2", "sentence": "I would never recommend this place to anybody even for a casual dinner .", "postag": ["PRP", "MD", "RB", "VB", "DT", "NN", "IN", "NN", "RB", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 8, 4, 13, 13, 13, 13, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "obj", "case", "obl", "advmod", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "P#1:2-0", "target_tags": "I\\O would\\O never\\O recommend\\O this\\O place\\B to\\O anybody\\O even\\O for\\O a\\O casual\\O dinner\\O .\\O", "opinion_tags": "I\\O would\\O never\\B recommend\\I this\\O place\\O to\\O anybody\\O even\\O for\\O a\\O casual\\O dinner\\O .\\O", "sentiment": "negative"}]}, {"id": "RL#9:1", "sentence": "the food is always fresh ...", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "RL#9:1-0", "target_tags": "the\\O food\\B is\\O always\\O fresh\\O ...\\O", "opinion_tags": "the\\O food\\O is\\O always\\O fresh\\B ...\\O", "sentiment": "positive"}]}, {"id": "Y#5:0", "sentence": "overpriced japanese food with mediocre service", "postag": ["JJ", "JJ", "NN", "IN", "JJ", "NN"], "head": [3, 3, 0, 6, 6, 3], "deprel": ["amod", "amod", "root", "case", "amod", "nmod"], "triples": [{"uid": "Y#5:0-0", "target_tags": "overpriced\\O japanese\\B food\\I with\\O mediocre\\O service\\O", "opinion_tags": "overpriced\\B japanese\\O food\\O with\\O mediocre\\O service\\O", "sentiment": "negative"}, {"uid": "Y#5:0-1", "target_tags": "overpriced\\O japanese\\O food\\O with\\O mediocre\\O service\\B", "opinion_tags": "overpriced\\O japanese\\O food\\O with\\O mediocre\\B service\\O", "sentiment": "neutral"}]}, {"id": "Y#5:3", "sentence": "food was luke warm .", "postag": ["NN", "VBD", "NNP", "JJ", "."], "head": [3, 3, 0, 3, 3], "deprel": ["nsubj", "cop", "root", "amod", "punct"], "triples": [{"uid": "Y#5:3-0", "target_tags": "food\\B was\\O luke\\O warm\\O .\\O", "opinion_tags": "food\\O was\\O luke\\B warm\\I .\\O", "sentiment": "negative"}]}, {"id": "Y#5:4", "sentence": "The waitress was not attentive at all .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "IN", "DT", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"], "triples": [{"uid": "Y#5:4-0", "target_tags": "The\\O waitress\\B was\\O not\\O attentive\\O at\\O all\\O .\\O", "opinion_tags": "The\\O waitress\\O was\\O not\\B attentive\\I at\\O all\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#8:1", "sentence": "The food was excellent as well as service , however , I left The Four Seasons very dissappointed .", "postag": ["DT", "NN", "VBD", "JJ", "RB", "RB", "IN", "NN", ",", "RB", ",", "PRP", "VBD", "DT", "CD", "NNPS", "RB", "JJ", "."], "head": [2, 4, 4, 0, 8, 5, 5, 4, 4, 13, 13, 13, 4, 16, 16, 13, 18, 13, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "fixed", "fixed", "conj", "punct", "advmod", "punct", "nsubj", "parataxis", "det", "nummod", "obj", "advmod", "xcomp", "punct"], "triples": [{"uid": "TFS#8:1-0", "target_tags": "The\\O food\\B was\\O excellent\\O as\\O well\\O as\\O service\\O ,\\O however\\O ,\\O I\\O left\\O The\\O Four\\O Seasons\\O very\\O dissappointed\\O .\\O", "opinion_tags": "The\\O food\\O was\\O excellent\\B as\\O well\\O as\\O service\\O ,\\O however\\O ,\\O I\\O left\\O The\\O Four\\O Seasons\\O very\\O dissappointed\\O .\\O", "sentiment": "positive"}, {"uid": "TFS#8:1-1", "target_tags": "The\\O food\\O was\\O excellent\\O as\\O well\\O as\\O service\\B ,\\O however\\O ,\\O I\\O left\\O The\\O Four\\O Seasons\\O very\\O dissappointed\\O .\\O", "opinion_tags": "The\\O food\\O was\\O excellent\\B as\\O well\\O as\\O service\\O ,\\O however\\O ,\\O I\\O left\\O The\\O Four\\O Seasons\\O very\\O dissappointed\\O .\\O", "sentiment": "positive"}, {"uid": "TFS#8:1-2", "target_tags": "The\\O food\\O was\\O excellent\\O as\\O well\\O as\\O service\\O ,\\O however\\O ,\\O I\\O left\\O The\\B Four\\I Seasons\\I very\\O dissappointed\\O .\\O", "opinion_tags": "The\\O food\\O was\\O excellent\\O as\\O well\\O as\\O service\\O ,\\O however\\O ,\\O I\\O left\\O The\\O Four\\O Seasons\\O very\\O dissappointed\\B .\\O", "sentiment": "negative"}]}, {"id": "Y#4:0", "sentence": "Red Dragon Roll - my favorite thing to eat , of any food group - hands down", "postag": ["NNP", "NNP", "NNP", ",", "PRP$", "JJ", "NN", "TO", "VB", ",", "IN", "DT", "NN", "NN", "HYPH", "NNS", "RB"], "head": [2, 3, 0, 3, 7, 7, 3, 9, 7, 3, 16, 16, 16, 16, 16, 9, 9], "deprel": ["compound", "compound", "root", "punct", "nmod:poss", "amod", "parataxis", "mark", "acl", "punct", "case", "det", "compound", "compound", "punct", "obl", "advmod"], "triples": [{"uid": "Y#4:0-0", "target_tags": "Red\\B Dragon\\I Roll\\I -\\O my\\O favorite\\O thing\\O to\\O eat\\O ,\\O of\\O any\\O food\\O group\\O -\\O hands\\O down\\O", "opinion_tags": "Red\\O Dragon\\O Roll\\O -\\O my\\O favorite\\B thing\\O to\\O eat\\O ,\\O of\\O any\\O food\\O group\\O -\\O hands\\O down\\O", "sentiment": "positive"}]}, {"id": "Y#4:3", "sentence": "The Seafood Dynamite is also otherworldly .", "postag": ["DT", "NNP", "NNP", "VBZ", "RB", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 6], "deprel": ["det", "compound", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "Y#4:3-0", "target_tags": "The\\O Seafood\\B Dynamite\\I is\\O also\\O otherworldly\\O .\\O", "opinion_tags": "The\\O Seafood\\O Dynamite\\O is\\O also\\O otherworldly\\B .\\O", "sentiment": "positive"}]}, {"id": "Y#6:0", "sentence": "Favorite Sushi in NYC", "postag": ["JJ", "NN", "IN", "NNP"], "head": [2, 0, 4, 2], "deprel": ["amod", "root", "case", "nmod"], "triples": [{"uid": "Y#6:0-0", "target_tags": "Favorite\\O Sushi\\B in\\O NYC\\O", "opinion_tags": "Favorite\\B Sushi\\O in\\O NYC\\O", "sentiment": "positive"}]}, {"id": "Y#6:2", "sentence": "An unpretentious spot in Park Slope , the sushi is consistently good , the service is pleasant , effective and unassuming .", "postag": ["DT", "JJ", "NN", "IN", "NNP", "NNP", ",", "DT", "NN", "VBZ", "RB", "JJ", ",", "DT", "NN", "VBZ", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [3, 3, 12, 6, 6, 3, 12, 9, 12, 12, 12, 0, 17, 15, 17, 17, 12, 19, 17, 21, 17, 12], "deprel": ["det", "amod", "nsubj", "case", "compound", "nmod", "punct", "det", "nsubj", "cop", "advmod", "root", "punct", "det", "nsubj", "cop", "parataxis", "punct", "conj", "cc", "conj", "punct"], "triples": [{"uid": "Y#6:2-0", "target_tags": "An\\O unpretentious\\O spot\\B in\\O Park\\O Slope\\O ,\\O the\\O sushi\\O is\\O consistently\\O good\\O ,\\O the\\O service\\O is\\O pleasant\\O ,\\O effective\\O and\\O unassuming\\O .\\O", "opinion_tags": "An\\O unpretentious\\B spot\\O in\\O Park\\O Slope\\O ,\\O the\\O sushi\\O is\\O consistently\\O good\\O ,\\O the\\O service\\O is\\O pleasant\\O ,\\O effective\\O and\\O unassuming\\O .\\O", "sentiment": "positive"}, {"uid": "Y#6:2-1", "target_tags": "An\\O unpretentious\\O spot\\O in\\O Park\\O Slope\\O ,\\O the\\O sushi\\B is\\O consistently\\O good\\O ,\\O the\\O service\\O is\\O pleasant\\O ,\\O effective\\O and\\O unassuming\\O .\\O", "opinion_tags": "An\\O unpretentious\\O spot\\O in\\O Park\\O Slope\\O ,\\O the\\O sushi\\O is\\O consistently\\O good\\B ,\\O the\\O service\\O is\\O pleasant\\O ,\\O effective\\O and\\O unassuming\\O .\\O", "sentiment": "positive"}, {"uid": "Y#6:2-2", "target_tags": "An\\O unpretentious\\O spot\\O in\\O Park\\O Slope\\O ,\\O the\\O sushi\\O is\\O consistently\\O good\\O ,\\O the\\O service\\B is\\O pleasant\\O ,\\O effective\\O and\\O unassuming\\O .\\O", "opinion_tags": "An\\O unpretentious\\O spot\\O in\\O Park\\O Slope\\O ,\\O the\\O sushi\\O is\\O consistently\\O good\\O ,\\O the\\O service\\O is\\O pleasant\\B ,\\O effective\\B and\\O unassuming\\B .\\O", "sentiment": "positive"}]}, {"id": "Y#6:3", "sentence": "In the summer months , the back garden area is really nice .", "postag": ["IN", "DT", "NN", "NNS", ",", "DT", "JJ", "NN", "NN", "VBZ", "RB", "JJ", "."], "head": [4, 4, 4, 12, 12, 9, 9, 9, 12, 12, 12, 0, 12], "deprel": ["case", "det", "compound", "obl", "punct", "det", "amod", "compound", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "Y#6:3-0", "target_tags": "In\\O the\\O summer\\O months\\O ,\\O the\\O back\\B garden\\I area\\I is\\O really\\O nice\\O .\\O", "opinion_tags": "In\\O the\\O summer\\O months\\O ,\\O the\\O back\\O garden\\O area\\O is\\O really\\O nice\\B .\\O", "sentiment": "positive"}]}, {"id": "Y#6:4", "sentence": "The rolls are creative and I have yet to find another sushi place that serves up more inventive yet delicious japanese food .", "postag": ["DT", "NNS", "VBP", "JJ", "CC", "PRP", "VBP", "RB", "TO", "VB", "DT", "NN", "NN", "WDT", "VBZ", "RP", "RBR", "JJ", "CC", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 7, 10, 7, 13, 13, 10, 15, 13, 15, 18, 22, 20, 18, 22, 15, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "conj", "advmod", "mark", "xcomp", "det", "compound", "obj", "nsubj", "acl:relcl", "compound:prt", "advmod", "amod", "cc", "conj", "amod", "obj", "punct"], "triples": [{"uid": "Y#6:4-0", "target_tags": "The\\O rolls\\B are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\O japanese\\O food\\O .\\O", "opinion_tags": "The\\O rolls\\O are\\O creative\\B and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\O japanese\\O food\\O .\\O", "sentiment": "positive"}, {"uid": "Y#6:4-1", "target_tags": "The\\O rolls\\O are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\O yet\\O delicious\\O japanese\\B food\\I .\\O", "opinion_tags": "The\\O rolls\\O are\\O creative\\O and\\O I\\O have\\O yet\\O to\\O find\\O another\\O sushi\\O place\\O that\\O serves\\O up\\O more\\O inventive\\B yet\\O delicious\\B japanese\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#6:5", "sentence": "The Dancing , White River and Millenium rolls are musts .", "postag": ["DT", "NN", ",", "NNP", "NNP", "CC", "NNP", "NNS", "VBP", "NNS", "."], "head": [2, 10, 5, 5, 2, 8, 8, 2, 10, 0, 10], "deprel": ["det", "nsubj", "punct", "compound", "conj", "cc", "compound", "conj", "cop", "root", "punct"], "triples": [{"uid": "Y#6:5-0", "target_tags": "The\\O Dancing\\B ,\\I White\\I River\\I and\\I Millenium\\I rolls\\I are\\O musts\\O .\\O", "opinion_tags": "The\\O Dancing\\O ,\\O White\\O River\\O and\\O Millenium\\O rolls\\O are\\O musts\\B .\\O", "sentiment": "positive"}]}, {"id": "RL#4:1", "sentence": "I CAN EAT HERE EVERY DAY OF THE WEEK REALLY LOL LOVE THIS PLACE ... )", "postag": ["PRP", "MD", "VB", "RB", "DT", "NN", "IN", "DT", "NN", "RB", "UH", "VB", "DT", "NN", ".", "-RRB-"], "head": [3, 3, 0, 3, 6, 3, 9, 9, 6, 12, 12, 3, 14, 12, 3, 3], "deprel": ["nsubj", "aux", "root", "advmod", "det", "obl:tmod", "case", "det", "nmod", "advmod", "discourse", "parataxis", "det", "obj", "punct", "punct"], "triples": [{"uid": "RL#4:1-0", "target_tags": "I\\O CAN\\O EAT\\O HERE\\O EVERY\\O DAY\\O OF\\O THE\\O WEEK\\O REALLY\\O LOL\\O LOVE\\O THIS\\O PLACE\\B ...\\O )\\O", "opinion_tags": "I\\O CAN\\O EAT\\O HERE\\O EVERY\\O DAY\\O OF\\O THE\\O WEEK\\O REALLY\\O LOL\\O LOVE\\B THIS\\O PLACE\\O ...\\O )\\O", "sentiment": "positive"}]}, {"id": "P#10:0", "sentence": "Gross food – Wow-", "postag": ["JJ", "NN", ":", "NNP"], "head": [2, 0, 2, 2], "deprel": ["amod", "root", "punct", "appos"], "triples": [{"uid": "P#10:0-0", "target_tags": "Gross\\O food\\B –\\O Wow-\\O", "opinion_tags": "Gross\\B food\\O –\\O Wow-\\O", "sentiment": "negative"}]}, {"id": "P#10:3", "sentence": "And $ 11 for a plate of bland guacamole ?", "postag": ["CC", "$", "CD", "IN", "DT", "NN", "IN", "JJ", "NN", "."], "head": [2, 0, 2, 6, 6, 2, 9, 9, 6, 2], "deprel": ["cc", "root", "nummod", "case", "det", "nmod", "case", "amod", "nmod", "punct"], "triples": [{"uid": "P#10:3-0", "target_tags": "And\\O $\\O 11\\O for\\O a\\O plate\\O of\\O bland\\O guacamole\\B ?\\O", "opinion_tags": "And\\O $\\O 11\\O for\\O a\\O plate\\O of\\O bland\\B guacamole\\O ?\\O", "sentiment": "negative"}]}, {"id": "P#10:9", "sentence": "Oh , and I never write reviews -- I just was so moved by how bad this place was , I felt it was my duty to spread the word .", "postag": ["UH", ",", "CC", "PRP", "RB", "VBP", "NNS", ",", "PRP", "RB", "VBD", "RB", "VBN", "IN", "WRB", "JJ", "DT", "NN", "VBD", ",", "PRP", "VBD", "PRP", "VBD", "PRP$", "NN", "TO", "VB", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 6, 6, 13, 13, 13, 13, 6, 16, 16, 13, 18, 16, 16, 22, 22, 6, 26, 26, 26, 22, 28, 26, 30, 28, 22], "deprel": ["discourse", "punct", "cc", "nsubj", "advmod", "root", "obj", "punct", "nsubj:pass", "advmod", "aux:pass", "advmod", "parataxis", "mark", "mark", "advcl", "det", "nsubj", "cop", "punct", "nsubj", "parataxis", "nsubj", "cop", "nmod:poss", "ccomp", "mark", "acl", "det", "obj", "punct"], "triples": [{"uid": "P#10:9-0", "target_tags": "Oh\\O ,\\O and\\O I\\O never\\O write\\O reviews\\O --\\O I\\O just\\O was\\O so\\O moved\\O by\\O how\\O bad\\O this\\O place\\B was\\O ,\\O I\\O felt\\O it\\O was\\O my\\O duty\\O to\\O spread\\O the\\O word\\O .\\O", "opinion_tags": "Oh\\O ,\\O and\\O I\\O never\\O write\\O reviews\\O --\\O I\\O just\\O was\\O so\\O moved\\O by\\O how\\O bad\\B this\\O place\\O was\\O ,\\O I\\O felt\\O it\\O was\\O my\\O duty\\O to\\O spread\\O the\\O word\\O .\\O", "sentiment": "negative"}]}, {"id": "TM#3:0", "sentence": "Great Indian Food !", "postag": ["JJ", "JJ", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"], "triples": [{"uid": "TM#3:0-0", "target_tags": "Great\\O Indian\\B Food\\I !\\O", "opinion_tags": "Great\\B Indian\\O Food\\O !\\O", "sentiment": "positive"}]}, {"id": "TM#3:3", "sentence": "The food was good , the place was clean and affordable .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 4, 7, 9, 9, 4, 11, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "parataxis", "cc", "conj", "punct"], "triples": [{"uid": "TM#3:3-0", "target_tags": "The\\O food\\B was\\O good\\O ,\\O the\\O place\\O was\\O clean\\O and\\O affordable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O good\\B ,\\O the\\O place\\O was\\O clean\\O and\\O affordable\\O .\\O", "sentiment": "positive"}, {"uid": "TM#3:3-1", "target_tags": "The\\O food\\O was\\O good\\O ,\\O the\\O place\\B was\\O clean\\O and\\O affordable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O good\\O ,\\O the\\O place\\O was\\O clean\\B and\\O affordable\\B .\\O", "sentiment": "positive"}]}, {"id": "TM#3:4", "sentence": "I noticed alot of indian people eatting there which is a great sign for an indian place !", "postag": ["PRP", "VBD", "NN", "IN", "JJ", "NNS", "VBG", "RB", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 2, 6, 6, 3, 6, 7, 13, 13, 13, 13, 6, 17, 17, 17, 13, 2], "deprel": ["nsubj", "root", "obj", "case", "amod", "nmod", "acl", "advmod", "nsubj", "cop", "det", "amod", "acl:relcl", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "TM#3:4-0", "target_tags": "I\\O noticed\\O alot\\O of\\O indian\\O people\\O eatting\\O there\\O which\\O is\\O a\\O great\\O sign\\O for\\O an\\O indian\\B place\\I !\\O", "opinion_tags": "I\\O noticed\\O alot\\O of\\O indian\\O people\\O eatting\\O there\\O which\\O is\\O a\\O great\\B sign\\O for\\O an\\O indian\\O place\\O !\\O", "sentiment": "positive"}]}, {"id": "TR#1:0", "sentence": "This is one of my favorite spot , very relaxing the food is great all the times , celebrated my engagement and my wedding here , it was very well organized .", "postag": ["DT", "VBZ", "CD", "IN", "PRP$", "JJ", "NN", ",", "RB", "VBG", "DT", "NN", "VBZ", "JJ", "PDT", "DT", "NNS", ",", "VBD", "PRP$", "NN", "CC", "PRP$", "NN", "RB", ",", "PRP", "VBD", "RB", "RB", "VBN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3, 10, 14, 12, 14, 14, 3, 17, 17, 14, 19, 3, 21, 19, 24, 24, 21, 19, 3, 31, 31, 30, 31, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "nmod:poss", "amod", "nmod", "punct", "advmod", "csubj", "det", "nsubj", "cop", "parataxis", "det:predet", "det", "obl:tmod", "punct", "parataxis", "nmod:poss", "obj", "cc", "nmod:poss", "conj", "advmod", "punct", "nsubj:pass", "aux:pass", "advmod", "advmod", "parataxis", "punct"], "triples": [{"uid": "TR#1:0-0", "target_tags": "This\\O is\\O one\\O of\\O my\\O favorite\\O spot\\O ,\\O very\\O relaxing\\O the\\O food\\B is\\O great\\O all\\O the\\O times\\O ,\\O celebrated\\O my\\O engagement\\O and\\O my\\O wedding\\O here\\O ,\\O it\\O was\\O very\\O well\\O organized\\O .\\O", "opinion_tags": "This\\O is\\O one\\O of\\O my\\O favorite\\O spot\\O ,\\O very\\O relaxing\\O the\\O food\\O is\\O great\\B all\\O the\\O times\\O ,\\O celebrated\\O my\\O engagement\\O and\\O my\\O wedding\\O here\\O ,\\O it\\O was\\O very\\O well\\O organized\\O .\\O", "sentiment": "positive"}]}, {"id": "TR#1:1", "sentence": "The staff is very good .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "TR#1:1-0", "target_tags": "The\\O staff\\B is\\O very\\O good\\O .\\O", "opinion_tags": "The\\O staff\\O is\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "TR#1:2", "sentence": "Love their drink menu .", "postag": ["VBP", "PRP$", "NN", "NN", "."], "head": [0, 4, 4, 1, 1], "deprel": ["root", "nmod:poss", "compound", "obj", "punct"], "triples": [{"uid": "TR#1:2-0", "target_tags": "Love\\O their\\O drink\\B menu\\I .\\O", "opinion_tags": "Love\\B their\\O drink\\O menu\\O .\\O", "sentiment": "positive"}]}, {"id": "TR#1:3", "sentence": "I highly recommend this beautiful place .", "postag": ["PRP", "RB", "VBP", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "obj", "punct"], "triples": [{"uid": "TR#1:3-0", "target_tags": "I\\O highly\\O recommend\\O this\\O beautiful\\O place\\B .\\O", "opinion_tags": "I\\O highly\\O recommend\\B this\\O beautiful\\B place\\O .\\O", "sentiment": "positive"}]}, {"id": "TR#8:6", "sentence": "Nice view of river and NYC .", "postag": ["JJ", "NN", "IN", "NN", "CC", "NNP", "."], "head": [2, 0, 4, 2, 6, 4, 2], "deprel": ["amod", "root", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "TR#8:6-0", "target_tags": "Nice\\O view\\B of\\I river\\I and\\I NYC\\I .\\O", "opinion_tags": "Nice\\B view\\O of\\O river\\O and\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "BG#1:2", "sentence": "Great survice", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "BG#1:2-0", "target_tags": "Great\\O survice\\B", "opinion_tags": "Great\\B survice\\O", "sentiment": "positive"}]}, {"id": "CLF#1:0", "sentence": "A beautifully designed dreamy Egyptian restaurant that gets sceney at night .", "postag": ["DT", "RB", "VBN", "JJ", "JJ", "NN", "WDT", "VBZ", "JJ", "IN", "NN", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 8, 11, 9, 6], "deprel": ["det", "advmod", "amod", "amod", "amod", "root", "nsubj", "acl:relcl", "xcomp", "case", "obl", "punct"], "triples": [{"uid": "CLF#1:0-0", "target_tags": "A\\O beautifully\\O designed\\O dreamy\\O Egyptian\\B restaurant\\I that\\O gets\\O sceney\\O at\\O night\\O .\\O", "opinion_tags": "A\\O beautifully\\O designed\\O dreamy\\B Egyptian\\O restaurant\\O that\\O gets\\O sceney\\O at\\O night\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#1:1", "sentence": "Watch the talented belly dancers as you enjoy delicious baba ganoush that 's more lemony than smoky .", "postag": ["VB", "DT", "JJ", "NN", "NNS", "IN", "PRP", "VBP", "JJ", "NN", "NN", "WDT", "VBZ", "RBR", "JJ", "IN", "JJ", "."], "head": [0, 5, 5, 5, 1, 8, 8, 1, 11, 11, 8, 15, 15, 15, 11, 17, 15, 1], "deprel": ["root", "det", "amod", "compound", "obj", "mark", "nsubj", "advcl", "amod", "compound", "obj", "nsubj", "cop", "advmod", "acl:relcl", "case", "obl", "punct"], "triples": [{"uid": "CLF#1:1-0", "target_tags": "Watch\\O the\\O talented\\O belly\\O dancers\\O as\\O you\\O enjoy\\O delicious\\O baba\\B ganoush\\I that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O", "opinion_tags": "Watch\\O the\\O talented\\O belly\\O dancers\\O as\\O you\\O enjoy\\B delicious\\B baba\\O ganoush\\O that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#1:1-1", "target_tags": "Watch\\O the\\O talented\\O belly\\B dancers\\I as\\O you\\O enjoy\\O delicious\\O baba\\O ganoush\\O that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O", "opinion_tags": "Watch\\O the\\O talented\\B belly\\O dancers\\O as\\O you\\O enjoy\\O delicious\\O baba\\O ganoush\\O that\\O 's\\O more\\O lemony\\O than\\O smoky\\O .\\O", "sentiment": "positive"}]}, {"id": "P#9:0", "sentence": "<PERSON> the bartender rocks !", "postag": ["NNP", "DT", "NN", "VBZ", "."], "head": [4, 3, 4, 0, 4], "deprel": ["nsubj", "det", "nsubj", "root", "punct"], "triples": [{"uid": "P#9:0-0", "target_tags": "Raymond\\B the\\O bartender\\O rocks\\O !\\O", "opinion_tags": "Raymond\\O the\\O bartender\\O rocks\\B !\\O", "sentiment": "positive"}]}, {"id": "P#9:1", "sentence": "Pacifico is a great place to casually hang out .", "postag": ["NNP", "VBZ", "DT", "JJ", "NN", "TO", "RB", "VB", "RP", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 8, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "advmod", "acl", "compound:prt", "punct"], "triples": [{"uid": "P#9:1-0", "target_tags": "Pacifico\\B is\\O a\\O great\\O place\\O to\\O casually\\O hang\\O out\\O .\\O", "opinion_tags": "Pacifico\\O is\\O a\\O great\\B place\\O to\\O casually\\O hang\\O out\\O .\\O", "sentiment": "positive"}]}, {"id": "P#9:2", "sentence": "The drinks are great , especially when made by <PERSON> .", "postag": ["DT", "NNS", "VBP", "JJ", ",", "RB", "WRB", "VBN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 10, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "mark", "advcl", "case", "obl", "punct"], "triples": [{"uid": "P#9:2-0", "target_tags": "The\\O drinks\\B are\\O great\\O ,\\O especially\\O when\\O made\\O by\\O Raymond\\O .\\O", "opinion_tags": "The\\O drinks\\O are\\O great\\B ,\\O especially\\O when\\O made\\O by\\O Raymond\\O .\\O", "sentiment": "positive"}, {"uid": "P#9:2-1", "target_tags": "The\\O drinks\\O are\\O great\\O ,\\O especially\\O when\\O made\\O by\\O Raymond\\B .\\O", "opinion_tags": "The\\O drinks\\O are\\O great\\B ,\\O especially\\O when\\O made\\O by\\O Raymond\\O .\\O", "sentiment": "positive"}]}, {"id": "P#9:3", "sentence": "The omlette for brunch is great ...", "postag": ["DT", "NN", "IN", "NN", "VBZ", "JJ", "."], "head": [2, 6, 4, 2, 6, 0, 6], "deprel": ["det", "nsubj", "case", "nmod", "cop", "root", "punct"], "triples": [{"uid": "P#9:3-0", "target_tags": "The\\O omlette\\B for\\I brunch\\I is\\O great\\O ...\\O", "opinion_tags": "The\\O omlette\\O for\\O brunch\\O is\\O great\\B ...\\O", "sentiment": "positive"}]}, {"id": "P#9:4", "sentence": "the spinach is fresh , definately not frozen ...", "postag": ["DT", "NN", "VBZ", "JJ", ",", "RB", "RB", "JJ", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "P#9:4-0", "target_tags": "the\\O spinach\\B is\\O fresh\\O ,\\O definately\\O not\\O frozen\\O ...\\O", "opinion_tags": "the\\O spinach\\O is\\O fresh\\B ,\\O definately\\O not\\O frozen\\O ...\\O", "sentiment": "positive"}]}, {"id": "P#9:5", "sentence": "quacamole at pacifico is yummy , as are the wings with chimmichuri .", "postag": ["NN", "IN", "NN", "VBZ", "JJ", ",", "IN", "VBP", "DT", "NNS", "IN", "NN", "."], "head": [5, 3, 1, 5, 0, 5, 10, 10, 10, 5, 12, 10, 5], "deprel": ["nsubj", "case", "nmod", "cop", "root", "punct", "mark", "cop", "det", "advcl", "case", "nmod", "punct"], "triples": [{"uid": "P#9:5-0", "target_tags": "quacamole\\B at\\O pacifico\\O is\\O yummy\\O ,\\O as\\O are\\O the\\O wings\\O with\\O chimmichuri\\O .\\O", "opinion_tags": "quacamole\\O at\\O pacifico\\O is\\O yummy\\B ,\\O as\\O are\\O the\\O wings\\O with\\O chimmichuri\\O .\\O", "sentiment": "positive"}, {"uid": "P#9:5-1", "target_tags": "quacamole\\O at\\O pacifico\\O is\\O yummy\\O ,\\O as\\O are\\O the\\O wings\\B with\\I chimmichuri\\I .\\O", "opinion_tags": "quacamole\\O at\\O pacifico\\O is\\O yummy\\B ,\\O as\\O are\\O the\\O wings\\O with\\O chimmichuri\\O .\\O", "sentiment": "positive"}]}, {"id": "P#9:6", "sentence": "A weakness is the chicken in the salads .", "postag": ["DT", "NN", "VBZ", "DT", "NN", "IN", "DT", "NNS", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "case", "det", "nmod", "punct"], "triples": [{"uid": "P#9:6-0", "target_tags": "A\\O weakness\\O is\\O the\\O chicken\\B in\\I the\\I salads\\I .\\O", "opinion_tags": "A\\O weakness\\B is\\O the\\O chicken\\O in\\O the\\O salads\\O .\\O", "sentiment": "negative"}]}, {"id": "P#9:8", "sentence": "Also , I personally was n't a fan of the portobello and asparagus mole .", "postag": ["RB", ",", "PRP", "RB", "VBD", "RB", "DT", "NN", "IN", "DT", "NN", "CC", "NN", "NN", "."], "head": [8, 8, 8, 8, 8, 8, 8, 0, 11, 11, 8, 14, 14, 11, 8], "deprel": ["advmod", "punct", "nsubj", "advmod", "cop", "advmod", "det", "root", "case", "det", "nmod", "cc", "compound", "conj", "punct"], "triples": [{"uid": "P#9:8-0", "target_tags": "Also\\O ,\\O I\\O personally\\O was\\O n't\\O a\\O fan\\O of\\O the\\O portobello\\B and\\I asparagus\\I mole\\I .\\O", "opinion_tags": "Also\\O ,\\O I\\O personally\\O was\\O n't\\O a\\O fan\\B of\\O the\\O portobello\\O and\\O asparagus\\O mole\\O .\\O", "sentiment": "negative"}]}, {"id": "P#9:9", "sentence": "Overall , decent food at a good price , with friendly people .", "postag": ["RB", ",", "JJ", "NN", "IN", "DT", "JJ", "NN", ",", "IN", "JJ", "NNS", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 4, 12, 12, 4, 4], "deprel": ["advmod", "punct", "amod", "root", "case", "det", "amod", "nmod", "punct", "case", "amod", "nmod", "punct"], "triples": [{"uid": "P#9:9-0", "target_tags": "Overall\\O ,\\O decent\\O food\\B at\\O a\\O good\\O price\\O ,\\O with\\O friendly\\O people\\O .\\O", "opinion_tags": "Overall\\O ,\\O decent\\B food\\O at\\O a\\O good\\O price\\O ,\\O with\\O friendly\\O people\\O .\\O", "sentiment": "positive"}, {"uid": "P#9:9-1", "target_tags": "Overall\\O ,\\O decent\\O food\\O at\\O a\\O good\\O price\\O ,\\O with\\O friendly\\O people\\B .\\O", "opinion_tags": "Overall\\O ,\\O decent\\O food\\O at\\O a\\O good\\O price\\O ,\\O with\\O friendly\\B people\\O .\\O", "sentiment": "positive"}]}, {"id": "BG#6:0", "sentence": "Best Indian Restaurant in the City", "postag": ["JJS", "JJ", "NN", "IN", "DT", "NNP"], "head": [3, 3, 0, 6, 6, 3], "deprel": ["amod", "amod", "root", "case", "det", "nmod"], "triples": [{"uid": "BG#6:0-0", "target_tags": "Best\\O Indian\\B Restaurant\\I in\\O the\\O City\\O", "opinion_tags": "Best\\B Indian\\O Restaurant\\O in\\O the\\O City\\O", "sentiment": "positive"}]}, {"id": "BG#6:1", "sentence": "Decor needs to be upgraded but the food is amazing !", "postag": ["NN", "VBZ", "TO", "VB", "VBN", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 0, 5, 5, 2, 10, 8, 10, 10, 2, 2], "deprel": ["nsubj", "root", "mark", "aux:pass", "xcomp", "cc", "det", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "BG#6:1-0", "target_tags": "Decor\\B needs\\O to\\O be\\O upgraded\\O but\\O the\\O food\\O is\\O amazing\\O !\\O", "opinion_tags": "Decor\\O needs\\O to\\O be\\O upgraded\\B but\\O the\\O food\\O is\\O amazing\\O !\\O", "sentiment": "negative"}, {"uid": "BG#6:1-1", "target_tags": "Decor\\O needs\\O to\\O be\\O upgraded\\O but\\O the\\O food\\B is\\O amazing\\O !\\O", "opinion_tags": "Decor\\O needs\\O to\\O be\\O upgraded\\O but\\O the\\O food\\O is\\O amazing\\B !\\O", "sentiment": "positive"}]}, {"id": "BzG#4:0", "sentence": "This small Astoria souvlaki spot makes what many consider the best gyros in New York .", "postag": ["DT", "JJ", "NNP", "NN", "NN", "VBZ", "WP", "JJ", "VBP", "DT", "JJS", "NNS", "IN", "NNP", "NNP", "."], "head": [5, 5, 5, 5, 6, 0, 9, 9, 6, 12, 12, 9, 15, 15, 12, 6], "deprel": ["det", "amod", "compound", "compound", "nsubj", "root", "obj", "nsubj", "ccomp", "det", "amod", "obj", "case", "compound", "nmod", "punct"], "triples": [{"uid": "BzG#4:0-0", "target_tags": "This\\O small\\O Astoria\\O souvlaki\\O spot\\O makes\\O what\\O many\\O consider\\O the\\O best\\O gyros\\B in\\O New\\O York\\O .\\O", "opinion_tags": "This\\O small\\O Astoria\\O souvlaki\\O spot\\O makes\\O what\\O many\\O consider\\O the\\O best\\B gyros\\O in\\O New\\O York\\O .\\O", "sentiment": "positive"}]}, {"id": "BzG#4:2", "sentence": "What really makes it shine is the food , which is aggressively seasoned with Cyrpriot spices , and all made in-house ( even the gyro meat and sausages ) , and made of much higher quality ingredients that might otherwise be expected .", "postag": ["WP", "RB", "VBZ", "PRP", "VB", "VBZ", "DT", "NN", ",", "WDT", "VBZ", "RB", "VBN", "IN", "NNP", "NNS", ",", "CC", "DT", "VBN", "JJ", "-LRB-", "RB", "DT", "NN", "NN", "CC", "NNS", "-RRB-", ",", "CC", "VBN", "IN", "RB", "JJR", "JJ", "NNS", "WDT", "MD", "RB", "VB", "VBN", "."], "head": [3, 3, 8, 3, 3, 8, 8, 0, 8, 13, 13, 13, 8, 16, 16, 13, 21, 19, 21, 8, 20, 26, 26, 26, 26, 21, 28, 26, 26, 32, 32, 13, 37, 35, 37, 37, 32, 42, 42, 42, 42, 37, 8], "deprel": ["nsubj", "advmod", "csubj", "obj", "xcomp", "cop", "det", "root", "punct", "nsubj:pass", "aux:pass", "advmod", "acl:relcl", "case", "compound", "obl", "punct", "cc", "det", "conj", "xcomp", "punct", "advmod", "det", "compound", "appos", "cc", "conj", "punct", "punct", "cc", "conj", "case", "advmod", "amod", "amod", "obl", "nsubj:pass", "aux", "advmod", "aux:pass", "acl:relcl", "punct"], "triples": [{"uid": "BzG#4:2-0", "target_tags": "What\\O really\\O makes\\O it\\O shine\\O is\\O the\\O food\\B ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\O (\\O even\\O the\\O gyro\\O meat\\O and\\O sausages\\O )\\O ,\\O and\\O made\\O of\\O much\\O higher\\O quality\\O ingredients\\O that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "opinion_tags": "What\\O really\\O makes\\O it\\O shine\\B is\\O the\\O food\\O ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\O (\\O even\\O the\\O gyro\\O meat\\O and\\O sausages\\O )\\O ,\\O and\\O made\\O of\\O much\\O higher\\O quality\\O ingredients\\O that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "sentiment": "positive"}, {"uid": "BzG#4:2-1", "target_tags": "What\\O really\\O makes\\O it\\O shine\\O is\\O the\\O food\\O ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\O (\\O even\\O the\\O gyro\\B meat\\I and\\O sausages\\O )\\O ,\\O and\\O made\\O of\\O much\\O higher\\O quality\\O ingredients\\O that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "opinion_tags": "What\\O really\\O makes\\O it\\O shine\\O is\\O the\\O food\\O ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\B (\\O even\\O the\\O gyro\\O meat\\O and\\O sausages\\O )\\O ,\\O and\\O made\\O of\\O much\\O higher\\O quality\\O ingredients\\O that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "sentiment": "positive"}, {"uid": "BzG#4:2-2", "target_tags": "What\\O really\\O makes\\O it\\O shine\\O is\\O the\\O food\\O ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\O (\\O even\\O the\\O gyro\\O meat\\O and\\O sausages\\B )\\O ,\\O and\\O made\\O of\\O much\\O higher\\O quality\\O ingredients\\O that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "opinion_tags": "What\\O really\\O makes\\O it\\O shine\\O is\\O the\\O food\\O ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\B (\\O even\\O the\\O gyro\\O meat\\O and\\O sausages\\O )\\O ,\\O and\\O made\\O of\\O much\\O higher\\O quality\\O ingredients\\O that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "sentiment": "positive"}, {"uid": "BzG#4:2-3", "target_tags": "What\\O really\\O makes\\O it\\O shine\\O is\\O the\\O food\\O ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\O (\\O even\\O the\\O gyro\\O meat\\O and\\O sausages\\O )\\O ,\\O and\\O made\\O of\\O much\\O higher\\O quality\\O ingredients\\B that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "opinion_tags": "What\\O really\\O makes\\O it\\O shine\\O is\\O the\\O food\\O ,\\O which\\O is\\O aggressively\\O seasoned\\O with\\O Cyrpriot\\O spices\\O ,\\O and\\O all\\O made\\O in-house\\O (\\O even\\O the\\O gyro\\O meat\\O and\\O sausages\\O )\\O ,\\O and\\O made\\O of\\O much\\O higher\\B quality\\I ingredients\\O that\\O might\\O otherwise\\O be\\O expected\\O .\\O", "sentiment": "positive"}]}, {"id": "BzG#4:3", "sentence": "All the various Greek and Cypriot dishes are excellent , but the gyro is the reason to come -- if you do n't eat one your trip was wasted .", "postag": ["PDT", "DT", "JJ", "JJ", "CC", "JJ", "NNS", "VBP", "JJ", ",", "CC", "DT", "NN", "VBZ", "DT", "NN", "TO", "VB", ",", "IN", "PRP", "VBP", "RB", "VB", "CD", "PRP$", "NN", "VBD", "VBN", "."], "head": [7, 7, 7, 7, 6, 4, 9, 9, 0, 16, 16, 13, 16, 16, 16, 9, 18, 16, 9, 24, 24, 24, 24, 16, 24, 27, 29, 29, 24, 9], "deprel": ["det:predet", "det", "amod", "amod", "cc", "conj", "nsubj", "cop", "root", "punct", "cc", "det", "nsubj", "cop", "det", "conj", "mark", "acl", "punct", "mark", "nsubj", "aux", "advmod", "advcl", "obj", "nmod:poss", "nsubj:pass", "aux:pass", "ccomp", "punct"], "triples": [{"uid": "BzG#4:3-0", "target_tags": "All\\O the\\O various\\O Greek\\B and\\I Cypriot\\I dishes\\I are\\O excellent\\O ,\\O but\\O the\\O gyro\\O is\\O the\\O reason\\O to\\O come\\O --\\O if\\O you\\O do\\O n't\\O eat\\O one\\O your\\O trip\\O was\\O wasted\\O .\\O", "opinion_tags": "All\\O the\\O various\\O Greek\\O and\\O Cypriot\\O dishes\\O are\\O excellent\\B ,\\O but\\O the\\O gyro\\O is\\O the\\O reason\\O to\\O come\\O --\\O if\\O you\\O do\\O n't\\O eat\\O one\\O your\\O trip\\O was\\O wasted\\O .\\O", "sentiment": "positive"}]}, {"id": "NP#10:0", "sentence": "Best restaurant in Brooklyn", "postag": ["JJS", "NN", "IN", "NNP"], "head": [2, 0, 4, 2], "deprel": ["amod", "root", "case", "nmod"], "triples": [{"uid": "NP#10:0-0", "target_tags": "Best\\O restaurant\\B in\\O Brooklyn\\O", "opinion_tags": "Best\\B restaurant\\O in\\O Brooklyn\\O", "sentiment": "positive"}]}, {"id": "NP#10:2", "sentence": "Great food , amazing service , this place is a class act .", "postag": ["JJ", "NN", ",", "JJ", "NN", ",", "DT", "NN", "VBZ", "DT", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 2, 8, 12, 12, 12, 12, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "det", "nsubj", "cop", "det", "compound", "parataxis", "punct"], "triples": [{"uid": "NP#10:2-0", "target_tags": "Great\\O food\\B ,\\O amazing\\O service\\O ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O", "opinion_tags": "Great\\B food\\O ,\\O amazing\\O service\\O ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O", "sentiment": "positive"}, {"uid": "NP#10:2-1", "target_tags": "Great\\O food\\O ,\\O amazing\\O service\\B ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O", "opinion_tags": "Great\\O food\\O ,\\O amazing\\B service\\O ,\\O this\\O place\\O is\\O a\\O class\\O act\\O .\\O", "sentiment": "positive"}, {"uid": "NP#10:2-2", "target_tags": "Great\\O food\\O ,\\O amazing\\O service\\O ,\\O this\\O place\\B is\\O a\\O class\\O act\\O .\\O", "opinion_tags": "Great\\O food\\O ,\\O amazing\\O service\\O ,\\O this\\O place\\O is\\O a\\O class\\B act\\I .\\O", "sentiment": "positive"}]}, {"id": "NP#10:3", "sentence": "The veal was incredible last night .", "postag": ["DT", "NN", "VBD", "JJ", "JJ", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "amod", "obl:tmod", "punct"], "triples": [{"uid": "NP#10:3-0", "target_tags": "The\\O veal\\B was\\O incredible\\O last\\O night\\O .\\O", "opinion_tags": "The\\O veal\\O was\\O incredible\\B last\\O night\\O .\\O", "sentiment": "positive"}]}, {"id": "NP#10:5", "sentence": "This place is a must visit !", "postag": ["DT", "NN", "VBZ", "DT", "MD", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "NP#10:5-0", "target_tags": "This\\O place\\B is\\O a\\O must\\O visit\\O !\\O", "opinion_tags": "This\\O place\\O is\\O a\\O must\\B visit\\I !\\O", "sentiment": "positive"}]}, {"id": "Z#11:5", "sentence": "The food is all shared so we get to order together and eat together .", "postag": ["DT", "NN", "VBZ", "RB", "VBN", "RB", "PRP", "VBP", "TO", "VB", "RB", "CC", "VB", "RB", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 8, 10, 13, 10, 13, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "advmod", "nsubj", "parataxis", "mark", "xcomp", "advmod", "cc", "conj", "advmod", "punct"], "triples": [{"uid": "Z#11:5-0", "target_tags": "The\\O food\\B is\\O all\\O shared\\O so\\O we\\O get\\O to\\O order\\O together\\O and\\O eat\\O together\\O .\\O", "opinion_tags": "The\\O food\\O is\\O all\\O shared\\B so\\O we\\O get\\O to\\O order\\O together\\O and\\O eat\\O together\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#11:6", "sentence": "I 've enjoyed 99 % of the dishes we 've ordered with the only exceptions being the occasional too-authentic-for-me dish ( I 'm a daring eater but not THAT daring ) .", "postag": ["PRP", "VBP", "VBN", "CD", "NN", "IN", "DT", "NNS", "PRP", "VBP", "VBN", "IN", "DT", "JJ", "NNS", "VBG", "DT", "JJ", "JJ", "NN", "-LRB-", "PRP", "VBP", "DT", "JJ", "NN", "CC", "RB", "RB", "JJ", "-RRB-", "."], "head": [3, 3, 0, 5, 3, 8, 8, 5, 11, 11, 8, 15, 15, 15, 11, 20, 20, 20, 20, 15, 26, 26, 26, 26, 26, 3, 30, 30, 30, 26, 26, 3], "deprel": ["nsubj", "aux", "root", "nummod", "obj", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "case", "det", "amod", "obl", "cop", "det", "amod", "amod", "acl", "punct", "nsubj", "cop", "det", "amod", "parataxis", "cc", "advmod", "advmod", "conj", "punct", "punct"], "triples": [{"uid": "Z#11:6-0", "target_tags": "I\\O 've\\O enjoyed\\O 99\\O %\\O of\\O the\\O dishes\\B we\\O 've\\O ordered\\O with\\O the\\O only\\O exceptions\\O being\\O the\\O occasional\\O too-authentic-for-me\\O dish\\O (\\O I\\O 'm\\O a\\O daring\\O eater\\O but\\O not\\O THAT\\O daring\\O )\\O .\\O", "opinion_tags": "I\\O 've\\O enjoyed\\B 99\\O %\\O of\\O the\\O dishes\\O we\\O 've\\O ordered\\O with\\O the\\O only\\O exceptions\\O being\\O the\\O occasional\\O too-authentic-for-me\\O dish\\O (\\O I\\O 'm\\O a\\O daring\\O eater\\O but\\O not\\O THAT\\O daring\\O )\\O .\\O", "sentiment": "positive"}, {"uid": "Z#11:6-1", "target_tags": "I\\O 've\\O enjoyed\\O 99\\O %\\O of\\O the\\O dishes\\O we\\O 've\\O ordered\\O with\\O the\\O only\\O exceptions\\O being\\O the\\O occasional\\O too-authentic-for-me\\O dish\\B (\\O I\\O 'm\\O a\\O daring\\O eater\\O but\\O not\\O THAT\\O daring\\O )\\O .\\O", "opinion_tags": "I\\O 've\\O enjoyed\\O 99\\O %\\O of\\O the\\O dishes\\O we\\O 've\\O ordered\\O with\\O the\\O only\\O exceptions\\O being\\O the\\O occasional\\O too-authentic-for-me\\B dish\\O (\\O I\\O 'm\\O a\\O daring\\O eater\\O but\\O not\\O THAT\\O daring\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "WE#4:1", "sentence": "My daughter 's wedding reception at Water 's Edge received the highest compliments from our guests .", "postag": ["PRP$", "NN", "POS", "NN", "NN", "IN", "NNP", "POS", "NNP", "VBD", "DT", "JJS", "NNS", "IN", "PRP$", "NNS", "."], "head": [2, 5, 2, 5, 10, 9, 9, 7, 5, 0, 13, 13, 10, 16, 16, 10, 10], "deprel": ["nmod:poss", "nmod:poss", "case", "compound", "nsubj", "case", "nmod:poss", "case", "nmod", "root", "det", "amod", "obj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "WE#4:1-0", "target_tags": "My\\O daughter\\O 's\\O wedding\\O reception\\O at\\O Water\\B 's\\I Edge\\I received\\O the\\O highest\\O compliments\\O from\\O our\\O guests\\O .\\O", "opinion_tags": "My\\O daughter\\O 's\\O wedding\\O reception\\O at\\O Water\\O 's\\O Edge\\O received\\O the\\O highest\\B compliments\\I from\\O our\\O guests\\O .\\O", "sentiment": "positive"}]}, {"id": "WE#4:2", "sentence": "Everyone raved about the atmosphere ( elegant rooms and absolutely incomparable views ) and the fabulous food !", "postag": ["NN", "VBD", "IN", "DT", "NN", "-LRB-", "JJ", "NNS", "CC", "RB", "JJ", "NNS", "-RRB-", "CC", "DT", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 12, 12, 12, 8, 8, 17, 17, 17, 5, 2], "deprel": ["nsubj", "root", "case", "det", "obl", "punct", "amod", "appos", "cc", "advmod", "amod", "conj", "punct", "cc", "det", "amod", "conj", "punct"], "triples": [{"uid": "WE#4:2-0", "target_tags": "Everyone\\O raved\\O about\\O the\\O atmosphere\\B (\\O elegant\\O rooms\\O and\\O absolutely\\O incomparable\\O views\\O )\\O and\\O the\\O fabulous\\O food\\O !\\O", "opinion_tags": "Everyone\\O raved\\B about\\O the\\O atmosphere\\O (\\O elegant\\O rooms\\O and\\O absolutely\\O incomparable\\O views\\O )\\O and\\O the\\O fabulous\\O food\\O !\\O", "sentiment": "positive"}, {"uid": "WE#4:2-1", "target_tags": "Everyone\\O raved\\O about\\O the\\O atmosphere\\O (\\O elegant\\O rooms\\B and\\O absolutely\\O incomparable\\O views\\O )\\O and\\O the\\O fabulous\\O food\\O !\\O", "opinion_tags": "Everyone\\O raved\\O about\\O the\\O atmosphere\\O (\\O elegant\\B rooms\\O and\\O absolutely\\O incomparable\\O views\\O )\\O and\\O the\\O fabulous\\O food\\O !\\O", "sentiment": "positive"}, {"uid": "WE#4:2-2", "target_tags": "Everyone\\O raved\\O about\\O the\\O atmosphere\\O (\\O elegant\\O rooms\\O and\\O absolutely\\O incomparable\\O views\\B )\\O and\\O the\\O fabulous\\O food\\O !\\O", "opinion_tags": "Everyone\\O raved\\O about\\O the\\O atmosphere\\O (\\O elegant\\O rooms\\O and\\O absolutely\\O incomparable\\B views\\O )\\O and\\O the\\O fabulous\\O food\\O !\\O", "sentiment": "positive"}, {"uid": "WE#4:2-3", "target_tags": "Everyone\\O raved\\O about\\O the\\O atmosphere\\O (\\O elegant\\O rooms\\O and\\O absolutely\\O incomparable\\O views\\O )\\O and\\O the\\O fabulous\\O food\\B !\\O", "opinion_tags": "Everyone\\O raved\\O about\\O the\\O atmosphere\\O (\\O elegant\\O rooms\\O and\\O absolutely\\O incomparable\\O views\\O )\\O and\\O the\\O fabulous\\B food\\O !\\O", "sentiment": "positive"}]}, {"id": "WE#4:3", "sentence": "Service was wonderful ;", "postag": ["NN", "VBD", "JJ", ":"], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"], "triples": [{"uid": "WE#4:3-0", "target_tags": "Service\\B was\\O wonderful\\O ;\\O", "opinion_tags": "Service\\O was\\O wonderful\\B ;\\O", "sentiment": "positive"}]}, {"id": "WE#4:5", "sentence": "<PERSON> , the maitre d ' , was totally professional and always on top of things .", "postag": ["NNP", ",", "DT", "NN", "NNP", "''", ",", "VBD", "RB", "JJ", "CC", "RB", "IN", "NN", "IN", "NNS", "."], "head": [10, 5, 5, 5, 1, 5, 10, 10, 10, 0, 14, 14, 14, 10, 16, 14, 10], "deprel": ["nsubj", "punct", "det", "compound", "appos", "punct", "punct", "cop", "advmod", "root", "cc", "advmod", "case", "obl", "case", "nmod", "punct"], "triples": [{"uid": "WE#4:5-0", "target_tags": "Paul\\B ,\\O the\\O maitre\\O d\\O '\\O ,\\O was\\O totally\\O professional\\O and\\O always\\O on\\O top\\O of\\O things\\O .\\O", "opinion_tags": "Paul\\O ,\\O the\\O maitre\\O d\\O '\\O ,\\O was\\O totally\\O professional\\B and\\O always\\O on\\O top\\O of\\O things\\O .\\O", "sentiment": "positive"}]}, {"id": "TR#2:1", "sentence": "Service ok but unfriendly , filthy bathroom .", "postag": ["NN", "JJ", "CC", "JJ", ",", "JJ", "NN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 2], "deprel": ["nsubj", "root", "cc", "amod", "punct", "amod", "conj", "punct"], "triples": [{"uid": "TR#2:1-0", "target_tags": "Service\\B ok\\O but\\O unfriendly\\O ,\\O filthy\\O bathroom\\O .\\O", "opinion_tags": "Service\\O ok\\B but\\O unfriendly\\B ,\\O filthy\\O bathroom\\O .\\O", "sentiment": "negative"}, {"uid": "TR#2:1-1", "target_tags": "Service\\O ok\\O but\\O unfriendly\\O ,\\O filthy\\O bathroom\\B .\\O", "opinion_tags": "Service\\O ok\\O but\\O unfriendly\\O ,\\O filthy\\B bathroom\\O .\\O", "sentiment": "negative"}]}, {"id": "TR#2:3", "sentence": "The bar drinks were Eh , ok to say the least .", "postag": ["DT", "NN", "NNS", "VBD", "UH", ",", "JJ", "TO", "VB", "DT", "JJS", "."], "head": [3, 3, 5, 7, 7, 7, 0, 9, 7, 11, 9, 7], "deprel": ["det", "compound", "nsubj", "cop", "discourse", "punct", "root", "mark", "advcl", "det", "obj", "punct"], "triples": [{"uid": "TR#2:3-0", "target_tags": "The\\O bar\\B drinks\\I were\\O Eh\\O ,\\O ok\\O to\\O say\\O the\\O least\\O .\\O", "opinion_tags": "The\\O bar\\O drinks\\O were\\O Eh\\O ,\\O ok\\B to\\O say\\O the\\O least\\O .\\O", "sentiment": "negative"}]}, {"id": "TR#2:4", "sentence": "The stuff tilapia was horrid ... tasted like cardboard .", "postag": ["DT", "NN", "NN", "VBD", "JJ", ",", "VBD", "IN", "NN", "."], "head": [3, 3, 5, 5, 0, 5, 5, 9, 7, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "parataxis", "case", "obl", "punct"], "triples": [{"uid": "TR#2:4-0", "target_tags": "The\\O stuff\\B tilapia\\I was\\O horrid\\O ...\\O tasted\\O like\\O cardboard\\O .\\O", "opinion_tags": "The\\O stuff\\O tilapia\\O was\\O horrid\\B ...\\O tasted\\O like\\O cardboard\\O .\\O", "sentiment": "negative"}]}, {"id": "TR#2:7", "sentence": "oh speaking of bathroom , the mens bathroom was disgusting .", "postag": ["UH", "VBG", "IN", "NN", ",", "DT", "NN", "NN", "VBD", "JJ", "."], "head": [2, 10, 4, 2, 10, 8, 8, 10, 10, 0, 10], "deprel": ["discourse", "advcl", "case", "obl", "punct", "det", "compound", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "TR#2:7-0", "target_tags": "oh\\O speaking\\O of\\O bathroom\\O ,\\O the\\O mens\\B bathroom\\I was\\O disgusting\\O .\\O", "opinion_tags": "oh\\O speaking\\O of\\O bathroom\\O ,\\O the\\O mens\\O bathroom\\O was\\O disgusting\\B .\\O", "sentiment": "negative"}]}, {"id": "TFS#10:2", "sentence": "The wine list was extensive - though the staff did not seem knowledgeable about wine pairings .", "postag": ["DT", "NN", "NN", "VBD", "JJ", ",", "IN", "DT", "NN", "VBD", "RB", "VB", "JJ", "IN", "NN", "NNS", "."], "head": [3, 3, 5, 5, 0, 5, 12, 9, 12, 12, 12, 5, 12, 16, 16, 13, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "mark", "det", "nsubj", "aux", "advmod", "advcl", "xcomp", "case", "compound", "obl", "punct"], "triples": [{"uid": "TFS#10:2-0", "target_tags": "The\\O wine\\B list\\I was\\O extensive\\O -\\O though\\O the\\O staff\\O did\\O not\\O seem\\O knowledgeable\\O about\\O wine\\O pairings\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O was\\O extensive\\B -\\O though\\O the\\O staff\\O did\\O not\\O seem\\O knowledgeable\\O about\\O wine\\O pairings\\O .\\O", "sentiment": "positive"}, {"uid": "TFS#10:2-1", "target_tags": "The\\O wine\\O list\\O was\\O extensive\\O -\\O though\\O the\\O staff\\B did\\O not\\O seem\\O knowledgeable\\O about\\O wine\\O pairings\\O .\\O", "opinion_tags": "The\\O wine\\O list\\O was\\O extensive\\O -\\O though\\O the\\O staff\\O did\\O not\\B seem\\I knowledgeable\\I about\\O wine\\O pairings\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#10:3", "sentence": "The bread we received was horrible - rock hard and cold - and the `` free '' appetizer of olives was disappointing .", "postag": ["DT", "NN", "PRP", "VBD", "VBD", "JJ", ",", "NN", "JJ", "CC", "JJ", ",", "CC", "DT", "``", "JJ", "''", "NN", "IN", "NNS", "VBD", "JJ", "."], "head": [2, 6, 4, 2, 6, 0, 6, 9, 6, 11, 9, 6, 22, 18, 18, 18, 18, 22, 20, 18, 22, 6, 6], "deprel": ["det", "nsubj", "nsubj", "acl:relcl", "cop", "root", "punct", "nsubj", "parataxis", "cc", "conj", "punct", "cc", "det", "punct", "amod", "punct", "nsubj", "case", "nmod", "cop", "conj", "punct"], "triples": [{"uid": "TFS#10:3-0", "target_tags": "The\\O bread\\B we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O", "opinion_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\B -\\O rock\\B hard\\I and\\O cold\\B -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\O .\\O", "sentiment": "negative"}, {"uid": "TFS#10:3-1", "target_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\B of\\I olives\\I was\\O disappointing\\O .\\O", "opinion_tags": "The\\O bread\\O we\\O received\\O was\\O horrible\\O -\\O rock\\O hard\\O and\\O cold\\O -\\O and\\O the\\O ``\\O free\\O ''\\O appetizer\\O of\\O olives\\O was\\O disappointing\\B .\\O", "sentiment": "neutral"}]}, {"id": "TFS#10:4", "sentence": "However , our main course was wonderful .", "postag": ["RB", ",", "PRP$", "JJ", "NN", "VBD", "JJ", "."], "head": [7, 7, 5, 5, 7, 7, 0, 7], "deprel": ["advmod", "punct", "nmod:poss", "amod", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "TFS#10:4-0", "target_tags": "However\\O ,\\O our\\O main\\B course\\I was\\O wonderful\\O .\\O", "opinion_tags": "However\\O ,\\O our\\O main\\O course\\O was\\O wonderful\\B .\\O", "sentiment": "positive"}]}, {"id": "TFS#10:5", "sentence": "I had fish and my husband had the filet - both of which exceeded our expectations .", "postag": ["PRP", "VBD", "NN", "CC", "PRP$", "NN", "VBD", "DT", "NN", ",", "DT", "IN", "WDT", "VBD", "PRP$", "NNS", "."], "head": [2, 0, 2, 6, 6, 3, 2, 9, 7, 9, 14, 13, 11, 9, 16, 14, 2], "deprel": ["nsubj", "root", "obj", "cc", "nmod:poss", "conj", "conj", "det", "obj", "punct", "nsubj", "case", "nmod", "acl:relcl", "nmod:poss", "obj", "punct"], "triples": [{"uid": "TFS#10:5-0", "target_tags": "I\\<PERSON> had\\O fish\\B and\\O my\\O husband\\O had\\O the\\O filet\\O -\\O both\\O of\\O which\\O exceeded\\O our\\O expectations\\O .\\O", "opinion_tags": "I\\<PERSON> had\\O fish\\O and\\O my\\O husband\\O had\\O the\\O filet\\O -\\O both\\O of\\O which\\O exceeded\\B our\\I expectations\\I .\\O", "sentiment": "positive"}, {"uid": "TFS#10:5-1", "target_tags": "I\\<PERSON> had\\O fish\\O and\\O my\\O husband\\O had\\O the\\O filet\\B -\\O both\\O of\\O which\\O exceeded\\O our\\O expectations\\O .\\O", "opinion_tags": "I\\<PERSON> had\\O fish\\O and\\O my\\O husband\\O had\\O the\\O filet\\O -\\O both\\O of\\O which\\O exceeded\\B our\\I expectations\\I .\\O", "sentiment": "positive"}]}, {"id": "TFS#10:6", "sentence": "The dessert ( we had a pear torte ) was good - but , once again , the staff was unable to provide appropriate drink suggestions .", "postag": ["DT", "NN", "-LRB-", "PRP", "VBD", "DT", "JJ", "NN", "-RRB-", "VBD", "JJ", ",", "CC", ",", "RB", "RB", ",", "DT", "NN", "VBD", "JJ", "TO", "VB", "JJ", "NN", "NNS", "."], "head": [2, 11, 5, 5, 2, 8, 8, 5, 5, 11, 0, 11, 21, 21, 16, 21, 16, 19, 21, 21, 11, 23, 21, 26, 26, 23, 11], "deprel": ["det", "nsubj", "punct", "nsubj", "parataxis", "det", "amod", "obj", "punct", "cop", "root", "punct", "cc", "punct", "advmod", "advmod", "punct", "det", "nsubj", "cop", "conj", "mark", "xcomp", "amod", "compound", "obj", "punct"], "triples": [{"uid": "TFS#10:6-0", "target_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\B torte\\I )\\O was\\O good\\O -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\O was\\O unable\\O to\\O provide\\O appropriate\\O drink\\O suggestions\\O .\\O", "opinion_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\O torte\\O )\\O was\\O good\\B -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\O was\\O unable\\O to\\O provide\\O appropriate\\O drink\\O suggestions\\O .\\O", "sentiment": "positive"}, {"uid": "TFS#10:6-1", "target_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\O torte\\O )\\O was\\O good\\O -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\B was\\O unable\\O to\\O provide\\O appropriate\\O drink\\O suggestions\\O .\\O", "opinion_tags": "The\\O dessert\\O (\\O we\\O had\\O a\\O pear\\O torte\\O )\\O was\\O good\\O -\\O but\\O ,\\O once\\O again\\O ,\\O the\\O staff\\O was\\O unable\\B to\\I provide\\I appropriate\\O drink\\O suggestions\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#10:8", "sentence": "Not what I would expect for the price and prestige of this location .", "postag": ["RB", "WP", "PRP", "MD", "VB", "IN", "DT", "NN", "CC", "NN", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 8, 13, 13, 8, 1], "deprel": ["advmod", "obj", "nsubj", "aux", "root", "case", "det", "obl", "cc", "conj", "case", "det", "nmod", "punct"], "triples": [{"uid": "TFS#10:8-0", "target_tags": "Not\\O what\\O I\\O would\\O expect\\O for\\O the\\O price\\O and\\O prestige\\O of\\O this\\O location\\B .\\O", "opinion_tags": "Not\\O what\\O I\\O would\\O expect\\B for\\O the\\O price\\O and\\O prestige\\O of\\O this\\O location\\O .\\O", "sentiment": "neutral"}]}, {"id": "TFS#10:9", "sentence": "All in all , I would return - as it was a beautiful restaurant - but I hope the staff pays more attention to the little details in the future .", "postag": ["DT", "IN", "DT", ",", "PRP", "MD", "VB", ",", "IN", "PRP", "VBD", "DT", "JJ", "NN", ",", "CC", "PRP", "VBP", "DT", "NN", "VBZ", "JJR", "NN", "IN", "DT", "JJ", "NNS", "IN", "DT", "NN", "."], "head": [7, 3, 1, 7, 7, 7, 0, 7, 14, 14, 14, 14, 14, 7, 7, 18, 18, 7, 20, 21, 18, 23, 21, 27, 27, 27, 21, 30, 30, 27, 7], "deprel": ["obl:npmod", "case", "nmod", "punct", "nsubj", "aux", "root", "punct", "mark", "nsubj", "cop", "det", "amod", "advcl", "punct", "cc", "nsubj", "conj", "det", "nsubj", "ccomp", "amod", "obj", "case", "det", "amod", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "TFS#10:9-0", "target_tags": "All\\O in\\O all\\O ,\\O I\\O would\\O return\\O -\\O as\\O it\\O was\\O a\\O beautiful\\O restaurant\\B -\\O but\\O I\\O hope\\O the\\O staff\\O pays\\O more\\O attention\\O to\\O the\\O little\\O details\\O in\\O the\\O future\\O .\\O", "opinion_tags": "All\\O in\\O all\\O ,\\O I\\O would\\O return\\O -\\O as\\O it\\O was\\O a\\O beautiful\\B restaurant\\O -\\O but\\O I\\O hope\\O the\\O staff\\O pays\\O more\\O attention\\O to\\O the\\O little\\O details\\O in\\O the\\O future\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#9:0", "sentence": "short and sweet – seating is great : it 's romantic , cozy and private .", "postag": ["JJ", "CC", "JJ", ",", "NN", "VBZ", "JJ", ":", "PRP", "VBZ", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [0, 3, 1, 1, 7, 7, 1, 7, 11, 11, 7, 13, 11, 15, 11, 1], "deprel": ["root", "cc", "conj", "punct", "nsubj", "cop", "parataxis", "punct", "nsubj", "cop", "parataxis", "punct", "conj", "cc", "conj", "punct"], "triples": [{"uid": "Z#9:0-0", "target_tags": "short\\O and\\O sweet\\O –\\O seating\\B is\\O great\\O :\\O it\\O 's\\O romantic\\O ,\\O cozy\\O and\\O private\\O .\\O", "opinion_tags": "short\\O and\\O sweet\\O –\\O seating\\O is\\O great\\B :\\O it\\O 's\\O romantic\\O ,\\O cozy\\O and\\O private\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#9:1", "sentence": "The boths are not as small as some of the reviews make them out to look they 're perfect for 2 people .", "postag": ["DT", "NNS", "VBP", "RB", "RB", "JJ", "IN", "DT", "IN", "DT", "NNS", "VBP", "PRP", "RP", "TO", "VB", "PRP", "VBP", "JJ", "IN", "CD", "NNS", "."], "head": [2, 6, 6, 6, 6, 0, 12, 12, 11, 11, 8, 6, 12, 12, 16, 12, 19, 19, 16, 22, 22, 19, 6], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "root", "mark", "nsubj", "case", "det", "nmod", "advcl", "obj", "compound:prt", "mark", "xcomp", "nsubj", "cop", "ccomp", "case", "nummod", "obl", "punct"], "triples": [{"uid": "Z#9:1-0", "target_tags": "The\\O boths\\B are\\O not\\O as\\O small\\O as\\O some\\O of\\O the\\O reviews\\O make\\O them\\O out\\O to\\O look\\O they\\O 're\\O perfect\\O for\\O 2\\O people\\O .\\O", "opinion_tags": "The\\O boths\\O are\\O not\\B as\\I small\\I as\\O some\\O of\\O the\\O reviews\\O make\\O them\\O out\\O to\\O look\\O they\\O 're\\O perfect\\B for\\O 2\\O people\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#9:2", "sentence": "The service was extremely fast and attentive ( thanks to the service button on your table ) but I barely understood 1 word when the waiter took our order .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "-LRB-", "NN", "IN", "DT", "NN", "NN", "IN", "PRP$", "NN", "-RRB-", "CC", "PRP", "RB", "VBD", "CD", "NN", "WRB", "DT", "NN", "VBD", "PRP$", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 9, 5, 13, 13, 13, 9, 16, 16, 13, 9, 21, 21, 21, 5, 23, 21, 27, 26, 27, 21, 29, 27, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct", "parataxis", "case", "det", "compound", "nmod", "case", "nmod:poss", "nmod", "punct", "cc", "nsubj", "advmod", "conj", "nummod", "obj", "mark", "det", "nsubj", "advcl", "nmod:poss", "obj", "punct"], "triples": [{"uid": "Z#9:2-0", "target_tags": "The\\O service\\B was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "opinion_tags": "The\\O service\\O was\\O extremely\\O fast\\B and\\O attentive\\B (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "sentiment": "positive"}, {"uid": "Z#9:2-1", "target_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\B button\\I on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "opinion_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\B to\\I the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "sentiment": "positive"}, {"uid": "Z#9:2-2", "target_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\O understood\\O 1\\O word\\O when\\O the\\O waiter\\B took\\O our\\O order\\O .\\O", "opinion_tags": "The\\O service\\O was\\O extremely\\O fast\\O and\\O attentive\\O (\\O thanks\\O to\\O the\\O service\\O button\\O on\\O your\\O table\\O )\\O but\\O I\\O barely\\B understood\\I 1\\O word\\O when\\O the\\O waiter\\O took\\O our\\O order\\O .\\O", "sentiment": "negative"}]}, {"id": "Z#9:3", "sentence": "The food was ok and fair nothing to go crazy .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", "NN", "TO", "VB", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 7, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "amod", "conj", "mark", "acl", "xcomp", "punct"], "triples": [{"uid": "Z#9:3-0", "target_tags": "The\\O food\\B was\\O ok\\O and\\O fair\\O nothing\\O to\\O go\\O crazy\\O .\\O", "opinion_tags": "The\\O food\\O was\\O ok\\B and\\O fair\\B nothing\\O to\\O go\\O crazy\\O .\\O", "sentiment": "neutral"}]}, {"id": "Z#9:5", "sentence": "Over all the looks of the place exceeds the actual meals .", "postag": ["IN", "PDT", "DT", "NNS", "IN", "DT", "NN", "VBZ", "DT", "JJ", "NNS", "."], "head": [4, 4, 4, 8, 7, 7, 4, 0, 11, 11, 8, 8], "deprel": ["case", "det:predet", "det", "obl", "case", "det", "nmod", "root", "det", "amod", "obj", "punct"], "triples": [{"uid": "Z#9:5-0", "target_tags": "Over\\O all\\O the\\O looks\\B of\\O the\\O place\\O exceeds\\O the\\O actual\\O meals\\O .\\O", "opinion_tags": "Over\\O all\\O the\\O looks\\O of\\O the\\O place\\O exceeds\\B the\\O actual\\O meals\\O .\\O", "sentiment": "positive"}]}, {"id": "NP#3:1", "sentence": "Subtle food and service", "postag": ["JJ", "NN", "CC", "NN"], "head": [2, 0, 4, 2], "deprel": ["amod", "root", "cc", "conj"], "triples": [{"uid": "NP#3:1-0", "target_tags": "Subtle\\O food\\B and\\O service\\O", "opinion_tags": "Subtle\\B food\\O and\\O service\\O", "sentiment": "positive"}, {"uid": "NP#3:1-1", "target_tags": "Subtle\\O food\\O and\\O service\\B", "opinion_tags": "Subtle\\B food\\O and\\O service\\O", "sentiment": "positive"}]}, {"id": "NP#3:2", "sentence": "Noodle pudding is exactly the type of service and food I enjoy .", "postag": ["NN", "NN", "VBZ", "RB", "DT", "NN", "IN", "NN", "CC", "NN", "PRP", "VBP", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 10, 8, 12, 8, 6], "deprel": ["compound", "nsubj", "cop", "advmod", "det", "root", "case", "nmod", "cc", "conj", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "NP#3:2-0", "target_tags": "Noodle\\O pudding\\O is\\O exactly\\O the\\O type\\O of\\O service\\B and\\O food\\O I\\O enjoy\\O .\\O", "opinion_tags": "Noodle\\O pudding\\O is\\O exactly\\O the\\O type\\O of\\O service\\O and\\O food\\O I\\O enjoy\\B .\\O", "sentiment": "positive"}, {"uid": "NP#3:2-1", "target_tags": "Noodle\\O pudding\\O is\\O exactly\\O the\\O type\\O of\\O service\\O and\\O food\\B I\\O enjoy\\O .\\O", "opinion_tags": "Noodle\\O pudding\\O is\\O exactly\\O the\\O type\\O of\\O service\\O and\\O food\\O I\\O enjoy\\B .\\O", "sentiment": "positive"}]}, {"id": "NP#3:3", "sentence": "Servers are all different , <PERSON> is my favorite .", "postag": ["NNS", "VBP", "RB", "JJ", ",", "NNP", "VBZ", "PRP$", "NN", "."], "head": [4, 4, 4, 0, 4, 9, 9, 9, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "nsubj", "cop", "nmod:poss", "parataxis", "punct"], "triples": [{"uid": "NP#3:3-0", "target_tags": "Servers\\O are\\O all\\O different\\O ,\\O Greg\\B is\\O my\\O favorite\\O .\\O", "opinion_tags": "Servers\\O are\\O all\\O different\\O ,\\O Greg\\O is\\O my\\O favorite\\B .\\O", "sentiment": "positive"}]}, {"id": "NP#3:7", "sentence": "I go out to eat and like my courses , servers are patient and never rush courses or force another drink .", "postag": ["PRP", "VBP", "RB", "TO", "VB", "CC", "IN", "PRP$", "NNS", ",", "NNS", "VBP", "JJ", "CC", "RB", "VBP", "NNS", "CC", "VBP", "DT", "NN", "."], "head": [2, 0, 2, 5, 2, 13, 9, 9, 13, 13, 13, 13, 2, 16, 16, 13, 16, 19, 13, 21, 19, 2], "deprel": ["nsubj", "root", "advmod", "mark", "advcl", "cc", "case", "nmod:poss", "obl", "punct", "nsubj", "cop", "conj", "cc", "advmod", "conj", "obj", "cc", "conj", "det", "obj", "punct"], "triples": [{"uid": "NP#3:7-0", "target_tags": "I\\O go\\O out\\O to\\O eat\\O and\\O like\\O my\\O courses\\O ,\\O servers\\B are\\O patient\\O and\\O never\\O rush\\O courses\\O or\\O force\\O another\\O drink\\O .\\O", "opinion_tags": "I\\O go\\O out\\O to\\O eat\\O and\\O like\\O my\\O courses\\O ,\\O servers\\O are\\O patient\\B and\\O never\\O rush\\O courses\\O or\\O force\\O another\\O drink\\O .\\O", "sentiment": "positive"}]}, {"id": "BHD#7:0", "sentence": "amazing fresh dogs but best of all endless toppings ! ! !", "postag": ["JJ", "JJ", "NNS", "CC", "JJS", "IN", "DT", "JJ", "NNS", ".", ".", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 5, 3, 3, 3], "deprel": ["amod", "amod", "root", "cc", "conj", "case", "det", "amod", "obl", "punct", "punct", "punct"], "triples": [{"uid": "BHD#7:0-0", "target_tags": "amazing\\O fresh\\O dogs\\B but\\O best\\O of\\O all\\O endless\\O toppings\\O !\\O !\\O !\\O", "opinion_tags": "amazing\\B fresh\\I dogs\\O but\\O best\\O of\\O all\\O endless\\O toppings\\O !\\O !\\O !\\O", "sentiment": "positive"}, {"uid": "BHD#7:0-1", "target_tags": "amazing\\O fresh\\O dogs\\O but\\O best\\O of\\O all\\O endless\\O toppings\\B !\\O !\\O !\\O", "opinion_tags": "amazing\\O fresh\\O dogs\\O but\\O best\\B of\\O all\\O endless\\B toppings\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "BHD#7:3", "sentence": "amazing fun for hot dog lovers of all ages PLEASE do yourself a favor and check this place out ! ! ! !", "postag": ["JJ", "NN", "IN", "JJ", "NN", "NNS", "IN", "DT", "NNS", "UH", "VB", "PRP", "DT", "NN", "CC", "VB", "DT", "NN", "RP", ".", ".", ".", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 6, 11, 2, 11, 14, 11, 16, 11, 18, 16, 16, 2, 2, 2, 2], "deprel": ["amod", "root", "case", "amod", "compound", "nmod", "case", "det", "nmod", "discourse", "parataxis", "i<PERSON><PERSON>", "det", "obj", "cc", "conj", "det", "obj", "compound:prt", "punct", "punct", "punct", "punct"], "triples": [{"uid": "BHD#7:3-0", "target_tags": "amazing\\O fun\\O for\\O hot\\B dog\\I lovers\\O of\\O all\\O ages\\O PLEASE\\O do\\O yourself\\O a\\O favor\\O and\\O check\\O this\\O place\\O out\\O !\\O !\\O !\\O !\\O", "opinion_tags": "amazing\\B fun\\I for\\O hot\\O dog\\O lovers\\O of\\O all\\O ages\\O PLEASE\\O do\\O yourself\\O a\\O favor\\O and\\O check\\O this\\O place\\O out\\O !\\O !\\O !\\O !\\O", "sentiment": "positive"}]}, {"id": "CLF#5:1", "sentence": "Stepping into Casa La Femme last night was a true experience unlike any other in New York !", "postag": ["VBG", "IN", "NNP", "NNP", "NNP", "JJ", "NN", "VBD", "DT", "JJ", "NN", "IN", "DT", "JJ", "IN", "NNP", "NNP", "."], "head": [11, 3, 1, 3, 3, 7, 1, 11, 11, 11, 0, 14, 14, 11, 16, 14, 16, 11], "deprel": ["csubj", "case", "obl", "flat", "flat", "amod", "obl:tmod", "cop", "det", "amod", "root", "case", "det", "nmod", "case", "nmod", "flat", "punct"], "triples": [{"uid": "CLF#5:1-0", "target_tags": "Stepping\\O into\\O Casa\\B La\\I Femme\\I last\\O night\\O was\\O a\\O true\\O experience\\O unlike\\O any\\O other\\O in\\O New\\O York\\O !\\O", "opinion_tags": "Stepping\\O into\\O Casa\\O La\\O Femme\\O last\\O night\\O was\\O a\\O true\\B experience\\O unlike\\O any\\O other\\O in\\O New\\O York\\O !\\O", "sentiment": "positive"}]}, {"id": "CLF#5:2", "sentence": "Highly impressed from the decor to the food to the hospitality to the great night I had !", "postag": ["RB", "JJ", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", "PRP", "VBD", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 11, 11, 2, 15, 15, 15, 2, 17, 15, 2], "deprel": ["advmod", "root", "case", "det", "obl", "case", "det", "obl", "case", "det", "obl", "case", "det", "amod", "obl", "nsubj", "acl:relcl", "punct"], "triples": [{"uid": "CLF#5:2-0", "target_tags": "Highly\\O impressed\\O from\\O the\\O decor\\B to\\O the\\O food\\O to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O", "opinion_tags": "Highly\\O impressed\\B from\\O the\\O decor\\O to\\O the\\O food\\O to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O", "sentiment": "positive"}, {"uid": "CLF#5:2-1", "target_tags": "Highly\\O impressed\\O from\\O the\\O decor\\O to\\O the\\O food\\B to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O", "opinion_tags": "Highly\\O impressed\\B from\\O the\\O decor\\O to\\O the\\O food\\O to\\O the\\O hospitality\\O to\\O the\\O great\\O night\\O I\\O had\\O !\\O", "sentiment": "positive"}]}, {"id": "CLF#5:3", "sentence": "The have a great cocktail with Citrus Vodka and lemon and lime juice and mint leaves that is to die for !", "postag": ["PRP", "VBP", "DT", "JJ", "NN", "IN", "JJ", "NN", "CC", "NN", "CC", "NN", "NN", "CC", "JJ", "NNS", "WDT", "VBZ", "TO", "VB", "IN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 10, 8, 13, 13, 8, 16, 16, 8, 18, 16, 20, 18, 20, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "case", "amod", "nmod", "cc", "conj", "cc", "compound", "conj", "cc", "amod", "conj", "nsubj", "acl:relcl", "mark", "xcomp", "obl", "punct"], "triples": [{"uid": "CLF#5:3-0", "target_tags": "The\\O have\\O a\\O great\\O cocktail\\B with\\I Citrus\\I Vodka\\I and\\I lemon\\I and\\I lime\\I juice\\I and\\I mint\\I leaves\\I that\\O is\\O to\\O die\\O for\\O !\\O", "opinion_tags": "The\\O have\\O a\\O great\\B cocktail\\O with\\O Citrus\\O Vodka\\O and\\O lemon\\O and\\O lime\\O juice\\O and\\O mint\\O leaves\\O that\\O is\\O to\\O die\\O for\\O !\\O", "sentiment": "positive"}]}, {"id": "CLF#5:4", "sentence": "Food took some time to prepare , all worth waiting for .", "postag": ["NN", "VBD", "DT", "NN", "TO", "VB", ",", "RB", "JJ", "VBG", "IN", "."], "head": [2, 0, 4, 2, 6, 4, 9, 9, 2, 9, 10, 2], "deprel": ["nsubj", "root", "det", "obj", "mark", "acl", "punct", "advmod", "parataxis", "xcomp", "obl", "punct"], "triples": [{"uid": "CLF#5:4-0", "target_tags": "Food\\B took\\O some\\O time\\O to\\O prepare\\O ,\\O all\\O worth\\O waiting\\O for\\O .\\O", "opinion_tags": "Food\\O took\\O some\\O time\\O to\\O prepare\\O ,\\O all\\O worth\\B waiting\\O for\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#5:5", "sentence": "We were drawn into the belly dancing show that captivated the crowd .", "postag": ["PRP", "VBD", "VBN", "IN", "DT", "NN", "NN", "NN", "WDT", "VBD", "DT", "NN", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 10, 8, 12, 10, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "case", "det", "compound", "compound", "obl", "nsubj", "acl:relcl", "det", "obj", "punct"], "triples": [{"uid": "CLF#5:5-0", "target_tags": "We\\O were\\O drawn\\O into\\O the\\O belly\\B dancing\\I show\\I that\\O captivated\\O the\\O crowd\\O .\\O", "opinion_tags": "We\\O were\\O drawn\\O into\\O the\\O belly\\O dancing\\O show\\O that\\O captivated\\B the\\O crowd\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#5:6", "sentence": "I never write on these sites but this restaurant is def worth commending !", "postag": ["PRP", "RB", "VBP", "IN", "DT", "NNS", "CC", "DT", "NN", "VBZ", "RB", "JJ", "VBG", "."], "head": [3, 3, 0, 6, 6, 3, 12, 9, 12, 12, 12, 3, 12, 3], "deprel": ["nsubj", "advmod", "root", "case", "det", "obl", "cc", "det", "nsubj", "cop", "advmod", "conj", "xcomp", "punct"], "triples": [{"uid": "CLF#5:6-0", "target_tags": "I\\O never\\O write\\O on\\O these\\O sites\\O but\\O this\\O restaurant\\B is\\O def\\O worth\\O commending\\O !\\O", "opinion_tags": "I\\O never\\O write\\O on\\O these\\O sites\\O but\\O this\\O restaurant\\O is\\O def\\O worth\\B commending\\O !\\O", "sentiment": "positive"}]}, {"id": "NP#4:2", "sentence": "The menu looked great , and the waiter was very nice , but when the food came , it was average .", "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", ",", "CC", "WRB", "DT", "NN", "VBD", ",", "PRP", "VBD", "JJ", "."], "head": [2, 3, 0, 3, 11, 11, 8, 11, 11, 11, 3, 21, 21, 17, 16, 17, 21, 21, 21, 21, 3, 3], "deprel": ["det", "nsubj", "root", "xcomp", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct", "cc", "mark", "det", "nsubj", "advcl", "punct", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "NP#4:2-0", "target_tags": "The\\O menu\\B looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O", "opinion_tags": "The\\O menu\\O looked\\O great\\B ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O", "sentiment": "positive"}, {"uid": "NP#4:2-1", "target_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\B was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O", "opinion_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\B ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\O .\\O", "sentiment": "positive"}, {"uid": "NP#4:2-2", "target_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\B came\\O ,\\O it\\O was\\O average\\O .\\O", "opinion_tags": "The\\O menu\\O looked\\O great\\O ,\\O and\\O the\\O waiter\\O was\\O very\\O nice\\O ,\\O but\\O when\\O the\\O food\\O came\\O ,\\O it\\O was\\O average\\B .\\O", "sentiment": "neutral"}]}, {"id": "NP#4:9", "sentence": "I have worked in restaurants and cook a lot , and there is no way a maggot should be able to get into well prepared food .", "postag": ["PRP", "VBP", "VBN", "IN", "NNS", "CC", "VBP", "DT", "NN", ",", "CC", "EX", "VBZ", "DT", "NN", "DT", "NN", "MD", "VB", "JJ", "TO", "VB", "IN", "RB", "VBN", "NN", "."], "head": [3, 3, 0, 5, 3, 7, 3, 9, 7, 13, 13, 13, 3, 15, 13, 17, 20, 20, 20, 15, 22, 20, 26, 25, 26, 22, 3], "deprel": ["nsubj", "aux", "root", "case", "obl", "cc", "conj", "det", "obj", "punct", "cc", "expl", "conj", "det", "nsubj", "det", "nsubj", "aux", "cop", "acl:relcl", "mark", "xcomp", "case", "advmod", "amod", "obl", "punct"], "triples": [{"uid": "NP#4:9-0", "target_tags": "I\\O have\\O worked\\O in\\O restaurants\\O and\\O cook\\O a\\O lot\\O ,\\O and\\O there\\O is\\O no\\O way\\O a\\O maggot\\O should\\O be\\O able\\O to\\O get\\O into\\O well\\O prepared\\O food\\B .\\O", "opinion_tags": "I\\O have\\O worked\\O in\\O restaurants\\O and\\O cook\\O a\\O lot\\O ,\\O and\\O there\\O is\\O no\\O way\\O a\\O maggot\\O should\\O be\\O able\\O to\\O get\\O into\\O well\\B prepared\\I food\\O .\\O", "sentiment": "negative"}]}, {"id": "NP#4:10", "sentence": "For a restaurant with such a good reputation and that is usually so packed , there was no reason for such a lack of intelligent customer service .", "postag": ["IN", "DT", "NN", "IN", "PDT", "DT", "JJ", "NN", "CC", "DT", "VBZ", "RB", "RB", "JJ", ",", "EX", "VBD", "DT", "NN", "IN", "PDT", "DT", "NN", "IN", "JJ", "NN", "NN", "."], "head": [3, 3, 17, 8, 8, 8, 8, 3, 14, 14, 14, 14, 14, 17, 17, 17, 0, 19, 17, 23, 23, 23, 19, 27, 27, 27, 23, 17], "deprel": ["case", "det", "obl", "case", "det:predet", "det", "amod", "nmod", "cc", "nsubj", "cop", "advmod", "advmod", "advcl", "punct", "expl", "root", "det", "nsubj", "case", "det:predet", "det", "nmod", "case", "amod", "compound", "nmod", "punct"], "triples": [{"uid": "NP#4:10-0", "target_tags": "For\\O a\\O restaurant\\B with\\O such\\O a\\O good\\O reputation\\O and\\O that\\O is\\O usually\\O so\\O packed\\O ,\\O there\\O was\\O no\\O reason\\O for\\O such\\O a\\O lack\\O of\\O intelligent\\O customer\\O service\\O .\\O", "opinion_tags": "For\\O a\\O restaurant\\O with\\O such\\O a\\O good\\B reputation\\I and\\O that\\O is\\O usually\\O so\\O packed\\O ,\\O there\\O was\\O no\\O reason\\O for\\O such\\O a\\O lack\\O of\\O intelligent\\O customer\\O service\\O .\\O", "sentiment": "positive"}, {"uid": "NP#4:10-1", "target_tags": "For\\O a\\O restaurant\\O with\\O such\\O a\\O good\\O reputation\\O and\\O that\\O is\\O usually\\O so\\O packed\\O ,\\O there\\O was\\O no\\O reason\\O for\\O such\\O a\\O lack\\O of\\O intelligent\\O customer\\B service\\I .\\O", "opinion_tags": "For\\O a\\O restaurant\\O with\\O such\\O a\\O good\\O reputation\\O and\\O that\\O is\\O usually\\O so\\O packed\\O ,\\O there\\O was\\O no\\O reason\\O for\\O such\\O a\\O lack\\O of\\O intelligent\\B customer\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "BzG#2:0", "sentence": "Great place , great value .", "postag": ["JJ", "NN", ",", "JJ", "NN", "."], "head": [2, 0, 2, 5, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct"], "triples": [{"uid": "BzG#2:0-0", "target_tags": "Great\\O place\\B ,\\O great\\O value\\O .\\O", "opinion_tags": "Great\\B place\\O ,\\O great\\O value\\O .\\O", "sentiment": "positive"}]}, {"id": "BzG#2:1", "sentence": "The food is flavorful , plentiful and reasonably priced .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "JJ", "CC", "RB", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "BzG#2:1-0", "target_tags": "The\\O food\\B is\\O flavorful\\O ,\\O plentiful\\O and\\O reasonably\\O priced\\O .\\O", "opinion_tags": "The\\O food\\O is\\O flavorful\\B ,\\O plentiful\\B and\\O reasonably\\B priced\\I .\\O", "sentiment": "positive"}]}, {"id": "BzG#2:2", "sentence": "The atmosphere is relaxed and casual .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct"], "triples": [{"uid": "BzG#2:2-0", "target_tags": "The\\O atmosphere\\B is\\O relaxed\\O and\\O casual\\O .\\O", "opinion_tags": "The\\O atmosphere\\O is\\O relaxed\\B and\\O casual\\B .\\O", "sentiment": "positive"}]}, {"id": "BzG#2:3", "sentence": "It 's a great place to order from or sit-in .", "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "IN", "CC", "JJ", "."], "head": [5, 5, 5, 5, 0, 7, 5, 7, 10, 8, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "obl", "cc", "conj", "punct"], "triples": [{"uid": "BzG#2:3-0", "target_tags": "It\\O 's\\O a\\O great\\O place\\B to\\O order\\O from\\O or\\O sit-in\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O great\\B place\\O to\\O order\\O from\\O or\\O sit-in\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#2:0", "sentence": "Sushi experience was unbelievable with my fiance .", "postag": ["NN", "NN", "VBD", "JJ", "IN", "PRP$", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 4], "deprel": ["compound", "nsubj", "cop", "root", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "Z#2:0-0", "target_tags": "Sushi\\B experience\\O was\\O unbelievable\\O with\\O my\\O fiance\\O .\\O", "opinion_tags": "Sushi\\O experience\\O was\\O unbelievable\\B with\\O my\\O fiance\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#9:0", "sentence": "Good creative rolls !", "postag": ["JJ", "JJ", "NNS", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"], "triples": [{"uid": "Y#9:0-0", "target_tags": "Good\\O creative\\O rolls\\B !\\O", "opinion_tags": "Good\\B creative\\B rolls\\O !\\O", "sentiment": "positive"}]}, {"id": "Y#9:1", "sentence": "Ya<PERSON>o is an excellent place to go if youre not into sashimi , or if you have friends who doesnt like sushi much .", "postag": ["NNP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "IN", "PRP", "RB", "IN", "NN", ",", "CC", "IN", "PRP", "VBP", "NNS", "WP", "MD", "VB", "NN", "JJ", "."], "head": [5, 5, 5, 5, 0, 7, 5, 12, 12, 12, 12, 7, 17, 17, 17, 17, 12, 17, 21, 21, 18, 23, 21, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "mark", "nsubj", "advmod", "case", "advcl", "punct", "cc", "mark", "nsubj", "conj", "obj", "nsubj", "aux", "acl:relcl", "compound", "obj", "punct"], "triples": [{"uid": "Y#9:1-0", "target_tags": "Yamato\\B is\\O an\\O excellent\\O place\\O to\\O go\\O if\\O youre\\O not\\O into\\O sashimi\\O ,\\O or\\O if\\O you\\O have\\O friends\\O who\\O doesnt\\O like\\O sushi\\O much\\O .\\O", "opinion_tags": "Yamato\\O is\\O an\\O excellent\\B place\\O to\\O go\\O if\\O youre\\O not\\O into\\O sashimi\\O ,\\O or\\O if\\O you\\O have\\O friends\\O who\\O doesnt\\O like\\O sushi\\O much\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#9:2", "sentence": "They have great rolls , the triple color and norwegetan rolls , are awesome and filling .", "postag": ["PRP", "VBP", "JJ", "NNS", ",", "DT", "NN", "NN", "CC", "NN", "NNS", ",", "VBP", "JJ", "CC", "VBG", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 11, 11, 8, 14, 14, 2, 16, 14, 2], "deprel": ["nsubj", "root", "amod", "obj", "punct", "det", "compound", "conj", "cc", "compound", "conj", "punct", "cop", "parataxis", "cc", "conj", "punct"], "triples": [{"uid": "Y#9:2-0", "target_tags": "They\\O have\\O great\\O rolls\\B ,\\O the\\O triple\\O color\\O and\\O norwegetan\\O rolls\\O ,\\O are\\O awesome\\O and\\O filling\\O .\\O", "opinion_tags": "They\\O have\\O great\\B rolls\\O ,\\O the\\O triple\\O color\\O and\\O norwegetan\\O rolls\\O ,\\O are\\O awesome\\O and\\O filling\\O .\\O", "sentiment": "positive"}, {"uid": "Y#9:2-1", "target_tags": "They\\O have\\O great\\O rolls\\O ,\\O the\\O triple\\B color\\I and\\I norwegetan\\I rolls\\I ,\\O are\\O awesome\\O and\\O filling\\O .\\O", "opinion_tags": "They\\O have\\O great\\O rolls\\O ,\\O the\\O triple\\O color\\O and\\O norwegetan\\O rolls\\O ,\\O are\\O awesome\\B and\\O filling\\B .\\O", "sentiment": "positive"}]}, {"id": "Y#9:3", "sentence": "One special roll and one regular roll is enough to fill you up , but save room for dessert !", "postag": ["CD", "JJ", "NN", "CC", "CD", "JJ", "NN", "VBZ", "JJ", "TO", "VB", "PRP", "RP", ",", "CC", "VB", "NN", "IN", "NN", "."], "head": [3, 3, 9, 7, 7, 7, 3, 9, 0, 11, 9, 11, 11, 16, 16, 11, 16, 19, 16, 9], "deprel": ["nummod", "amod", "nsubj", "cc", "nummod", "amod", "conj", "cop", "root", "mark", "xcomp", "obj", "compound:prt", "punct", "cc", "conj", "obj", "case", "obl", "punct"], "triples": [{"uid": "Y#9:3-0", "target_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\B !\\O", "opinion_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\B room\\I for\\O dessert\\O !\\O", "sentiment": "positive"}, {"uid": "Y#9:3-1", "target_tags": "One\\O special\\B roll\\I and\\O one\\O regular\\O roll\\O is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "opinion_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\B to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "sentiment": "positive"}, {"uid": "Y#9:3-2", "target_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\B roll\\I is\\O enough\\O to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "opinion_tags": "One\\O special\\O roll\\O and\\O one\\O regular\\O roll\\O is\\O enough\\B to\\O fill\\O you\\O up\\O ,\\O but\\O save\\O room\\O for\\O dessert\\O !\\O", "sentiment": "positive"}]}, {"id": "Y#9:4", "sentence": "They have a delicious banana chocolate dessert , as well as a great green tea tempura .", "postag": ["PRP", "VBP", "DT", "JJ", "JJ", "NN", "NN", ",", "RB", "RB", "IN", "DT", "JJ", "JJ", "NN", "NN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 16, 16, 9, 9, 16, 16, 16, 16, 7, 2], "deprel": ["nsubj", "root", "det", "amod", "amod", "compound", "obj", "punct", "cc", "fixed", "fixed", "det", "amod", "amod", "compound", "conj", "punct"], "triples": [{"uid": "Y#9:4-0", "target_tags": "They\\O have\\O a\\O delicious\\O banana\\B chocolate\\I dessert\\I ,\\O as\\O well\\O as\\O a\\O great\\O green\\O tea\\O tempura\\O .\\O", "opinion_tags": "They\\O have\\O a\\O delicious\\B banana\\O chocolate\\O dessert\\O ,\\O as\\O well\\O as\\O a\\O great\\O green\\O tea\\O tempura\\O .\\O", "sentiment": "positive"}, {"uid": "Y#9:4-1", "target_tags": "They\\O have\\O a\\O delicious\\O banana\\O chocolate\\O dessert\\O ,\\O as\\O well\\O as\\O a\\O great\\O green\\B tea\\I tempura\\I .\\O", "opinion_tags": "They\\O have\\O a\\O delicious\\O banana\\O chocolate\\O dessert\\O ,\\O as\\O well\\O as\\O a\\O great\\B green\\O tea\\O tempura\\O .\\O", "sentiment": "positive"}]}, {"id": "Y#9:5", "sentence": "The appetizers are also delicious !", "postag": ["DT", "NNS", "VBP", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "Y#9:5-0", "target_tags": "The\\O appetizers\\B are\\O also\\O delicious\\O !\\O", "opinion_tags": "The\\O appetizers\\O are\\O also\\O delicious\\B !\\O", "sentiment": "positive"}]}, {"id": "Z#5:0", "sentence": "Amazing food .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "Z#5:0-0", "target_tags": "Amazing\\O food\\B .\\O", "opinion_tags": "Amazing\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#5:1", "sentence": "Mazing interior .", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "Z#5:1-0", "target_tags": "Mazing\\O interior\\B .\\O", "opinion_tags": "Mazing\\B interior\\O .\\O", "sentiment": "negative"}]}, {"id": "Z#5:2", "sentence": "Great food !", "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"], "triples": [{"uid": "Z#5:2-0", "target_tags": "Great\\O food\\B !\\O", "opinion_tags": "Great\\B food\\O !\\O", "sentiment": "positive"}]}, {"id": "Z#5:3", "sentence": "I 've had my fair share of modern Japanese and this spot delivers .", "postag": ["PRP", "VBP", "VBN", "PRP$", "JJ", "NN", "IN", "JJ", "NNP", "CC", "DT", "NN", "VBZ", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 6, 13, 12, 13, 3, 3], "deprel": ["nsubj", "aux", "root", "nmod:poss", "amod", "obj", "case", "amod", "nmod", "cc", "det", "nsubj", "conj", "punct"], "triples": [{"uid": "Z#5:3-0", "target_tags": "I\\O 've\\O had\\O my\\O fair\\O share\\O of\\O modern\\B Japanese\\I and\\O this\\O spot\\O delivers\\O .\\O", "opinion_tags": "I\\O 've\\O had\\O my\\O fair\\O share\\O of\\O modern\\O Japanese\\O and\\O this\\O spot\\O delivers\\B .\\O", "sentiment": "positive"}]}, {"id": "Z#5:4", "sentence": "The atmosphere was pretty nice but had a bit lacking , which it tries to make up for with a crazy scheme of mirrors .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "VBD", "DT", "NN", "JJ", ",", "WDT", "PRP", "VBZ", "TO", "VB", "RP", "IN", "IN", "DT", "JJ", "NN", "IN", "NNS", "."], "head": [2, 5, 5, 5, 0, 7, 5, 9, 10, 7, 14, 14, 14, 10, 16, 14, 16, 22, 22, 22, 22, 16, 24, 22, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "det", "obl:npmod", "xcomp", "punct", "obj", "nsubj", "parataxis", "mark", "xcomp", "compound:prt", "case", "case", "det", "amod", "obl", "case", "nmod", "punct"], "triples": [{"uid": "Z#5:4-0", "target_tags": "The\\O atmosphere\\B was\\O pretty\\O nice\\O but\\O had\\O a\\O bit\\O lacking\\O ,\\O which\\O it\\O tries\\O to\\O make\\O up\\O for\\O with\\O a\\O crazy\\O scheme\\O of\\O mirrors\\O .\\O", "opinion_tags": "The\\O atmosphere\\O was\\O pretty\\O nice\\B but\\O had\\O a\\O bit\\O lacking\\O ,\\O which\\O it\\O tries\\O to\\O make\\O up\\O for\\O with\\O a\\O crazy\\O scheme\\O of\\O mirrors\\O .\\O", "sentiment": "negative"}, {"uid": "Z#5:4-1", "target_tags": "The\\O atmosphere\\O was\\O pretty\\O nice\\O but\\O had\\O a\\O bit\\O lacking\\O ,\\O which\\O it\\O tries\\O to\\O make\\O up\\O for\\O with\\O a\\O crazy\\O scheme\\B of\\I mirrors\\I .\\O", "opinion_tags": "The\\O atmosphere\\O was\\O pretty\\O nice\\O but\\O had\\O a\\O bit\\O lacking\\O ,\\O which\\O it\\O tries\\O to\\O make\\O up\\O for\\O with\\O a\\O crazy\\B scheme\\O of\\O mirrors\\O .\\O", "sentiment": "negative"}]}, {"id": "Z#5:6", "sentence": "Despite the confusing mirrors this will likely be my go-to for modern Japanese food for the foreseeable future .", "postag": ["IN", "DT", "JJ", "NNS", "DT", "MD", "RB", "VB", "PRP$", "NN", "IN", "JJ", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 10, 10, 10, 10, 10, 10, 0, 14, 14, 14, 10, 18, 18, 18, 10, 10], "deprel": ["case", "det", "amod", "obl", "nsubj", "aux", "advmod", "cop", "nmod:poss", "root", "case", "amod", "amod", "nmod", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "Z#5:6-0", "target_tags": "Despite\\O the\\O confusing\\O mirrors\\O this\\O will\\O likely\\O be\\O my\\O go-to\\O for\\O modern\\B Japanese\\I food\\I for\\O the\\O foreseeable\\O future\\O .\\O", "opinion_tags": "Despite\\O the\\O confusing\\O mirrors\\O this\\O will\\O likely\\O be\\O my\\O go-to\\B for\\I modern\\O Japanese\\O food\\O for\\O the\\O foreseeable\\O future\\O .\\O", "sentiment": "positive"}, {"uid": "Z#5:6-1", "target_tags": "Despite\\O the\\O confusing\\O mirrors\\B this\\O will\\O likely\\O be\\O my\\O go-to\\O for\\O modern\\O Japanese\\O food\\O for\\O the\\O foreseeable\\O future\\O .\\O", "opinion_tags": "Despite\\O the\\O confusing\\B mirrors\\O this\\O will\\O likely\\O be\\O my\\O go-to\\O for\\O modern\\O Japanese\\O food\\O for\\O the\\O foreseeable\\O future\\O .\\O", "sentiment": "negative"}]}, {"id": "TM#4:0", "sentence": "Indo Chinese food , pretty good ...", "postag": ["JJ", "JJ", "NN", ",", "RB", "JJ", "."], "head": [3, 3, 0, 3, 6, 3, 3], "deprel": ["amod", "amod", "root", "punct", "advmod", "parataxis", "punct"], "triples": [{"uid": "TM#4:0-0", "target_tags": "Indo\\B Chinese\\I food\\I ,\\O pretty\\O good\\O ...\\O", "opinion_tags": "Indo\\O Chinese\\O food\\O ,\\O pretty\\O good\\B ...\\O", "sentiment": "positive"}]}, {"id": "TM#4:1", "sentence": "Not a very fancy place but very good Chinese style Indian food .", "postag": ["RB", "DT", "RB", "JJ", "NN", "CC", "RB", "JJ", "JJ", "NN", "JJ", "NN", "."], "head": [5, 5, 4, 5, 0, 10, 8, 10, 10, 5, 12, 5, 5], "deprel": ["advmod", "det", "advmod", "amod", "root", "cc", "advmod", "amod", "amod", "conj", "amod", "conj", "punct"], "triples": [{"uid": "TM#4:1-0", "target_tags": "Not\\O a\\O very\\O fancy\\O place\\B but\\O very\\O good\\O Chinese\\O style\\O Indian\\O food\\O .\\O", "opinion_tags": "Not\\O a\\O very\\O fancy\\B place\\O but\\O very\\O good\\O Chinese\\O style\\O Indian\\O food\\O .\\O", "sentiment": "neutral"}, {"uid": "TM#4:1-1", "target_tags": "Not\\O a\\O very\\O fancy\\O place\\O but\\O very\\O good\\O Chinese\\B style\\I Indian\\I food\\I .\\O", "opinion_tags": "Not\\O a\\O very\\O fancy\\O place\\O but\\O very\\O good\\B Chinese\\O style\\O Indian\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "TM#4:2", "sentence": "The chicken lollipop is my favorite , most of the dishes ( I have to agree with a previous reviewer ) are quite oily and very spicy , espeically the Chilli Chicken .", "postag": ["DT", "NN", "NN", "VBZ", "PRP$", "NN", ",", "JJS", "IN", "DT", "NNS", "-LRB-", "PRP", "VBP", "TO", "VB", "IN", "DT", "JJ", "NN", "-RRB-", "VBP", "RB", "JJ", "CC", "RB", "JJ", ",", "RB", "DT", "NNP", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 6, 24, 11, 11, 8, 14, 14, 6, 16, 14, 20, 20, 20, 16, 14, 24, 24, 6, 27, 27, 24, 32, 32, 32, 32, 24, 6], "deprel": ["det", "compound", "nsubj", "cop", "nmod:poss", "root", "punct", "nsubj", "case", "det", "nmod", "punct", "nsubj", "parataxis", "mark", "xcomp", "case", "det", "amod", "obl", "punct", "cop", "advmod", "parataxis", "cc", "advmod", "conj", "punct", "advmod", "det", "compound", "conj", "punct"], "triples": [{"uid": "TM#4:2-0", "target_tags": "The\\O chicken\\B lollipop\\I is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "opinion_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\B ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "sentiment": "positive"}, {"uid": "TM#4:2-1", "target_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\B (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "opinion_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\B and\\O very\\O spicy\\B ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "sentiment": "negative"}, {"uid": "TM#4:2-2", "target_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\O and\\O very\\O spicy\\O ,\\O espeically\\O the\\O Chilli\\B Chicken\\I .\\O", "opinion_tags": "The\\O chicken\\O lollipop\\O is\\O my\\O favorite\\O ,\\O most\\O of\\O the\\O dishes\\O (\\O I\\O have\\O to\\O agree\\O with\\O a\\O previous\\O reviewer\\O )\\O are\\O quite\\O oily\\B and\\O very\\O spicy\\B ,\\O espeically\\O the\\O Chilli\\O Chicken\\O .\\O", "sentiment": "negative"}]}, {"id": "TM#4:3", "sentence": "My mom originally introduced me to this place , but even she ( being Indian ) feels the food can be somewhat over the top spicy and far too oily .", "postag": ["PRP$", "NN", "RB", "VBD", "PRP", "IN", "DT", "NN", ",", "CC", "RB", "PRP", "-LRB-", "VBG", "JJ", "-RRB-", "VBZ", "DT", "NN", "MD", "VB", "RB", "IN", "DT", "NN", "JJ", "CC", "RB", "RB", "JJ", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 17, 17, 12, 17, 15, 15, 12, 15, 4, 19, 25, 25, 25, 26, 25, 25, 17, 25, 30, 30, 30, 26, 4], "deprel": ["nmod:poss", "nsubj", "advmod", "root", "obj", "case", "det", "obl", "punct", "cc", "advmod", "nsubj", "punct", "cop", "advcl", "punct", "conj", "det", "nsubj", "aux", "cop", "advmod", "case", "det", "ccomp", "amod", "cc", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "TM#4:3-0", "target_tags": "My\\O mom\\O originally\\O introduced\\O me\\O to\\O this\\O place\\O ,\\O but\\O even\\O she\\O (\\O being\\O Indian\\O )\\O feels\\O the\\O food\\B can\\O be\\O somewhat\\O over\\O the\\O top\\O spicy\\O and\\O far\\O too\\O oily\\O .\\O", "opinion_tags": "My\\O mom\\O originally\\O introduced\\O me\\O to\\O this\\O place\\O ,\\O but\\O even\\O she\\O (\\O being\\O Indian\\O )\\O feels\\O the\\O food\\O can\\O be\\O somewhat\\O over\\O the\\O top\\O spicy\\B and\\O far\\O too\\O oily\\B .\\O", "sentiment": "negative"}]}, {"id": "TFS#11:0", "sentence": "I was speechless by the horrible food .", "postag": ["PRP", "VBD", "JJ", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 7, 7, 7, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "TFS#11:0-0", "target_tags": "I\\O was\\O speechless\\O by\\O the\\O horrible\\O food\\B .\\O", "opinion_tags": "I\\O was\\O speechless\\B by\\O the\\O horrible\\B food\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#11:1", "sentence": "I attended a holiday dinner at the restaurant , and the food was majorly disappointing .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "IN", "DT", "NN", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 15, 15, 12, 15, 15, 15, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "case", "det", "obl", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "TFS#11:1-0", "target_tags": "I\\<PERSON> attended\\O a\\O holiday\\O dinner\\O at\\O the\\O restaurant\\O ,\\O and\\O the\\O food\\B was\\O majorly\\O disappointing\\O .\\O", "opinion_tags": "I\\<PERSON> attended\\O a\\O holiday\\O dinner\\O at\\O the\\O restaurant\\O ,\\O and\\O the\\O food\\O was\\O majorly\\O disappointing\\B .\\O", "sentiment": "negative"}]}, {"id": "ADLT#10:1", "sentence": "This is the MOST wonderful restaurant in all of New York City , not just Brooklyn ...", "postag": ["DT", "VBZ", "DT", "RBS", "JJ", "NN", "IN", "DT", "IN", "NNP", "NNP", "NNP", ",", "RB", "RB", "NNP", "."], "head": [6, 6, 6, 5, 6, 0, 8, 6, 12, 12, 12, 8, 12, 16, 16, 12, 6], "deprel": ["nsubj", "cop", "det", "advmod", "amod", "root", "case", "nmod", "case", "compound", "compound", "nmod", "punct", "advmod", "advmod", "appos", "punct"], "triples": [{"uid": "ADLT#10:1-0", "target_tags": "This\\O is\\O the\\O MOST\\O wonderful\\O restaurant\\B in\\O all\\O of\\O New\\O York\\O City\\O ,\\O not\\O just\\O Brooklyn\\O ...\\O", "opinion_tags": "This\\O is\\O the\\O MOST\\O wonderful\\B restaurant\\O in\\O all\\O of\\O New\\O York\\O City\\O ,\\O not\\O just\\O Brooklyn\\O ...\\O", "sentiment": "positive"}]}, {"id": "ADLT#10:2", "sentence": "for 7 years they have put out the most tasty , most delicious food and kept it that way ...", "postag": ["IN", "CD", "NNS", "PRP", "VBP", "VBN", "RP", "DT", "RBS", "JJ", ",", "RBS", "JJ", "NN", "CC", "VBD", "PRP", "DT", "NN", "."], "head": [3, 3, 6, 6, 6, 0, 6, 14, 10, 14, 14, 13, 14, 6, 16, 6, 16, 19, 16, 6], "deprel": ["case", "nummod", "obl", "nsubj", "aux", "root", "compound:prt", "det", "advmod", "amod", "punct", "advmod", "amod", "obj", "cc", "conj", "obj", "det", "obl:tmod", "punct"], "triples": [{"uid": "ADLT#10:2-0", "target_tags": "for\\O 7\\O years\\O they\\O have\\O put\\O out\\O the\\O most\\O tasty\\O ,\\O most\\O delicious\\O food\\B and\\O kept\\O it\\O that\\O way\\O ...\\O", "opinion_tags": "for\\O 7\\O years\\O they\\O have\\O put\\O out\\O the\\O most\\O tasty\\B ,\\O most\\O delicious\\B food\\O and\\O kept\\O it\\O that\\O way\\O ...\\O", "sentiment": "positive"}]}, {"id": "ADLT#10:3", "sentence": "never swaying , never a bad meal , never bad service ...", "postag": ["RB", "VBG", ",", "RB", "DT", "JJ", "NN", ",", "RB", "JJ", "NN", "."], "head": [2, 0, 2, 7, 7, 7, 2, 11, 11, 11, 2, 2], "deprel": ["advmod", "root", "punct", "advmod", "det", "amod", "conj", "punct", "advmod", "amod", "parataxis", "punct"], "triples": [{"uid": "ADLT#10:3-0", "target_tags": "never\\O swaying\\O ,\\O never\\O a\\O bad\\O meal\\B ,\\O never\\O bad\\O service\\O ...\\O", "opinion_tags": "never\\O swaying\\O ,\\O never\\B a\\I bad\\I meal\\O ,\\O never\\O bad\\O service\\O ...\\O", "sentiment": "positive"}, {"uid": "ADLT#10:3-1", "target_tags": "never\\O swaying\\O ,\\O never\\O a\\O bad\\O meal\\O ,\\O never\\O bad\\O service\\B ...\\O", "opinion_tags": "never\\O swaying\\O ,\\O never\\O a\\O bad\\O meal\\O ,\\O never\\B bad\\I service\\O ...\\O", "sentiment": "positive"}]}, {"id": "ADLT#10:6", "sentence": "great food , great wine list , great service in a great neighborhood ...", "postag": ["JJ", "NN", ",", "JJ", "NN", "NN", ",", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 13, 13, 13, 9, 2], "deprel": ["amod", "root", "punct", "amod", "compound", "conj", "punct", "amod", "conj", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "ADLT#10:6-0", "target_tags": "great\\O food\\B ,\\O great\\O wine\\O list\\O ,\\O great\\O service\\O in\\O a\\O great\\O neighborhood\\O ...\\O", "opinion_tags": "great\\B food\\O ,\\O great\\O wine\\O list\\O ,\\O great\\O service\\O in\\O a\\O great\\O neighborhood\\O ...\\O", "sentiment": "positive"}, {"uid": "ADLT#10:6-1", "target_tags": "great\\O food\\O ,\\O great\\O wine\\B list\\I ,\\O great\\O service\\O in\\O a\\O great\\O neighborhood\\O ...\\O", "opinion_tags": "great\\O food\\O ,\\O great\\B wine\\O list\\O ,\\O great\\O service\\O in\\O a\\O great\\O neighborhood\\O ...\\O", "sentiment": "positive"}, {"uid": "ADLT#10:6-2", "target_tags": "great\\O food\\O ,\\O great\\O wine\\O list\\O ,\\O great\\O service\\B in\\O a\\O great\\O neighborhood\\O ...\\O", "opinion_tags": "great\\O food\\O ,\\O great\\O wine\\O list\\O ,\\O great\\B service\\O in\\O a\\O great\\O neighborhood\\O ...\\O", "sentiment": "positive"}, {"uid": "ADLT#10:6-3", "target_tags": "great\\O food\\O ,\\O great\\O wine\\O list\\O ,\\O great\\O service\\O in\\O a\\O great\\O neighborhood\\B ...\\O", "opinion_tags": "great\\O food\\O ,\\O great\\O wine\\O list\\O ,\\O great\\O service\\O in\\O a\\O great\\B neighborhood\\O ...\\O", "sentiment": "positive"}]}, {"id": "PP#3:0", "sentence": "<PERSON><PERSON> 's Pizza = true love", "postag": ["NNP", "POS", "NN", ",", "JJ", "NN"], "head": [3, 1, 0, 3, 6, 3], "deprel": ["nmod:poss", "case", "root", "punct", "amod", "parataxis"], "triples": [{"uid": "PP#3:0-0", "target_tags": "Pat<PERSON>\\B 's\\I Pizza\\I =\\O true\\O love\\O", "opinion_tags": "<PERSON><PERSON>\\O 's\\O Pizza\\O =\\O true\\B love\\I", "sentiment": "positive"}]}, {"id": "PP#3:1", "sentence": "Hands down the best pizza on the planet .", "postag": ["NNS", "RP", "DT", "JJS", "NN", "IN", "DT", "NN", "."], "head": [5, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["nsubj", "case", "det", "amod", "root", "case", "det", "nmod", "punct"], "triples": [{"uid": "PP#3:1-0", "target_tags": "Hands\\O down\\O the\\O best\\O pizza\\B on\\O the\\O planet\\O .\\O", "opinion_tags": "Hands\\O down\\O the\\O best\\B pizza\\O on\\O the\\O planet\\O .\\O", "sentiment": "positive"}]}, {"id": "BHD#6:0", "sentence": "great hot dogs .", "postag": ["JJ", "JJ", "NNS", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"], "triples": [{"uid": "BHD#6:0-0", "target_tags": "great\\O hot\\B dogs\\I .\\O", "opinion_tags": "great\\B hot\\O dogs\\O .\\O", "sentiment": "positive"}]}, {"id": "BHD#6:1", "sentence": "the hot dogs were juicy and tender inside and had plenty of crunch and snap on the outside .", "postag": ["DT", "JJ", "NNS", "VBD", "JJ", "CC", "JJ", "RB", "CC", "VBD", "NN", "IN", "NN", "CC", "NN", "IN", "DT", "JJ", "."], "head": [3, 3, 5, 5, 0, 7, 5, 5, 10, 5, 10, 13, 11, 15, 13, 18, 18, 10, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "cc", "conj", "advmod", "cc", "conj", "obj", "case", "nmod", "cc", "conj", "case", "det", "obl", "punct"], "triples": [{"uid": "BHD#6:1-0", "target_tags": "the\\O hot\\B dogs\\I were\\O juicy\\O and\\O tender\\O inside\\O and\\O had\\O plenty\\O of\\O crunch\\O and\\O snap\\O on\\O the\\O outside\\O .\\O", "opinion_tags": "the\\O hot\\O dogs\\O were\\O juicy\\B and\\O tender\\O inside\\O and\\O had\\O plenty\\O of\\O crunch\\O and\\O snap\\O on\\O the\\O outside\\O .\\O", "sentiment": "positive"}]}, {"id": "BHD#6:2", "sentence": "great toppings definitely a place you need to check out for late night munchies or a mid day boost !", "postag": ["JJ", "NNS", "RB", "DT", "NN", "PRP", "VBP", "TO", "VB", "RP", "IN", "JJ", "NN", "NNS", "CC", "DT", "AFX", "NN", "NN", "."], "head": [2, 0, 2, 5, 2, 7, 5, 9, 7, 9, 14, 14, 14, 9, 19, 19, 18, 19, 14, 2], "deprel": ["amod", "root", "advmod", "det", "appos", "nsubj", "acl:relcl", "mark", "xcomp", "compound:prt", "case", "amod", "compound", "obl", "cc", "det", "compound", "compound", "conj", "punct"], "triples": [{"uid": "BHD#6:2-0", "target_tags": "great\\O toppings\\B definitely\\O a\\O place\\O you\\O need\\O to\\O check\\O out\\O for\\O late\\O night\\O munchies\\O or\\O a\\O mid\\O day\\O boost\\O !\\O", "opinion_tags": "great\\B toppings\\O definitely\\O a\\O place\\O you\\O need\\O to\\O check\\O out\\O for\\O late\\O night\\O munchies\\O or\\O a\\O mid\\O day\\O boost\\O !\\O", "sentiment": "positive"}]}, {"id": "TM#5:2", "sentence": "For me dishes a little oily , but overall dining experience good .", "postag": ["IN", "PRP", "NNS", "DT", "JJ", "JJ", ",", "CC", "JJ", "NN", "NN", "JJ", "."], "head": [3, 3, 0, 5, 6, 3, 11, 11, 11, 11, 3, 11, 6], "deprel": ["case", "nmod:poss", "root", "det", "obl:npmod", "amod", "punct", "cc", "amod", "compound", "conj", "amod", "punct"], "triples": [{"uid": "TM#5:2-0", "target_tags": "For\\O me\\O dishes\\B a\\O little\\O oily\\O ,\\O but\\O overall\\O dining\\O experience\\O good\\O .\\O", "opinion_tags": "For\\O me\\O dishes\\O a\\O little\\O oily\\B ,\\O but\\O overall\\O dining\\O experience\\O good\\O .\\O", "sentiment": "negative"}]}, {"id": "TM#5:3", "sentence": "Helpful service and average price per dish $ 10 .", "postag": ["JJ", "NN", "CC", "JJ", "NN", "IN", "NN", "$", "CD", "."], "head": [2, 0, 5, 5, 2, 7, 2, 2, 8, 2], "deprel": ["amod", "root", "cc", "amod", "conj", "case", "nmod", "appos", "nummod", "punct"], "triples": [{"uid": "TM#5:3-0", "target_tags": "Helpful\\O service\\B and\\O average\\O price\\O per\\O dish\\O $\\O 10\\O .\\O", "opinion_tags": "Helpful\\B service\\O and\\O average\\O price\\O per\\O dish\\O $\\O 10\\O .\\O", "sentiment": "positive"}]}, {"id": "TM#5:4", "sentence": "The only thing that strikes you is the decor ? ( not very pleasant ) .", "postag": ["DT", "JJ", "NN", "WDT", "VBZ", "PRP", "VBZ", "DT", "NN", ".", "-LRB-", "RB", "RB", "JJ", "-RRB-", "."], "head": [3, 3, 9, 5, 3, 5, 9, 9, 0, 9, 14, 14, 14, 9, 14, 9], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "obj", "cop", "det", "root", "punct", "punct", "advmod", "advmod", "parataxis", "punct", "punct"], "triples": [{"uid": "TM#5:4-0", "target_tags": "The\\O only\\O thing\\O that\\O strikes\\O you\\O is\\O the\\O decor\\B ?\\O (\\O not\\O very\\O pleasant\\O )\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O that\\O strikes\\O you\\O is\\O the\\O decor\\O ?\\O (\\O not\\B very\\I pleasant\\I )\\O .\\O", "sentiment": "negative"}]}, {"id": "TM#6:0", "sentence": "great food", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "TM#6:0-0", "target_tags": "great\\O food\\B", "opinion_tags": "great\\B food\\O", "sentiment": "positive"}]}, {"id": "TM#6:1", "sentence": "This place has great indian chinese food .", "postag": ["DT", "NN", "VBZ", "JJ", "JJ", "JJ", "NN", "."], "head": [2, 3, 0, 7, 7, 7, 3, 3], "deprel": ["det", "nsubj", "root", "amod", "amod", "amod", "obj", "punct"], "triples": [{"uid": "TM#6:1-0", "target_tags": "This\\O place\\O has\\O great\\O indian\\B chinese\\I food\\I .\\O", "opinion_tags": "This\\O place\\O has\\O great\\B indian\\O chinese\\O food\\O .\\O", "sentiment": "positive"}]}, {"id": "TM#6:3", "sentence": "Be prepared to wait , because the place is pretty tiny .", "postag": ["VB", "JJ", "TO", "VB", ",", "IN", "DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 0, 4, 2, 2, 11, 8, 11, 11, 11, 2, 2], "deprel": ["cop", "root", "mark", "xcomp", "punct", "mark", "det", "nsubj", "cop", "advmod", "advcl", "punct"], "triples": [{"uid": "TM#6:3-0", "target_tags": "Be\\O prepared\\O to\\O wait\\O ,\\O because\\O the\\O place\\B is\\O pretty\\O tiny\\O .\\O", "opinion_tags": "Be\\O prepared\\O to\\O wait\\O ,\\O because\\O the\\O place\\O is\\O pretty\\O tiny\\B .\\O", "sentiment": "negative"}]}, {"id": "TM#6:5", "sentence": "Even though the place is not beautiful , the food speaks for itself .", "postag": ["RB", "IN", "DT", "NN", "VBZ", "RB", "JJ", ",", "DT", "NN", "VBZ", "IN", "PRP", "."], "head": [7, 7, 4, 7, 7, 7, 11, 11, 10, 11, 0, 13, 11, 11], "deprel": ["advmod", "mark", "det", "nsubj", "cop", "advmod", "advcl", "punct", "det", "nsubj", "root", "case", "obl", "punct"], "triples": [{"uid": "TM#6:5-0", "target_tags": "Even\\O though\\O the\\O place\\B is\\O not\\O beautiful\\O ,\\O the\\O food\\O speaks\\O for\\O itself\\O .\\O", "opinion_tags": "Even\\O though\\O the\\O place\\O is\\O not\\B beautiful\\I ,\\O the\\O food\\O speaks\\O for\\O itself\\O .\\O", "sentiment": "negative"}, {"uid": "TM#6:5-1", "target_tags": "Even\\O though\\O the\\O place\\O is\\O not\\O beautiful\\O ,\\O the\\O food\\B speaks\\O for\\O itself\\O .\\O", "opinion_tags": "Even\\O though\\O the\\O place\\O is\\O not\\O beautiful\\O ,\\O the\\O food\\O speaks\\B for\\I itself\\I .\\O", "sentiment": "positive"}]}, {"id": "TM#6:6", "sentence": "Best Indian Chinese in the city , by far !", "postag": ["JJS", "JJ", "JJ", "IN", "DT", "NN", ",", "IN", "RB", "."], "head": [3, 3, 0, 6, 6, 3, 3, 9, 3, 3], "deprel": ["amod", "amod", "root", "case", "det", "obl", "punct", "case", "obl", "punct"], "triples": [{"uid": "TM#6:6-0", "target_tags": "Best\\O Indian\\B Chinese\\I in\\O the\\O city\\O ,\\O by\\O far\\O !\\O", "opinion_tags": "Best\\B Indian\\O Chinese\\O in\\O the\\O city\\O ,\\O by\\O far\\O !\\O", "sentiment": "positive"}]}, {"id": "TVU#6:3", "sentence": "The martinis are amazing and very fairly priced .", "postag": ["DT", "NNS", "VBP", "JJ", "CC", "RB", "RB", "JJ", "."], "head": [2, 4, 4, 0, 8, 7, 8, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "TVU#6:3-0", "target_tags": "The\\O martinis\\B are\\O amazing\\O and\\O very\\O fairly\\O priced\\O .\\O", "opinion_tags": "The\\O martinis\\O are\\O amazing\\B and\\O very\\O fairly\\B priced\\I .\\O", "sentiment": "positive"}]}, {"id": "TVU#6:4", "sentence": "THE SERVICE IS AMAZING , i 've had different waiters and they were all nice , which is a rare thing in NYC .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "PRP", "VBP", "VBN", "JJ", "NNS", "CC", "PRP", "VBD", "RB", "JJ", ",", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "NNP", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 10, 8, 15, 15, 15, 15, 4, 21, 21, 21, 21, 21, 15, 23, 21, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "aux", "parataxis", "amod", "obj", "cc", "nsubj", "cop", "advmod", "conj", "punct", "nsubj", "cop", "det", "amod", "parataxis", "case", "nmod", "punct"], "triples": [{"uid": "TVU#6:4-0", "target_tags": "THE\\O SERVICE\\B IS\\O AMAZING\\O ,\\O i\\O 've\\O had\\O different\\O waiters\\O and\\O they\\O were\\O all\\O nice\\O ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "opinion_tags": "THE\\O SERVICE\\O IS\\O AMAZING\\B ,\\O i\\O 've\\O had\\O different\\O waiters\\O and\\O they\\O were\\O all\\O nice\\O ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "sentiment": "positive"}, {"uid": "TVU#6:4-1", "target_tags": "THE\\O SERVICE\\O IS\\O AMAZING\\O ,\\O i\\O 've\\O had\\O different\\O waiters\\B and\\O they\\O were\\O all\\O nice\\O ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "opinion_tags": "THE\\O SERVICE\\O IS\\O AMAZING\\O ,\\O i\\O 've\\O had\\O different\\O waiters\\O and\\O they\\O were\\O all\\O nice\\B ,\\O which\\O is\\O a\\O rare\\O thing\\O in\\O NYC\\O .\\O", "sentiment": "positive"}]}, {"id": "TVU#6:5", "sentence": "The DJ is awesome , I have been there for my birthday and a bunch of other times with friends and I keep going back .", "postag": ["DT", "NNP", "VBZ", "JJ", ",", "PRP", "VBP", "VBN", "RB", "IN", "PRP$", "NN", "CC", "DT", "NN", "IN", "JJ", "NNS", "IN", "NNS", "CC", "PRP", "VBP", "VBG", "RB", "."], "head": [2, 4, 4, 0, 4, 9, 9, 9, 4, 12, 12, 9, 15, 15, 12, 18, 18, 15, 20, 15, 22, 20, 4, 23, 24, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "aux", "cop", "parataxis", "case", "nmod:poss", "obl", "cc", "det", "conj", "case", "amod", "nmod", "case", "nmod", "cc", "conj", "conj", "xcomp", "advmod", "punct"], "triples": [{"uid": "TVU#6:5-0", "target_tags": "The\\O DJ\\B is\\O awesome\\O ,\\O I\\O have\\O been\\O there\\O for\\O my\\O birthday\\O and\\O a\\O bunch\\O of\\O other\\O times\\O with\\O friends\\O and\\O I\\O keep\\O going\\O back\\O .\\O", "opinion_tags": "The\\O DJ\\O is\\O awesome\\B ,\\O I\\O have\\O been\\O there\\O for\\O my\\O birthday\\O and\\O a\\O bunch\\O of\\O other\\O times\\O with\\O friends\\O and\\O I\\O keep\\O going\\O back\\O .\\O", "sentiment": "positive"}]}, {"id": "ADLT#3:1", "sentence": "Everything on the menu is great .", "postag": ["NN", "IN", "DT", "NN", "VBZ", "JJ", "."], "head": [6, 4, 4, 1, 6, 0, 6], "deprel": ["nsubj", "case", "det", "nmod", "cop", "root", "punct"], "triples": [{"uid": "ADLT#3:1-0", "target_tags": "Everything\\O on\\O the\\O menu\\B is\\O great\\O .\\O", "opinion_tags": "Everything\\O on\\O the\\O menu\\O is\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "ADLT#3:2", "sentence": "This establishment is the real deal .", "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [2, 6, 6, 6, 6, 0, 6], "deprel": ["det", "nsubj", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "ADLT#3:2-0", "target_tags": "This\\O establishment\\B is\\O the\\O real\\O deal\\O .\\O", "opinion_tags": "This\\O establishment\\O is\\O the\\O real\\B deal\\I .\\O", "sentiment": "positive"}]}, {"id": "ADLT#3:3", "sentence": "Wish NY had more of these kind of places : intimate , superb food , homey , top notch all the way around , certainly worth the wait .", "postag": ["VBP", "NNP", "VBD", "JJR", "IN", "DT", "NN", "IN", "NNS", ":", "JJ", ",", "JJ", "NN", ",", "JJ", ",", "JJ", "NN", "PDT", "DT", "NN", "RB", ",", "RB", "JJ", "DT", "NN", "."], "head": [0, 3, 1, 3, 7, 7, 4, 9, 7, 11, 7, 14, 14, 11, 16, 11, 19, 19, 11, 22, 22, 23, 11, 26, 26, 11, 28, 26, 1], "deprel": ["root", "nsubj", "ccomp", "obj", "case", "det", "obl", "case", "nmod", "punct", "appos", "punct", "amod", "conj", "punct", "conj", "punct", "amod", "conj", "det:predet", "det", "obl:npmod", "conj", "punct", "advmod", "list", "det", "obj", "punct"], "triples": [{"uid": "ADLT#3:3-0", "target_tags": "Wish\\O NY\\O had\\O more\\O of\\O these\\O kind\\O of\\O places\\O :\\O intimate\\O ,\\O superb\\O food\\B ,\\O homey\\O ,\\O top\\O notch\\O all\\O the\\O way\\O around\\O ,\\O certainly\\O worth\\O the\\O wait\\O .\\O", "opinion_tags": "Wish\\O NY\\O had\\O more\\O of\\O these\\O kind\\O of\\O places\\O :\\O intimate\\O ,\\O superb\\B food\\O ,\\O homey\\O ,\\O top\\O notch\\O all\\O the\\O way\\O around\\O ,\\O certainly\\O worth\\O the\\O wait\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#5:4", "sentence": "Look , the appetizers were really good .", "postag": ["VB", ",", "DT", "NNS", "VBD", "RB", "JJ", "."], "head": [0, 1, 4, 7, 7, 7, 1, 1], "deprel": ["root", "punct", "det", "nsubj", "cop", "advmod", "parataxis", "punct"], "triples": [{"uid": "TFS#5:4-0", "target_tags": "Look\\O ,\\O the\\O appetizers\\B were\\O really\\O good\\O .\\O", "opinion_tags": "Look\\O ,\\O the\\O appetizers\\O were\\O really\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "TFS#5:5", "sentence": "The entree was also very good .", "postag": ["DT", "NN", "VBD", "RB", "RB", "JJ", "."], "head": [2, 6, 6, 6, 6, 0, 6], "deprel": ["det", "nsubj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "TFS#5:5-0", "target_tags": "The\\O entree\\B was\\O also\\O very\\O good\\O .\\O", "opinion_tags": "The\\O entree\\O was\\O also\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "TFS#5:8", "sentence": "Yes , the place is classy and beautiful , but they most certainly target the uber whealthy Not the common joe that wants to go all out every once in a while .", "postag": ["UH", ",", "DT", "NN", "VBZ", "JJ", "CC", "JJ", ",", "CC", "PRP", "RBS", "RB", "VBP", "DT", "NN", "WDT", "RB", "DT", "JJ", "NN", "WDT", "VBZ", "TO", "VB", "RB", "RP", "DT", "RB", "IN", "DT", "NN", "."], "head": [6, 6, 4, 6, 6, 0, 8, 6, 14, 14, 14, 13, 14, 6, 16, 14, 21, 21, 21, 21, 16, 23, 21, 25, 23, 25, 25, 29, 25, 32, 32, 25, 6], "deprel": ["discourse", "punct", "det", "nsubj", "cop", "root", "cc", "conj", "punct", "cc", "nsubj", "advmod", "advmod", "conj", "det", "obj", "nsubj", "advmod", "det", "amod", "acl:relcl", "nsubj", "acl:relcl", "mark", "xcomp", "advmod", "compound:prt", "det", "advmod", "case", "det", "obl", "punct"], "triples": [{"uid": "TFS#5:8-0", "target_tags": "Yes\\O ,\\O the\\O place\\B is\\O classy\\O and\\O beautiful\\O ,\\O but\\O they\\O most\\O certainly\\O target\\O the\\O uber\\O whealthy\\O Not\\O the\\O common\\O joe\\O that\\O wants\\O to\\O go\\O all\\O out\\O every\\O once\\O in\\O a\\O while\\O .\\O", "opinion_tags": "Yes\\O ,\\O the\\O place\\O is\\O classy\\B and\\O beautiful\\B ,\\O but\\O they\\O most\\O certainly\\O target\\O the\\O uber\\O whealthy\\O Not\\O the\\O common\\O joe\\O that\\O wants\\O to\\O go\\O all\\O out\\O every\\O once\\O in\\O a\\O while\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#5:16", "sentence": "<PERSON><PERSON> was good but not amazing .", "postag": ["NNP", "VBD", "JJ", "CC", "RB", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "TFS#5:16-0", "target_tags": "Vanison\\B was\\O good\\O but\\O not\\O amazing\\O .\\O", "opinion_tags": "<PERSON><PERSON>\\O was\\O good\\B but\\O not\\B amazing\\I .\\O", "sentiment": "neutral"}]}, {"id": "TFS#5:17", "sentence": "<PERSON><PERSON> was quite excellent however .", "postag": ["NNP", "VBD", "RB", "JJ", "RB", "."], "head": [4, 4, 4, 0, 4, 4], "deprel": ["nsubj", "cop", "advmod", "root", "advmod", "punct"], "triples": [{"uid": "TFS#5:17-0", "target_tags": "Bison\\B was\\O quite\\O excellent\\O however\\O .\\O", "opinion_tags": "Bison\\O was\\O quite\\O excellent\\B however\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#5:18", "sentence": "Dessert : pure disaster .", "postag": ["NN", ":", "JJ", "NN", "."], "head": [0, 1, 4, 1, 1], "deprel": ["root", "punct", "amod", "appos", "punct"], "triples": [{"uid": "TFS#5:18-0", "target_tags": "Dessert\\B :\\O pure\\O disaster\\O .\\O", "opinion_tags": "Dessert\\O :\\O pure\\O disaster\\B .\\O", "sentiment": "negative"}]}, {"id": "TFS#5:21", "sentence": "I read reviews that called the restaurant too expensive and I thought to myself , but may be it is worth it .", "postag": ["PRP", "VBD", "NNS", "WDT", "VBD", "DT", "NN", "RB", "JJ", "CC", "PRP", "VBD", "IN", "PRP", ",", "CC", "MD", "VB", "PRP", "VBZ", "JJ", "PRP", "."], "head": [2, 0, 2, 5, 3, 7, 5, 9, 5, 12, 12, 2, 14, 12, 21, 21, 21, 21, 21, 21, 2, 21, 2], "deprel": ["nsubj", "root", "obj", "nsubj", "acl:relcl", "det", "obj", "advmod", "xcomp", "cc", "nsubj", "conj", "case", "obl", "punct", "cc", "aux", "cop", "nsubj", "cop", "conj", "obj", "punct"], "triples": [{"uid": "TFS#5:21-0", "target_tags": "I\\O read\\O reviews\\O that\\O called\\O the\\O restaurant\\B too\\O expensive\\O and\\O I\\O thought\\O to\\O myself\\O ,\\O but\\O may\\O be\\O it\\O is\\O worth\\O it\\O .\\O", "opinion_tags": "I\\O read\\O reviews\\O that\\O called\\O the\\O restaurant\\O too\\O expensive\\B and\\O I\\O thought\\O to\\O myself\\O ,\\O but\\O may\\O be\\O it\\O is\\O worth\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#5:25", "sentence": "All in all , the food was great ( except for the dessserts ) .", "postag": ["DT", "IN", "DT", ",", "DT", "NN", "VBD", "JJ", "-LRB-", "IN", "IN", "DT", "NNS", "-RRB-", "."], "head": [8, 3, 1, 8, 6, 8, 8, 0, 13, 13, 13, 13, 8, 13, 8], "deprel": ["advmod", "case", "nmod", "punct", "det", "nsubj", "cop", "root", "punct", "case", "case", "det", "obl", "punct", "punct"], "triples": [{"uid": "TFS#5:25-0", "target_tags": "All\\O in\\O all\\O ,\\O the\\O food\\B was\\O great\\O (\\O except\\O for\\O the\\O dessserts\\O )\\O .\\O", "opinion_tags": "All\\O in\\O all\\O ,\\O the\\O food\\O was\\O great\\B (\\O except\\O for\\O the\\O dessserts\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#5:26", "sentence": "The environment is very upscale and you will see a lot of rich guys with trophy wives or just highly paid escorts .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "PRP", "MD", "VB", "DT", "NN", "IN", "JJ", "NNS", "IN", "NN", "NNS", "CC", "RB", "RB", "VBN", "NNS", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 11, 9, 14, 14, 11, 17, 17, 9, 22, 22, 21, 22, 17, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "nsubj", "aux", "conj", "det", "obj", "case", "amod", "nmod", "case", "compound", "obl", "cc", "advmod", "advmod", "amod", "conj", "punct"], "triples": [{"uid": "TFS#5:26-0", "target_tags": "The\\O environment\\B is\\O very\\O upscale\\O and\\O you\\O will\\O see\\O a\\O lot\\O of\\O rich\\O guys\\O with\\O trophy\\O wives\\O or\\O just\\O highly\\O paid\\O escorts\\O .\\O", "opinion_tags": "The\\O environment\\O is\\O very\\O upscale\\B and\\O you\\O will\\O see\\O a\\O lot\\O of\\O rich\\O guys\\O with\\O trophy\\O wives\\O or\\O just\\O highly\\O paid\\O escorts\\O .\\O", "sentiment": "neutral"}]}, {"id": "TFS#5:27", "sentence": "If you are going for the food , it will not be worth it .", "postag": ["IN", "PRP", "VBP", "VBG", "IN", "DT", "NN", ",", "PRP", "MD", "RB", "VB", "JJ", "PRP", "."], "head": [4, 4, 4, 13, 7, 7, 4, 13, 13, 13, 13, 13, 0, 13, 13], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "det", "obl", "punct", "nsubj", "aux", "advmod", "cop", "root", "obj", "punct"], "triples": [{"uid": "TFS#5:27-0", "target_tags": "If\\O you\\O are\\O going\\O for\\O the\\O food\\B ,\\O it\\O will\\O not\\O be\\O worth\\O it\\O .\\O", "opinion_tags": "If\\O you\\O are\\O going\\O for\\O the\\O food\\O ,\\O it\\O will\\O not\\O be\\O worth\\B it\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#5:29", "sentence": "You would think they would make up for it with service , sadly , no .", "postag": ["PRP", "MD", "VB", "PRP", "MD", "VB", "RP", "IN", "PRP", "IN", "NN", ",", "RB", ",", "UH", "."], "head": [3, 3, 0, 6, 6, 3, 6, 9, 6, 11, 6, 3, 6, 3, 3, 3], "deprel": ["nsubj", "aux", "root", "nsubj", "aux", "ccomp", "compound:prt", "case", "obl", "case", "obl", "punct", "advmod", "punct", "discourse", "punct"], "triples": [{"uid": "TFS#5:29-0", "target_tags": "You\\O would\\O think\\O they\\O would\\O make\\O up\\O for\\O it\\O with\\O service\\B ,\\O sadly\\O ,\\O no\\O .\\O", "opinion_tags": "You\\O would\\O think\\O they\\O would\\O make\\O up\\O for\\O it\\O with\\O service\\O ,\\O sadly\\B ,\\O no\\O .\\O", "sentiment": "negative"}]}, {"id": "TFS#5:30", "sentence": "Service was just ok , it is not what you 'd expect for $ 500 .", "postag": ["NN", "VBD", "RB", "JJ", ",", "PRP", "VBZ", "RB", "WP", "PRP", "MD", "VB", "IN", "$", "CD", "."], "head": [4, 4, 4, 0, 4, 9, 4, 9, 4, 12, 12, 9, 14, 12, 14, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "nsubj", "parataxis", "advmod", "parataxis", "nsubj", "aux", "acl:relcl", "case", "obl", "nummod", "punct"], "triples": [{"uid": "TFS#5:30-0", "target_tags": "Service\\B was\\O just\\O ok\\O ,\\O it\\O is\\O not\\O what\\O you\\O 'd\\O expect\\O for\\O $\\O 500\\O .\\O", "opinion_tags": "Service\\O was\\O just\\O ok\\B ,\\O it\\O is\\O not\\O what\\O you\\O 'd\\O expect\\O for\\O $\\O 500\\O .\\O", "sentiment": "negative"}]}, {"id": "CLF#3:4", "sentence": "I literally just got back home after visiting Casa La Femme and was so offended by my visit felt it necessary to try and warn other diners who value their money and time .", "postag": ["PRP", "RB", "RB", "VBD", "RB", "RB", "IN", "VBG", "NNP", "NNP", "NNP", "CC", "VBD", "RB", "JJ", "IN", "PRP$", "NN", "VBD", "PRP", "JJ", "TO", "VB", "CC", "VB", "JJ", "NNS", "WP", "VBP", "PRP$", "NN", "CC", "NN", "."], "head": [4, 4, 4, 0, 4, 4, 8, 4, 11, 11, 8, 15, 15, 15, 4, 18, 18, 15, 4, 19, 19, 23, 21, 25, 23, 27, 25, 29, 27, 31, 29, 33, 31, 4], "deprel": ["nsubj", "advmod", "advmod", "root", "advmod", "advmod", "mark", "advcl", "compound", "compound", "obj", "cc", "cop", "advmod", "conj", "case", "nmod:poss", "obl", "conj", "expl", "xcomp", "mark", "xcomp", "cc", "conj", "amod", "obj", "nsubj", "acl:relcl", "nmod:poss", "obj", "cc", "conj", "punct"], "triples": [{"uid": "CLF#3:4-0", "target_tags": "I\\O literally\\O just\\O got\\O back\\O home\\O after\\O visiting\\O Casa\\B La\\I Femme\\I and\\O was\\O so\\O offended\\O by\\O my\\O visit\\O felt\\O it\\O necessary\\O to\\O try\\O and\\O warn\\O other\\O diners\\O who\\O value\\O their\\O money\\O and\\O time\\O .\\O", "opinion_tags": "I\\O literally\\O just\\O got\\O back\\O home\\O after\\O visiting\\O Casa\\O La\\O Femme\\O and\\O was\\O so\\O offended\\B by\\O my\\O visit\\O felt\\O it\\O necessary\\O to\\O try\\O and\\O warn\\O other\\O diners\\O who\\O value\\O their\\O money\\O and\\O time\\O .\\O", "sentiment": "negative"}]}, {"id": "CLF#3:11", "sentence": "The place is beautiful !", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "CLF#3:11-0", "target_tags": "The\\O place\\B is\\O beautiful\\O !\\O", "opinion_tags": "The\\O place\\O is\\O beautiful\\B !\\O", "sentiment": "positive"}]}, {"id": "CLF#3:12", "sentence": "The hostess was very pleasant .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "CLF#3:12-0", "target_tags": "The\\O hostess\\B was\\O very\\O pleasant\\O .\\O", "opinion_tags": "The\\O hostess\\O was\\O very\\O pleasant\\B .\\O", "sentiment": "positive"}]}, {"id": "CLF#3:13", "sentence": "However , our $ 14 drinks were were horrible !", "postag": ["RB", ",", "PRP$", "$", "CD", "NNS", "VBD", "VBD", "JJ", "."], "head": [9, 9, 6, 6, 4, 9, 9, 9, 0, 9], "deprel": ["advmod", "punct", "nmod:poss", "compound", "nummod", "nsubj", "cop", "cop", "root", "punct"], "triples": [{"uid": "CLF#3:13-0", "target_tags": "However\\O ,\\O our\\O $\\O 14\\O drinks\\B were\\O were\\O horrible\\O !\\O", "opinion_tags": "However\\O ,\\O our\\O $\\O 14\\O drinks\\O were\\O were\\O horrible\\B !\\O", "sentiment": "negative"}]}, {"id": "CLF#3:21", "sentence": "We asked for sides which the waiter than admitted that he forgot to put in that part of our order .", "postag": ["PRP", "VBD", "IN", "NNS", "WDT", "DT", "NN", "IN", "VBD", "IN", "PRP", "VBD", "TO", "VB", "IN", "DT", "NN", "IN", "PRP$", "NN", "."], "head": [2, 0, 4, 2, 9, 7, 9, 9, 4, 12, 12, 9, 14, 12, 17, 17, 14, 20, 20, 17, 2], "deprel": ["nsubj", "root", "case", "obl", "obj", "det", "nsubj", "mark", "acl:relcl", "mark", "nsubj", "ccomp", "mark", "xcomp", "case", "det", "obl", "case", "nmod:poss", "nmod", "punct"], "triples": [{"uid": "CLF#3:21-0", "target_tags": "We\\O asked\\O for\\O sides\\O which\\O the\\O waiter\\B than\\O admitted\\O that\\O he\\O forgot\\O to\\O put\\O in\\O that\\O part\\O of\\O our\\O order\\O .\\O", "opinion_tags": "We\\O asked\\O for\\O sides\\O which\\O the\\O waiter\\O than\\O admitted\\O that\\O he\\O forgot\\B to\\O put\\O in\\O that\\O part\\O of\\O our\\O order\\O .\\O", "sentiment": "negative"}]}, {"id": "CLF#3:22", "sentence": "My chicken was inedible as there were so many fatty lumps which i had to keep spitting out into my napkin .", "postag": ["PRP$", "NN", "VBD", "JJ", "IN", "EX", "VBD", "RB", "JJ", "NN", "NNS", "WDT", "PRP", "VBD", "TO", "VB", "VBG", "RP", "IN", "PRP$", "NN", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 11, 11, 7, 14, 14, 11, 16, 14, 16, 17, 21, 21, 17, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "mark", "expl", "advcl", "advmod", "amod", "compound", "nsubj", "obj", "nsubj", "acl:relcl", "mark", "xcomp", "xcomp", "compound:prt", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "CLF#3:22-0", "target_tags": "My\\O chicken\\B was\\O inedible\\O as\\O there\\O were\\O so\\O many\\O fatty\\O lumps\\O which\\O i\\O had\\O to\\O keep\\O spitting\\O out\\O into\\O my\\O napkin\\O .\\O", "opinion_tags": "My\\O chicken\\O was\\O inedible\\B as\\O there\\O were\\O so\\O many\\O fatty\\O lumps\\O which\\O i\\O had\\O to\\O keep\\O spitting\\O out\\O into\\O my\\O napkin\\O .\\O", "sentiment": "negative"}]}, {"id": "CLF#3:28", "sentence": "By the time we left our wallets were empy and so were our stomachs AND we missed the show we were supposed to see following our dinner , which would have been acceptable if we got to enjoy the experience of good food and belly dancers !", "postag": ["IN", "DT", "NN", "PRP", "VBD", "PRP$", "NNS", "VBD", "JJ", "CC", "RB", "VBD", "PRP$", "NNS", "CC", "PRP", "VBD", "DT", "NN", "PRP", "VBD", "VBN", "TO", "VB", "VBG", "PRP$", "NN", ",", "WDT", "MD", "VB", "VBN", "JJ", "IN", "PRP", "VBD", "TO", "VB", "DT", "NN", "IN", "JJ", "NN", "CC", "NN", "NNS", "."], "head": [3, 3, 9, 5, 3, 7, 5, 9, 0, 11, 9, 17, 14, 17, 17, 17, 9, 19, 17, 22, 22, 19, 24, 22, 27, 27, 24, 27, 33, 33, 33, 33, 27, 36, 36, 33, 38, 36, 40, 38, 43, 43, 40, 46, 46, 43, 9], "deprel": ["case", "det", "obl", "nsubj", "acl:relcl", "nmod:poss", "obj", "cop", "root", "cc", "conj", "aux", "nmod:poss", "nsubj", "cc", "nsubj", "conj", "det", "obj", "nsubj", "aux", "acl:relcl", "mark", "xcomp", "case", "nmod:poss", "obl", "punct", "nsubj", "aux", "aux", "cop", "acl:relcl", "mark", "nsubj", "advcl", "mark", "xcomp", "det", "obj", "case", "amod", "nmod", "cc", "compound", "conj", "punct"], "triples": [{"uid": "CLF#3:28-0", "target_tags": "By\\O the\\O time\\O we\\O left\\O our\\O wallets\\O were\\O empy\\O and\\O so\\O were\\O our\\O stomachs\\O AND\\O we\\O missed\\O the\\O show\\O we\\O were\\O supposed\\O to\\O see\\O following\\O our\\O dinner\\O ,\\O which\\O would\\O have\\O been\\O acceptable\\O if\\O we\\O got\\O to\\O enjoy\\O the\\O experience\\O of\\O good\\O food\\B and\\O belly\\O dancers\\O !\\O", "opinion_tags": "By\\O the\\O time\\O we\\O left\\O our\\O wallets\\O were\\O empy\\O and\\O so\\O were\\O our\\O stomachs\\O AND\\O we\\O missed\\O the\\O show\\O we\\O were\\O supposed\\O to\\O see\\O following\\O our\\O dinner\\O ,\\O which\\O would\\O have\\O been\\O acceptable\\O if\\O we\\O got\\O to\\O enjoy\\O the\\O experience\\O of\\O good\\B food\\O and\\O belly\\O dancers\\O !\\O", "sentiment": "negative"}]}, {"id": "CLF#3:29", "sentence": "If it seemed possible to do so while there I would have fought my bill since my dinner portion of my meal was inedible !", "postag": ["IN", "PRP", "VBD", "JJ", "TO", "VB", "RB", "IN", "RB", "PRP", "MD", "VB", "VBN", "PRP$", "NN", "IN", "PRP$", "NN", "NN", "IN", "PRP$", "NN", "VBD", "JJ", "."], "head": [3, 3, 13, 3, 6, 4, 6, 9, 13, 13, 13, 13, 0, 15, 13, 24, 19, 19, 24, 22, 22, 19, 24, 13, 13], "deprel": ["mark", "expl", "advcl", "xcomp", "mark", "xcomp", "advmod", "mark", "advcl", "nsubj", "aux", "aux", "root", "nmod:poss", "obj", "mark", "nmod:poss", "compound", "nsubj", "case", "nmod:poss", "nmod", "cop", "advcl", "punct"], "triples": [{"uid": "CLF#3:29-0", "target_tags": "If\\O it\\O seemed\\O possible\\O to\\O do\\O so\\O while\\O there\\O I\\O would\\O have\\O fought\\O my\\O bill\\O since\\O my\\O dinner\\O portion\\O of\\O my\\O meal\\B was\\O inedible\\O !\\O", "opinion_tags": "If\\O it\\O seemed\\O possible\\O to\\O do\\O so\\O while\\O there\\O I\\O would\\O have\\O fought\\O my\\O bill\\O since\\O my\\O dinner\\O portion\\O of\\O my\\O meal\\O was\\O inedible\\B !\\O", "sentiment": "negative"}]}, {"id": "CLF#3:30", "sentence": "I have never left a restaurant feeling as if i was abused , and wasted my hard earned money .", "postag": ["PRP", "VBP", "RB", "VBN", "DT", "NN", "NN", "IN", "IN", "PRP", "VBD", "VBN", ",", "CC", "VBD", "PRP$", "JJ", "VBN", "NN", "."], "head": [4, 4, 4, 0, 7, 7, 4, 12, 12, 12, 12, 4, 15, 15, 4, 19, 19, 19, 15, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "compound", "obj", "mark", "mark", "nsubj:pass", "aux:pass", "advcl", "punct", "cc", "conj", "nmod:poss", "amod", "amod", "obj", "punct"], "triples": [{"uid": "CLF#3:30-0", "target_tags": "I\\O have\\O never\\O left\\O a\\O restaurant\\B feeling\\O as\\O if\\O i\\O was\\O abused\\O ,\\O and\\O wasted\\O my\\O hard\\O earned\\O money\\O .\\O", "opinion_tags": "I\\O have\\O never\\O left\\O a\\O restaurant\\O feeling\\O as\\O if\\O i\\O was\\O abused\\B ,\\O and\\O wasted\\O my\\O hard\\O earned\\O money\\O .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:5", "sentence": "The menu is fairly simple without much descriptions .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "IN", "JJ", "NNS", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "case", "amod", "obl", "punct"], "triples": [{"uid": "BFC#9:5-0", "target_tags": "The\\O menu\\B is\\O fairly\\O simple\\O without\\O much\\O descriptions\\O .\\O", "opinion_tags": "The\\O menu\\O is\\O fairly\\O simple\\B without\\O much\\O descriptions\\O .\\O", "sentiment": "neutral"}]}, {"id": "BFC#9:6", "sentence": "There was no tap beer that evening , which was a disappointment .", "postag": ["EX", "VBD", "DT", "NN", "NN", "DT", "NN", ",", "WDT", "VBD", "DT", "NN", "."], "head": [2, 0, 5, 5, 2, 7, 2, 12, 12, 12, 12, 5, 2], "deprel": ["expl", "root", "det", "compound", "nsubj", "det", "obl:tmod", "punct", "nsubj", "cop", "det", "acl:relcl", "punct"], "triples": [{"uid": "BFC#9:6-0", "target_tags": "There\\O was\\O no\\O tap\\O beer\\B that\\O evening\\O ,\\O which\\O was\\O a\\O disappointment\\O .\\O", "opinion_tags": "There\\O was\\O no\\O tap\\O beer\\O that\\O evening\\O ,\\O which\\O was\\O a\\O disappointment\\B .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:7", "sentence": "Not much of a selection of bottled beer either , we went with <PERSON><PERSON><PERSON> .", "postag": ["RB", "JJ", "IN", "DT", "NN", "IN", "VBN", "NN", "CC", ",", "PRP", "VBD", "IN", "NNP", "."], "head": [2, 12, 5, 5, 2, 8, 8, 5, 8, 2, 12, 0, 14, 12, 12], "deprel": ["advmod", "advmod", "case", "det", "obl", "case", "amod", "nmod", "cc:preconj", "punct", "nsubj", "root", "case", "obl", "punct"], "triples": [{"uid": "BFC#9:7-0", "target_tags": "Not\\O much\\O of\\O a\\O selection\\B of\\I bottled\\I beer\\I either\\O ,\\O we\\O went\\O with\\O Brahma\\O .\\O", "opinion_tags": "Not\\B much\\I of\\O a\\O selection\\O of\\O bottled\\O beer\\O either\\O ,\\O we\\O went\\O with\\O Brahma\\O .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:8", "sentence": "The appetizers we ordered were served quickly - an order of fried oysters and clams were delicious but a tiny portion ( maybe 3 of each ) .", "postag": ["DT", "NNS", "PRP", "VBD", "VBD", "VBN", "RB", ",", "DT", "NN", "IN", "JJ", "NNS", "CC", "NNS", "VBD", "JJ", "CC", "DT", "JJ", "NN", "-LRB-", "RB", "CD", "IN", "DT", "-RRB-", "."], "head": [2, 6, 4, 2, 6, 0, 6, 6, 10, 17, 13, 13, 10, 15, 13, 17, 6, 21, 21, 21, 17, 24, 24, 21, 26, 24, 24, 6], "deprel": ["det", "nsubj:pass", "nsubj", "acl:relcl", "aux:pass", "root", "advmod", "punct", "det", "nsubj", "case", "amod", "nmod", "cc", "conj", "cop", "conj", "cc", "det", "amod", "conj", "punct", "advmod", "appos", "case", "nmod", "punct", "punct"], "triples": [{"uid": "BFC#9:8-0", "target_tags": "The\\O appetizers\\O we\\O ordered\\O were\\O served\\O quickly\\O -\\O an\\O order\\O of\\O fried\\B oysters\\I and\\I clams\\I were\\O delicious\\O but\\O a\\O tiny\\O portion\\O (\\O maybe\\O 3\\O of\\O each\\O )\\O .\\O", "opinion_tags": "The\\O appetizers\\O we\\O ordered\\O were\\O served\\O quickly\\O -\\O an\\O order\\O of\\O fried\\O oysters\\O and\\O clams\\O were\\O delicious\\B but\\O a\\O tiny\\O portion\\O (\\O maybe\\O 3\\O of\\O each\\O )\\O .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:9", "sentence": "The lobster knuckles ( special of the day ) were ok , but pretty tasteless .", "postag": ["DT", "NN", "NNS", "-LRB-", "JJ", "IN", "DT", "NN", "-RRB-", "VBD", "JJ", ",", "CC", "RB", "JJ", "."], "head": [3, 3, 11, 5, 3, 8, 8, 5, 5, 11, 0, 15, 15, 15, 11, 11], "deprel": ["det", "compound", "nsubj", "punct", "appos", "case", "det", "obl", "punct", "cop", "root", "punct", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "BFC#9:9-0", "target_tags": "The\\O lobster\\B knuckles\\I (\\O special\\O of\\O the\\O day\\O )\\O were\\O ok\\O ,\\O but\\O pretty\\O tasteless\\O .\\O", "opinion_tags": "The\\O lobster\\O knuckles\\O (\\O special\\O of\\O the\\O day\\O )\\O were\\O ok\\B ,\\O but\\O pretty\\O tasteless\\B .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:17", "sentence": "I had the Thai style Fried Sea Bass ... which was very good .", "postag": ["PRP", "VBD", "DT", "JJ", "NN", "VBN", "NNP", "NNP", ",", "WDT", "VBD", "RB", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 8, 13, 13, 13, 8, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "amod", "compound", "obj", "punct", "nsubj", "cop", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "BFC#9:17-0", "target_tags": "I\\O had\\O the\\O Thai\\B style\\I Fried\\I Sea\\I Bass\\I ...\\O which\\O was\\O very\\O good\\O .\\O", "opinion_tags": "I\\O had\\O the\\O Thai\\O style\\O Fried\\O Sea\\O Bass\\O ...\\O which\\O was\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "BFC#9:18", "sentence": "Everyone seemed generally happy with their food , except my brother who had the grilled <PERSON><PERSON> Ma<PERSON> , seemingly drenched in Grapfruit Juice !", "postag": ["NN", "VBD", "RB", "JJ", "IN", "PRP$", "NN", ",", "IN", "PRP$", "NN", "WP", "VBD", "DT", "VBN", "NNP", "NN", ",", "RB", "VBN", "IN", "NNP", "NNP", "."], "head": [2, 0, 4, 2, 7, 7, 4, 11, 11, 11, 7, 13, 11, 17, 17, 17, 13, 20, 20, 17, 23, 23, 20, 2], "deprel": ["nsubj", "root", "advmod", "xcomp", "case", "nmod:poss", "obl", "punct", "case", "nmod:poss", "nmod", "nsubj", "acl:relcl", "det", "amod", "compound", "obj", "punct", "advmod", "acl", "case", "compound", "obl", "punct"], "triples": [{"uid": "BFC#9:18-0", "target_tags": "Everyone\\O seemed\\O generally\\O happy\\O with\\O their\\O food\\B ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\O Mahi\\O Mahi\\O ,\\O seemingly\\O drenched\\O in\\O Grapfruit\\O Juice\\O !\\O", "opinion_tags": "Everyone\\O seemed\\O generally\\O happy\\B with\\O their\\O food\\O ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\O Mahi\\O Mahi\\O ,\\O seemingly\\O drenched\\O in\\O Grapfruit\\O Juice\\O !\\O", "sentiment": "positive"}, {"uid": "BFC#9:18-1", "target_tags": "Everyone\\O seemed\\O generally\\O happy\\O with\\O their\\O food\\O ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\B Mahi\\I Mahi\\I ,\\O seemingly\\O drenched\\O in\\O Grapfruit\\O Juice\\O !\\O", "opinion_tags": "Everyone\\O seemed\\O generally\\O happy\\O with\\O their\\O food\\O ,\\O except\\O my\\O brother\\O who\\O had\\O the\\O grilled\\O Mahi\\O Mahi\\O ,\\O seemingly\\O drenched\\B in\\O Grapfruit\\O Juice\\O !\\O", "sentiment": "negative"}]}, {"id": "BFC#9:19", "sentence": "I heard the lobster roll was excellent .", "postag": ["PRP", "VBD", "DT", "NN", "NN", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 7, 7, 2, 2], "deprel": ["nsubj", "root", "det", "compound", "nsubj", "cop", "ccomp", "punct"], "triples": [{"uid": "BFC#9:19-0", "target_tags": "I\\O heard\\O the\\O lobster\\B roll\\I was\\O excellent\\O .\\O", "opinion_tags": "I\\O heard\\O the\\O lobster\\O roll\\O was\\O excellent\\B .\\O", "sentiment": "positive"}]}, {"id": "BFC#9:21", "sentence": "All in all the food was good - a little on the expensive side , but fresh .", "postag": ["DT", "IN", "PDT", "DT", "NN", "VBD", "JJ", ",", "DT", "JJ", "IN", "DT", "JJ", "NN", ",", "CC", "JJ", "."], "head": [7, 5, 5, 5, 1, 7, 0, 7, 10, 7, 14, 14, 14, 7, 17, 17, 7, 7], "deprel": ["nsubj", "case", "det:predet", "det", "nmod", "cop", "root", "punct", "det", "obl:npmod", "case", "det", "amod", "obl", "punct", "cc", "conj", "punct"], "triples": [{"uid": "BFC#9:21-0", "target_tags": "All\\O in\\O all\\O the\\O food\\B was\\O good\\O -\\O a\\O little\\O on\\O the\\O expensive\\O side\\O ,\\O but\\O fresh\\O .\\O", "opinion_tags": "All\\O in\\O all\\O the\\O food\\O was\\O good\\B -\\O a\\O little\\O on\\O the\\O expensive\\B side\\O ,\\O but\\O fresh\\B .\\O", "sentiment": "negative"}]}, {"id": "BFC#9:22", "sentence": "Service not the friendliest to our `` large party '' !", "postag": ["NN", "RB", "DT", "JJS", "IN", "PRP$", "``", "JJ", "NN", "''", "."], "head": [4, 4, 4, 0, 9, 9, 9, 9, 4, 9, 4], "deprel": ["nsubj", "advmod", "det", "root", "case", "nmod:poss", "punct", "amod", "obl", "punct", "punct"], "triples": [{"uid": "BFC#9:22-0", "target_tags": "Service\\B not\\O the\\O friendliest\\O to\\O our\\O ``\\O large\\O party\\O ''\\O !\\O", "opinion_tags": "Service\\O not\\B the\\I friendliest\\I to\\O our\\O ``\\O large\\O party\\O ''\\O !\\O", "sentiment": "negative"}]}, {"id": "BG#5:0", "sentence": "Great Indian food", "postag": ["JJ", "JJ", "NN"], "head": [3, 3, 0], "deprel": ["amod", "amod", "root"], "triples": [{"uid": "BG#5:0-0", "target_tags": "Great\\O Indian\\B food\\I", "opinion_tags": "Great\\B Indian\\O food\\O", "sentiment": "positive"}]}, {"id": "BG#5:2", "sentence": "Food was amazing - I love Indian food and eat it quite regularly , but I can say this is one of the best I 've had .", "postag": ["NN", "VBD", "JJ", ",", "PRP", "VBP", "JJ", "NN", "CC", "VBP", "PRP", "RB", "RB", ",", "CC", "PRP", "MD", "VB", "DT", "VBZ", "CD", "IN", "DT", "JJS", "PRP", "VBP", "VBN", "."], "head": [3, 3, 0, 3, 6, 3, 8, 6, 10, 6, 10, 13, 10, 18, 18, 18, 18, 3, 21, 21, 18, 24, 24, 21, 27, 27, 24, 3], "deprel": ["nsubj", "cop", "root", "punct", "nsubj", "parataxis", "amod", "obj", "cc", "conj", "obj", "advmod", "advmod", "punct", "cc", "nsubj", "aux", "conj", "nsubj", "cop", "ccomp", "case", "det", "nmod", "nsubj", "aux", "acl:relcl", "punct"], "triples": [{"uid": "BG#5:2-0", "target_tags": "Food\\B was\\O amazing\\O -\\O I\\O love\\O Indian\\O food\\O and\\O eat\\O it\\O quite\\O regularly\\O ,\\O but\\O I\\O can\\O say\\O this\\O is\\O one\\O of\\O the\\O best\\O I\\O 've\\O had\\O .\\O", "opinion_tags": "Food\\O was\\O amazing\\B -\\O I\\O love\\O Indian\\O food\\O and\\O eat\\O it\\O quite\\O regularly\\O ,\\O but\\O I\\O can\\O say\\O this\\O is\\O one\\O of\\O the\\O best\\O I\\O 've\\O had\\O .\\O", "sentiment": "positive"}]}, {"id": "BG#5:3", "sentence": "Very `` normal Indian food '' , but done really well .", "postag": ["RB", "``", "JJ", "JJ", "NN", "''", ",", "CC", "VBN", "RB", "RB", "."], "head": [5, 5, 5, 5, 0, 5, 9, 9, 5, 11, 9, 5], "deprel": ["advmod", "punct", "amod", "amod", "root", "punct", "punct", "cc", "conj", "advmod", "advmod", "punct"], "triples": [{"uid": "BG#5:3-0", "target_tags": "Very\\O ``\\O normal\\O Indian\\B food\\I ''\\O ,\\O but\\O done\\O really\\O well\\O .\\O", "opinion_tags": "Very\\O ``\\O normal\\B Indian\\O food\\O ''\\O ,\\O but\\O done\\O really\\O well\\B .\\O", "sentiment": "positive"}]}, {"id": "BG#8:3", "sentence": "The lunch buffet is expensive but is deff worth it .", "postag": ["DT", "NN", "NN", "VBZ", "JJ", "CC", "VBZ", "RB", "JJ", "PRP", "."], "head": [3, 3, 5, 5, 0, 9, 9, 9, 5, 9, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "cc", "cop", "advmod", "conj", "obj", "punct"], "triples": [{"uid": "BG#8:3-0", "target_tags": "The\\O lunch\\B buffet\\I is\\O expensive\\O but\\O is\\O deff\\O worth\\O it\\O .\\O", "opinion_tags": "The\\O lunch\\O buffet\\O is\\O expensive\\B but\\O is\\O deff\\O worth\\B it\\O .\\O", "sentiment": "positive"}]}, {"id": "BG#8:5", "sentence": "We have gone for dinner only a few times but the same great quality and service is given .", "postag": ["PRP", "VBP", "VBN", "IN", "NN", "RB", "DT", "JJ", "NNS", "CC", "DT", "JJ", "JJ", "NN", "CC", "NN", "VBZ", "VBN", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 3, 18, 14, 14, 14, 18, 16, 14, 18, 3, 3], "deprel": ["nsubj", "aux", "root", "case", "obl", "advmod", "det", "amod", "obl:tmod", "cc", "det", "amod", "amod", "nsubj:pass", "cc", "conj", "aux:pass", "conj", "punct"], "triples": [{"uid": "BG#8:5-0", "target_tags": "We\\O have\\O gone\\O for\\O dinner\\O only\\O a\\O few\\O times\\O but\\O the\\O same\\O great\\O quality\\O and\\O service\\B is\\O given\\O .\\O", "opinion_tags": "We\\O have\\O gone\\O for\\O dinner\\O only\\O a\\O few\\O times\\O but\\O the\\O same\\O great\\B quality\\O and\\O service\\O is\\O given\\O .\\O", "sentiment": "positive"}, {"uid": "BG#8:5-1", "target_tags": "We\\O have\\O gone\\O for\\O dinner\\B only\\O a\\O few\\O times\\O but\\O the\\O same\\O great\\O quality\\O and\\O service\\O is\\O given\\O .\\O", "opinion_tags": "We\\O have\\O gone\\O for\\O dinner\\O only\\O a\\O few\\O times\\O but\\O the\\O same\\O great\\B quality\\I and\\O service\\O is\\O given\\O .\\O", "sentiment": "positive"}]}, {"id": "BG#8:6", "sentence": "<PERSON><PERSON><PERSON> is on my top 5 Indian places in NYC", "postag": ["NNP", "VBZ", "IN", "PRP$", "JJ", "CD", "JJ", "NNS", "IN", "NNP"], "head": [8, 8, 8, 8, 8, 8, 8, 0, 10, 8], "deprel": ["nsubj", "cop", "case", "nmod:poss", "amod", "nummod", "amod", "root", "case", "nmod"], "triples": [{"uid": "BG#8:6-0", "target_tags": "Bukhara\\B is\\O on\\O my\\O top\\O 5\\O Indian\\O places\\O in\\O NYC\\O", "opinion_tags": "Bukhara\\O is\\O on\\O my\\O top\\B 5\\O Indian\\O places\\O in\\O NYC\\O", "sentiment": "positive"}]}, {"id": "DBG#2:3", "sentence": "I have never been so disgusted by both food an service .", "postag": ["PRP", "VBP", "RB", "VBN", "RB", "JJ", "IN", "DT", "NN", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 11, 6, 6], "deprel": ["nsubj", "aux", "advmod", "cop", "advmod", "root", "case", "det", "obl", "det", "obl:npmod", "punct"], "triples": [{"uid": "DBG#2:3-0", "target_tags": "I\\O have\\O never\\O been\\O so\\O disgusted\\O by\\O both\\O food\\B an\\O service\\O .\\O", "opinion_tags": "I\\O have\\O never\\O been\\O so\\O disgusted\\B by\\O both\\O food\\O an\\O service\\O .\\O", "sentiment": "negative"}, {"uid": "DBG#2:3-1", "target_tags": "I\\O have\\O never\\O been\\O so\\O disgusted\\O by\\O both\\O food\\O an\\O service\\B .\\O", "opinion_tags": "I\\O have\\O never\\O been\\O so\\O disgusted\\B by\\O both\\O food\\O an\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "DBG#2:6", "sentence": "However , once I received my predictably mediocre order of what <PERSON><PERSON><PERSON> thinks passes as Korean fair , ( sometimes you have to settle when it 's your only option ) , I got through about half my kimchee before I found a piece of random lettuce accompanied by a far more disgusting , slimy , clearly bad piece of fish skin .", "postag": ["RB", ",", "IN", "PRP", "VBD", "PRP$", "RB", "JJ", "NN", "IN", "WP", "NNP", "VBZ", "VBZ", "IN", "JJ", "JJ", ",", "-LRB-", "RB", "PRP", "VBP", "TO", "VB", "WRB", "PRP", "VBZ", "PRP$", "JJ", "NN", "-RRB-", ",", "PRP", "VBD", "RB", "IN", "PDT", "PRP$", "NN", "IN", "PRP", "VBD", "DT", "NN", "IN", "JJ", "NN", "VBN", "IN", "DT", "RB", "RBR", "JJ", ",", "JJ", ",", "RB", "JJ", "NN", "IN", "NN", "NN", "."], "head": [34, 34, 5, 5, 34, 9, 8, 9, 5, 11, 9, 13, 11, 11, 17, 17, 14, 22, 22, 22, 22, 5, 24, 22, 30, 30, 30, 30, 30, 24, 22, 34, 34, 0, 34, 39, 39, 39, 34, 42, 42, 34, 44, 42, 47, 47, 44, 47, 59, 59, 52, 53, 59, 55, 59, 59, 58, 53, 48, 62, 62, 59, 34], "deprel": ["advmod", "punct", "mark", "nsubj", "advcl", "nmod:poss", "advmod", "amod", "obj", "case", "nmod", "nsubj", "acl:relcl", "acl:relcl", "case", "amod", "obl", "punct", "punct", "advmod", "nsubj", "parataxis", "mark", "xcomp", "mark", "nsubj", "cop", "nmod:poss", "amod", "advcl", "punct", "punct", "nsubj", "root", "advmod", "case", "det:predet", "nmod:poss", "obl", "mark", "nsubj", "advcl", "det", "obj", "case", "amod", "nmod", "acl", "case", "det", "advmod", "advmod", "amod", "punct", "amod", "punct", "advmod", "conj", "obl", "case", "compound", "nmod", "punct"], "triples": [{"uid": "DBG#2:6-0", "target_tags": "However\\O ,\\O once\\O I\\O received\\O my\\O predictably\\O mediocre\\O order\\O of\\O what\\O Dokebi\\O thinks\\O passes\\O as\\O Korean\\O fair\\O ,\\O (\\O sometimes\\O you\\O have\\O to\\O settle\\O when\\O it\\O 's\\O your\\O only\\O option\\O )\\O ,\\O I\\O got\\O through\\O about\\O half\\O my\\O kimchee\\B before\\O I\\O found\\O a\\O piece\\O of\\O random\\O lettuce\\O accompanied\\O by\\O a\\O far\\O more\\O disgusting\\O ,\\O slimy\\O ,\\O clearly\\O bad\\O piece\\O of\\O fish\\O skin\\O .\\O", "opinion_tags": "However\\O ,\\O once\\O I\\O received\\O my\\O predictably\\O mediocre\\O order\\O of\\O what\\O Dokebi\\O thinks\\O passes\\O as\\O Korean\\O fair\\O ,\\O (\\O sometimes\\O you\\O have\\O to\\O settle\\O when\\O it\\O 's\\O your\\O only\\O option\\O )\\O ,\\O I\\O got\\O through\\O about\\O half\\O my\\O kimchee\\O before\\O I\\O found\\O a\\O piece\\O of\\O random\\O lettuce\\O accompanied\\O by\\O a\\O far\\O more\\O disgusting\\B ,\\O slimy\\O ,\\O clearly\\O bad\\O piece\\O of\\O fish\\O skin\\O .\\O", "sentiment": "negative"}, {"uid": "DBG#2:6-1", "target_tags": "However\\O ,\\O once\\O I\\O received\\O my\\O predictably\\O mediocre\\O order\\O of\\O what\\O Dokebi\\O thinks\\O passes\\O as\\O Korean\\B fair\\I ,\\O (\\O sometimes\\O you\\O have\\O to\\O settle\\O when\\O it\\O 's\\O your\\O only\\O option\\O )\\O ,\\O I\\O got\\O through\\O about\\O half\\O my\\O kimchee\\O before\\O I\\O found\\O a\\O piece\\O of\\O random\\O lettuce\\O accompanied\\O by\\O a\\O far\\O more\\O disgusting\\O ,\\O slimy\\O ,\\O clearly\\O bad\\O piece\\O of\\O fish\\O skin\\O .\\O", "opinion_tags": "However\\O ,\\O once\\O I\\O received\\O my\\O predictably\\O mediocre\\B order\\O of\\O what\\O Dokebi\\O thinks\\O passes\\O as\\O Korean\\O fair\\O ,\\O (\\O sometimes\\O you\\O have\\O to\\O settle\\O when\\O it\\O 's\\O your\\O only\\O option\\O )\\O ,\\O I\\O got\\O through\\O about\\O half\\O my\\O kimchee\\O before\\O I\\O found\\O a\\O piece\\O of\\O random\\O lettuce\\O accompanied\\O by\\O a\\O far\\O more\\O disgusting\\O ,\\O slimy\\O ,\\O clearly\\O bad\\O piece\\O of\\O fish\\O skin\\O .\\O", "sentiment": "negative"}]}, {"id": "DBG#2:11", "sentence": "My girlfriend , being slightly more aggressive , and having been equally disgusted causing her to throw out the remainder of her barely eaten meal , called back only to be informed that I was probably wrong and that it was most likely an oyster , and that we were also blacklisted from their restaurant .", "postag": ["PRP$", "NN", ",", "VBG", "RB", "RBR", "JJ", ",", "CC", "VBG", "VBN", "RB", "JJ", "VBG", "PRP", "TO", "VB", "RP", "DT", "NN", "IN", "PRP$", "RB", "VBN", "NN", ",", "VBN", "RB", "RB", "TO", "VB", "VBN", "IN", "PRP", "VBD", "RB", "JJ", "CC", "IN", "PRP", "VBD", "RBS", "RB", "DT", "NN", ",", "CC", "IN", "PRP", "VBD", "RB", "VBN", "IN", "PRP$", "NN", "."], "head": [2, 27, 7, 7, 6, 7, 2, 13, 13, 13, 13, 13, 7, 13, 14, 17, 14, 17, 20, 17, 25, 25, 24, 25, 20, 27, 0, 27, 32, 32, 32, 27, 37, 37, 37, 37, 32, 45, 45, 45, 45, 43, 45, 45, 37, 52, 52, 52, 52, 52, 52, 45, 55, 55, 52, 27], "deprel": ["nmod:poss", "nsubj", "punct", "cop", "advmod", "advmod", "acl", "punct", "cc", "aux", "cop", "advmod", "conj", "xcomp", "obj", "mark", "xcomp", "compound:prt", "det", "obj", "case", "nmod:poss", "advmod", "amod", "nmod", "punct", "root", "advmod", "advmod", "mark", "aux:pass", "advcl", "mark", "nsubj", "cop", "advmod", "ccomp", "cc", "mark", "nsubj", "cop", "advmod", "advmod", "det", "conj", "punct", "cc", "mark", "nsubj:pass", "aux:pass", "advmod", "conj", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "DBG#2:11-0", "target_tags": "My\\O girlfriend\\O ,\\O being\\O slightly\\O more\\O aggressive\\O ,\\O and\\O having\\O been\\O equally\\O disgusted\\O causing\\O her\\O to\\O throw\\O out\\O the\\O remainder\\O of\\O her\\O barely\\O eaten\\O meal\\B ,\\O called\\O back\\O only\\O to\\O be\\O informed\\O that\\O I\\O was\\O probably\\O wrong\\O and\\O that\\O it\\O was\\O most\\O likely\\O an\\O oyster\\O ,\\O and\\O that\\O we\\O were\\O also\\O blacklisted\\O from\\O their\\O restaurant\\O .\\O", "opinion_tags": "My\\O girlfriend\\O ,\\O being\\O slightly\\O more\\O aggressive\\O ,\\O and\\O having\\O been\\O equally\\O disgusted\\B causing\\O her\\O to\\O throw\\O out\\O the\\O remainder\\O of\\O her\\O barely\\O eaten\\O meal\\O ,\\O called\\O back\\O only\\O to\\O be\\O informed\\O that\\O I\\O was\\O probably\\O wrong\\O and\\O that\\O it\\O was\\O most\\O likely\\O an\\O oyster\\O ,\\O and\\O that\\O we\\O were\\O also\\O blacklisted\\O from\\O their\\O restaurant\\O .\\O", "sentiment": "negative"}]}, {"id": "DBG#2:14", "sentence": "It was n't as if this restaurant had any major bragging points before hand , but now it 's simply repulsive .", "postag": ["PRP", "VBD", "RB", "IN", "IN", "DT", "NN", "VBD", "DT", "JJ", "NN", "NNS", "IN", "NN", ",", "CC", "RB", "PRP", "VBZ", "RB", "JJ", "."], "head": [2, 0, 2, 8, 8, 7, 8, 2, 12, 12, 12, 8, 14, 12, 21, 21, 21, 21, 21, 21, 2, 2], "deprel": ["nsubj", "root", "advmod", "mark", "mark", "det", "nsubj", "advcl", "det", "amod", "compound", "obj", "case", "nmod", "punct", "cc", "advmod", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "DBG#2:14-0", "target_tags": "It\\O was\\O n't\\O as\\O if\\O this\\O restaurant\\B had\\O any\\O major\\O bragging\\O points\\O before\\O hand\\O ,\\O but\\O now\\O it\\O 's\\O simply\\O repulsive\\O .\\O", "opinion_tags": "It\\O was\\O n't\\O as\\O if\\O this\\O restaurant\\O had\\O any\\O major\\O bragging\\O points\\O before\\O hand\\O ,\\O but\\O now\\O it\\O 's\\O simply\\O repulsive\\B .\\O", "sentiment": "negative"}]}, {"id": "CLF#7:0", "sentence": "Gorgeous place ideal for a romantic dinner", "postag": ["JJ", "NN", "JJ", "IN", "DT", "JJ", "NN"], "head": [2, 0, 2, 7, 7, 7, 3], "deprel": ["amod", "root", "amod", "case", "det", "amod", "obl"], "triples": [{"uid": "CLF#7:0-0", "target_tags": "Gorgeous\\O place\\B ideal\\O for\\O a\\O romantic\\O dinner\\O", "opinion_tags": "Gorgeous\\B place\\O ideal\\B for\\O a\\O romantic\\O dinner\\O", "sentiment": "positive"}]}, {"id": "CLF#7:2", "sentence": "I book a gorgeous white organza tent which included a four course prix fix menu which we enjoyed a lot .", "postag": ["PRP", "VBP", "DT", "JJ", "JJ", "NN", "NN", "WDT", "VBD", "DT", "CD", "NN", "NN", "NN", "NN", "WDT", "PRP", "VBD", "DT", "NN", "."], "head": [2, 0, 7, 7, 7, 7, 2, 9, 7, 15, 12, 13, 15, 15, 9, 18, 18, 15, 20, 18, 2], "deprel": ["nsubj", "root", "det", "amod", "amod", "compound", "obj", "nsubj", "acl:relcl", "det", "nummod", "compound", "compound", "compound", "obj", "obj", "nsubj", "acl:relcl", "det", "obj", "punct"], "triples": [{"uid": "CLF#7:2-0", "target_tags": "I\\O book\\O a\\O gorgeous\\O white\\O organza\\O tent\\O which\\O included\\O a\\O four\\B course\\I prix\\I fix\\I menu\\I which\\O we\\O enjoyed\\O a\\O lot\\O .\\O", "opinion_tags": "I\\O book\\O a\\O gorgeous\\O white\\O organza\\O tent\\O which\\O included\\O a\\O four\\O course\\O prix\\O fix\\O menu\\O which\\O we\\O enjoyed\\B a\\O lot\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#7:2-1", "target_tags": "I\\O book\\O a\\O gorgeous\\O white\\B organza\\I tent\\I which\\O included\\O a\\O four\\O course\\O prix\\O fix\\O menu\\O which\\O we\\O enjoyed\\O a\\O lot\\O .\\O", "opinion_tags": "I\\O book\\O a\\O gorgeous\\B white\\O organza\\O tent\\O which\\O included\\O a\\O four\\O course\\O prix\\O fix\\O menu\\O which\\O we\\O enjoyed\\O a\\O lot\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#7:3", "sentence": "The service was spectacular as the waiter knew everything about the menu and his recommendations were amazing !", "postag": ["DT", "NN", "VBD", "JJ", "IN", "DT", "NN", "VBD", "NN", "IN", "DT", "NN", "CC", "PRP$", "NNS", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 8, 7, 8, 4, 8, 12, 12, 9, 17, 15, 17, 17, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "det", "nsubj", "advcl", "obj", "case", "det", "nmod", "cc", "nmod:poss", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "CLF#7:3-0", "target_tags": "The\\O service\\B was\\O spectacular\\O as\\O the\\O waiter\\O knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\O !\\O", "opinion_tags": "The\\O service\\O was\\O spectacular\\B as\\O the\\O waiter\\O knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\O !\\O", "sentiment": "positive"}, {"uid": "CLF#7:3-1", "target_tags": "The\\O service\\O was\\O spectacular\\O as\\O the\\O waiter\\B knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\O !\\O", "opinion_tags": "The\\O service\\O was\\O spectacular\\O as\\O the\\O waiter\\O knew\\O everything\\O about\\O the\\O menu\\O and\\O his\\O recommendations\\O were\\O amazing\\B !\\O", "sentiment": "positive"}]}, {"id": "CLF#7:4", "sentence": "I completely recommend Casa La Femme for any special occasion and to REALLY impress your date .", "postag": ["PRP", "RB", "VBP", "NNP", "NNP", "NNP", "IN", "DT", "JJ", "NN", "CC", "TO", "RB", "VB", "PRP$", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 3, 14, 14, 14, 3, 16, 14, 3], "deprel": ["nsubj", "advmod", "root", "compound", "compound", "obj", "case", "det", "amod", "obl", "cc", "mark", "advmod", "conj", "nmod:poss", "obj", "punct"], "triples": [{"uid": "CLF#7:4-0", "target_tags": "I\\O completely\\O recommend\\O Casa\\B La\\I Femme\\I for\\O any\\O special\\O occasion\\O and\\O to\\O REALLY\\O impress\\O your\\O date\\O .\\O", "opinion_tags": "I\\O completely\\O recommend\\B Casa\\O La\\O Femme\\O for\\O any\\O special\\O occasion\\O and\\O to\\O REALLY\\O impress\\O your\\O date\\O .\\O", "sentiment": "positive"}]}, {"id": "DBG#7:4", "sentence": "The bibimbap was average , but the stone bowl was n't even close to sizzling .", "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "DT", "NN", "NN", "VBD", "RB", "RB", "JJ", "IN", "VBG", "."], "head": [2, 4, 4, 0, 13, 13, 9, 9, 13, 13, 13, 13, 4, 15, 13, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "det", "compound", "nsubj", "cop", "advmod", "advmod", "conj", "case", "obl", "punct"], "triples": [{"uid": "DBG#7:4-0", "target_tags": "The\\O bibimbap\\B was\\O average\\O ,\\O but\\O the\\O stone\\O bowl\\O was\\O n't\\O even\\O close\\O to\\O sizzling\\O .\\O", "opinion_tags": "The\\O bibimbap\\O was\\O average\\B ,\\O but\\O the\\O stone\\O bowl\\O was\\O n't\\O even\\O close\\O to\\O sizzling\\O .\\O", "sentiment": "neutral"}, {"uid": "DBG#7:4-1", "target_tags": "The\\O bibimbap\\O was\\O average\\O ,\\O but\\O the\\O stone\\B bowl\\I was\\O n't\\O even\\O close\\O to\\O sizzling\\O .\\O", "opinion_tags": "The\\O bibimbap\\O was\\O average\\O ,\\O but\\O the\\O stone\\O bowl\\O was\\B n't\\I even\\I close\\I to\\I sizzling\\I .\\O", "sentiment": "negative"}]}, {"id": "DBG#7:5", "sentence": "Too bad I had paid an extra $ 2 for the stone bowl .", "postag": ["RB", "JJ", "PRP", "VBD", "VBN", "DT", "JJ", "$", "CD", "IN", "DT", "NN", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 8, 13, 13, 13, 5, 2], "deprel": ["advmod", "root", "nsubj", "aux", "csubj", "det", "amod", "obj", "nummod", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "DBG#7:5-0", "target_tags": "Too\\O bad\\O I\\O had\\O paid\\O an\\O extra\\O $\\O 2\\O for\\O the\\O stone\\B bowl\\I .\\O", "opinion_tags": "Too\\O bad\\B I\\O had\\O paid\\O an\\O extra\\O $\\O 2\\O for\\O the\\O stone\\O bowl\\O .\\O", "sentiment": "negative"}]}, {"id": "DBG#7:6", "sentence": "The nakgi-bokum was horrible .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "DBG#7:6-0", "target_tags": "The\\O nakgi-bokum\\B was\\O horrible\\O .\\O", "opinion_tags": "The\\O nakgi-bokum\\O was\\O horrible\\B .\\O", "sentiment": "negative"}]}, {"id": "DBG#7:7", "sentence": "Easily the worst stir-fried squid I 've ever tasted .", "postag": ["RB", "DT", "JJS", "JJ", "NN", "PRP", "VBP", "RB", "VBN", "."], "head": [5, 5, 5, 5, 0, 9, 9, 9, 5, 5], "deprel": ["advmod", "det", "amod", "amod", "root", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "DBG#7:7-0", "target_tags": "Easily\\O the\\O worst\\O stir-fried\\B squid\\I I\\O 've\\O ever\\O tasted\\O .\\O", "opinion_tags": "Easily\\O the\\O worst\\B stir-fried\\O squid\\O I\\O 've\\O ever\\O tasted\\O .\\O", "sentiment": "negative"}]}, {"id": "DBG#7:9", "sentence": "The side dishes were passable , and I did get a refill upon request .", "postag": ["DT", "NN", "NNS", "VBD", "JJ", ",", "CC", "PRP", "VBD", "VB", "DT", "NN", "IN", "NN", "."], "head": [3, 3, 5, 5, 0, 10, 10, 10, 10, 5, 12, 10, 14, 10, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "punct", "cc", "nsubj", "aux", "conj", "det", "obj", "case", "obl", "punct"], "triples": [{"uid": "DBG#7:9-0", "target_tags": "The\\O side\\B dishes\\I were\\O passable\\O ,\\O and\\O I\\O did\\O get\\O a\\O refill\\O upon\\O request\\O .\\O", "opinion_tags": "The\\O side\\O dishes\\O were\\O passable\\B ,\\O and\\O I\\O did\\O get\\O a\\O refill\\O upon\\O request\\O .\\O", "sentiment": "positive"}]}, {"id": "DBG#7:10", "sentence": "The real problem I had with this place was the complete lack of service .", "postag": ["DT", "JJ", "NN", "PRP", "VBD", "IN", "DT", "NN", "VBD", "DT", "JJ", "NN", "IN", "NN", "."], "head": [3, 3, 12, 5, 3, 8, 8, 5, 12, 12, 12, 0, 14, 12, 12], "deprel": ["det", "amod", "nsubj", "nsubj", "acl:relcl", "case", "det", "obl", "cop", "det", "amod", "root", "case", "nmod", "punct"], "triples": [{"uid": "DBG#7:10-0", "target_tags": "The\\O real\\O problem\\O I\\O had\\O with\\O this\\O place\\O was\\O the\\O complete\\O lack\\O of\\O service\\B .\\O", "opinion_tags": "The\\O real\\O problem\\O I\\O had\\O with\\O this\\O place\\O was\\O the\\O complete\\O lack\\B of\\I service\\O .\\O", "sentiment": "negative"}]}, {"id": "DBG#7:15", "sentence": "My wife had barely touched that mess of a dish .", "postag": ["PRP$", "NN", "VBD", "RB", "VBN", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["nmod:poss", "nsubj", "aux", "advmod", "root", "det", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "DBG#7:15-0", "target_tags": "My\\O wife\\O had\\O barely\\O touched\\O that\\O mess\\O of\\O a\\O dish\\B .\\O", "opinion_tags": "My\\O wife\\O had\\O barely\\O touched\\O that\\O mess\\B of\\O a\\O dish\\O .\\O", "sentiment": "negative"}]}, {"id": "ADLT#9:4", "sentence": "The wife had the risotto which was amazing .", "postag": ["DT", "NN", "VBD", "DT", "NN", "WDT", "VBD", "JJ", "."], "head": [2, 3, 0, 5, 3, 8, 8, 5, 3], "deprel": ["det", "nsubj", "root", "det", "obj", "nsubj", "cop", "acl:relcl", "punct"], "triples": [{"uid": "ADLT#9:4-0", "target_tags": "The\\O wife\\O had\\O the\\O risotto\\B which\\O was\\O amazing\\O .\\O", "opinion_tags": "The\\O wife\\O had\\O the\\O risotto\\O which\\O was\\O amazing\\B .\\O", "sentiment": "positive"}]}, {"id": "ADLT#9:6", "sentence": "The farro salad and the mashed yukon potatoes were also extremely tasty .", "postag": ["DT", "NN", "NN", "CC", "DT", "JJ", "NN", "NNS", "VBD", "RB", "RB", "JJ", "."], "head": [3, 3, 12, 8, 8, 8, 8, 3, 12, 12, 12, 0, 12], "deprel": ["det", "compound", "nsubj", "cc", "det", "amod", "compound", "conj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "ADLT#9:6-0", "target_tags": "The\\O farro\\B salad\\I and\\O the\\O mashed\\O yukon\\O potatoes\\O were\\O also\\O extremely\\O tasty\\O .\\O", "opinion_tags": "The\\O farro\\O salad\\O and\\O the\\O mashed\\O yukon\\O potatoes\\O were\\O also\\O extremely\\O tasty\\B .\\O", "sentiment": "positive"}, {"uid": "ADLT#9:6-1", "target_tags": "The\\O farro\\O salad\\O and\\O the\\O mashed\\B yukon\\I potatoes\\I were\\O also\\O extremely\\O tasty\\O .\\O", "opinion_tags": "The\\O farro\\O salad\\O and\\O the\\O mashed\\O yukon\\O potatoes\\O were\\O also\\O extremely\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "EVPK#4:0", "sentence": "i love margherita pizza – i looove east village pizza", "postag": ["PRP", "VBP", "NNP", "NN", ",", "PRP", "VBP", "JJ", "NN", "NN"], "head": [2, 0, 4, 2, 2, 7, 2, 9, 10, 7], "deprel": ["nsubj", "root", "compound", "obj", "punct", "nsubj", "parataxis", "amod", "compound", "obj"], "triples": [{"uid": "EVPK#4:0-0", "target_tags": "i\\O love\\O margherita\\O pizza\\O –\\O i\\O looove\\O east\\B village\\I pizza\\I", "opinion_tags": "i\\O love\\B margherita\\O pizza\\O –\\O i\\O looove\\O east\\O village\\O pizza\\O", "sentiment": "positive"}, {"uid": "EVPK#4:0-1", "target_tags": "i\\O love\\O margherita\\B pizza\\I –\\O i\\O looove\\O east\\O village\\O pizza\\O", "opinion_tags": "i\\O love\\O margherita\\O pizza\\O –\\O i\\O looove\\B east\\O village\\O pizza\\O", "sentiment": "positive"}]}, {"id": "EVPK#4:1", "sentence": "Love this place , every time we are in the city this is one of the places we always go .", "postag": ["VBP", "DT", "NN", ",", "DT", "NN", "PRP", "VBP", "IN", "DT", "NN", "DT", "VBZ", "CD", "IN", "DT", "NNS", "PRP", "RB", "VBP", "."], "head": [0, 3, 1, 14, 6, 14, 11, 11, 11, 11, 6, 14, 14, 1, 17, 17, 14, 20, 20, 17, 1], "deprel": ["root", "det", "obj", "punct", "det", "obl:tmod", "nsubj", "cop", "case", "det", "acl:relcl", "nsubj", "cop", "parataxis", "case", "det", "nmod", "nsubj", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "EVPK#4:1-0", "target_tags": "Love\\O this\\O place\\B ,\\O every\\O time\\O we\\O are\\O in\\O the\\O city\\O this\\O is\\O one\\O of\\O the\\O places\\O we\\O always\\O go\\O .\\O", "opinion_tags": "Love\\B this\\O place\\O ,\\O every\\O time\\O we\\O are\\O in\\O the\\O city\\O this\\O is\\O one\\O of\\O the\\O places\\O we\\O always\\O go\\O .\\O", "sentiment": "positive"}]}, {"id": "EVPK#4:2", "sentence": "A quintessential slice of NYC pizza .", "postag": ["DT", "JJ", "NN", "IN", "NNP", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 3], "deprel": ["det", "amod", "root", "case", "compound", "nmod", "punct"], "triples": [{"uid": "EVPK#4:2-0", "target_tags": "A\\O quintessential\\O slice\\B of\\I NYC\\I pizza\\I .\\O", "opinion_tags": "A\\O quintessential\\B slice\\O of\\O NYC\\O pizza\\O .\\O", "sentiment": "neutral"}]}, {"id": "EVPK#4:3", "sentence": "The crust has a great bite and a good chew , the sauce is light with a nice acidity to it , the salt from the cheese is great , really heightens the flavor of all the other components .", "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", "CC", "DT", "JJ", "NN", ",", "DT", "NN", "VBZ", "JJ", "IN", "DT", "JJ", "NN", "IN", "PRP", ",", "DT", "NN", "IN", "DT", "NN", "VBZ", "JJ", ",", "RB", "VBZ", "DT", "NN", "IN", "PDT", "DT", "JJ", "NNS", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 6, 3, 13, 15, 15, 3, 19, 19, 19, 15, 21, 19, 29, 24, 29, 27, 27, 24, 29, 3, 32, 32, 29, 34, 32, 39, 39, 39, 39, 34, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "obj", "cc", "det", "amod", "conj", "punct", "det", "nsubj", "cop", "conj", "case", "det", "amod", "obl", "case", "nmod", "punct", "det", "nsubj", "case", "det", "nmod", "cop", "parataxis", "punct", "advmod", "conj", "det", "obj", "case", "det:predet", "det", "amod", "nmod", "punct"], "triples": [{"uid": "EVPK#4:3-0", "target_tags": "The\\O crust\\B has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "opinion_tags": "The\\O crust\\O has\\O a\\O great\\B bite\\O and\\O a\\O good\\B chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "sentiment": "positive"}, {"uid": "EVPK#4:3-1", "target_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\B is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "opinion_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\B with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "sentiment": "positive"}, {"uid": "EVPK#4:3-2", "target_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\B is\\O great\\O ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "opinion_tags": "The\\O crust\\O has\\O a\\O great\\O bite\\O and\\O a\\O good\\O chew\\O ,\\O the\\O sauce\\O is\\O light\\O with\\O a\\O nice\\O acidity\\O to\\O it\\O ,\\O the\\O salt\\O from\\O the\\O cheese\\O is\\O great\\B ,\\O really\\O heightens\\O the\\O flavor\\O of\\O all\\O the\\O other\\O components\\O .\\O", "sentiment": "positive"}]}, {"id": "EVPK#4:4", "sentence": "Personally I like the marg<PERSON>ita pizza better , but they are all good .", "postag": ["RB", "PRP", "VBP", "DT", "NN", "NN", "JJR", ",", "CC", "PRP", "VBP", "RB", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 3, 13, 13, 13, 13, 13, 3, 3], "deprel": ["advmod", "nsubj", "root", "det", "compound", "obj", "advmod", "punct", "cc", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "EVPK#4:4-0", "target_tags": "Personally\\O I\\O like\\O the\\O margherita\\B pizza\\I better\\O ,\\O but\\O they\\O are\\O all\\O good\\O .\\O", "opinion_tags": "Personally\\O I\\O like\\B the\\O margherita\\O pizza\\O better\\O ,\\O but\\O they\\O are\\O all\\O good\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#6:0", "sentence": "Possibly the Most Romantic Restaurant in the City", "postag": ["RB", "DT", "NNP", "NNP", "NNP", "IN", "DT", "NNP"], "head": [5, 5, 5, 5, 0, 8, 8, 5], "deprel": ["advmod", "det", "amod", "amod", "root", "case", "det", "nmod"], "triples": [{"uid": "Z#6:0-0", "target_tags": "Possibly\\O the\\O Most\\O Romantic\\O Restaurant\\B in\\O the\\O City\\O", "opinion_tags": "Possibly\\O the\\O Most\\O Romantic\\B Restaurant\\O in\\O the\\O City\\O", "sentiment": "positive"}]}, {"id": "Z#6:1", "sentence": "This is undoubtedly my favorite modern Japanese brasserie ( that don ’ t serve sushi ) , and in my opinion , one of the most romantic restaurants in the city !", "postag": ["DT", "VBZ", "RB", "PRP$", "JJ", "JJ", "JJ", "NN", "-LRB-", "WDT", "VBZ", "POS", "NN", "VBP", "NN", "-RRB-", ",", "CC", "IN", "PRP$", "NN", ",", "CD", "IN", "DT", "RBS", "JJ", "NNS", "IN", "DT", "NN", "."], "head": [8, 8, 8, 8, 8, 8, 8, 0, 8, 14, 13, 11, 14, 8, 14, 14, 23, 23, 21, 21, 23, 23, 8, 28, 28, 27, 28, 23, 31, 31, 28, 8], "deprel": ["nsubj", "cop", "advmod", "nmod:poss", "amod", "amod", "amod", "root", "punct", "nsubj", "nmod:poss", "case", "nsubj", "acl:relcl", "obj", "punct", "punct", "cc", "case", "nmod:poss", "nmod", "punct", "conj", "case", "det", "advmod", "amod", "nmod", "case", "det", "nmod", "punct"], "triples": [{"uid": "Z#6:1-0", "target_tags": "This\\O is\\O undoubtedly\\O my\\O favorite\\O modern\\B Japanese\\I brasserie\\I (\\O that\\O don\\O ’\\O t\\O serve\\O sushi\\O )\\O ,\\O and\\O in\\O my\\O opinion\\O ,\\O one\\O of\\O the\\O most\\O romantic\\O restaurants\\O in\\O the\\O city\\O !\\O", "opinion_tags": "This\\O is\\O undoubtedly\\O my\\O favorite\\B modern\\O Japanese\\O brasserie\\O (\\O that\\O don\\O ’\\O t\\O serve\\O sushi\\O )\\O ,\\O and\\O in\\O my\\O opinion\\O ,\\O one\\O of\\O the\\O most\\O romantic\\B restaurants\\O in\\O the\\O city\\O !\\O", "sentiment": "positive"}]}, {"id": "Z#6:2", "sentence": "Not only is it an adventure getting to this somewhat hidden spot , once you enter the unmarked wooden doors , the zen and intimate décor will make you feel like you ’ re no longer in the city .", "postag": ["RB", "RB", "VBZ", "PRP", "DT", "NN", "VBG", "IN", "DT", "RB", "JJ", "NN", ",", "IN", "PRP", "VBP", "DT", "JJ", "JJ", "NNS", ",", "DT", "JJ", "CC", "JJ", "NN", "MD", "VB", "PRP", "VB", "IN", "PRP", "``", "VBP", "RB", "RBR", "IN", "DT", "NN", "."], "head": [2, 6, 6, 6, 6, 28, 6, 12, 12, 11, 12, 7, 28, 16, 16, 28, 20, 20, 20, 16, 28, 26, 26, 25, 23, 28, 28, 0, 28, 28, 34, 34, 36, 36, 36, 30, 39, 39, 36, 28], "deprel": ["advmod", "advmod", "cop", "nsubj", "det", "parataxis", "acl", "case", "det", "advmod", "amod", "obl", "punct", "mark", "nsubj", "advcl", "det", "amod", "amod", "obj", "punct", "det", "amod", "cc", "conj", "nsubj", "aux", "root", "obj", "xcomp", "mark", "nsubj", "punct", "cop", "advmod", "advcl", "case", "det", "obl", "punct"], "triples": [{"uid": "Z#6:2-0", "target_tags": "Not\\O only\\O is\\O it\\O an\\O adventure\\O getting\\O to\\O this\\O somewhat\\O hidden\\O spot\\B ,\\O once\\O you\\O enter\\O the\\O unmarked\\O wooden\\O doors\\O ,\\O the\\O zen\\O and\\O intimate\\O décor\\O will\\O make\\O you\\O feel\\O like\\O you\\O ’\\O re\\O no\\O longer\\O in\\O the\\O city\\O .\\O", "opinion_tags": "Not\\O only\\O is\\O it\\O an\\O adventure\\O getting\\O to\\O this\\O somewhat\\O hidden\\B spot\\O ,\\O once\\O you\\O enter\\O the\\O unmarked\\O wooden\\O doors\\O ,\\O the\\O zen\\O and\\O intimate\\O décor\\O will\\O make\\O you\\O feel\\O like\\O you\\O ’\\O re\\O no\\O longer\\O in\\O the\\O city\\O .\\O", "sentiment": "neutral"}, {"uid": "Z#6:2-1", "target_tags": "Not\\O only\\O is\\O it\\O an\\O adventure\\O getting\\O to\\O this\\O somewhat\\O hidden\\O spot\\O ,\\O once\\O you\\O enter\\O the\\O unmarked\\O wooden\\O doors\\O ,\\O the\\O zen\\O and\\O intimate\\O décor\\B will\\O make\\O you\\O feel\\O like\\O you\\O ’\\O re\\O no\\O longer\\O in\\O the\\O city\\O .\\O", "opinion_tags": "Not\\O only\\O is\\O it\\O an\\O adventure\\O getting\\O to\\O this\\O somewhat\\O hidden\\O spot\\O ,\\O once\\O you\\O enter\\O the\\O unmarked\\O wooden\\O doors\\O ,\\O the\\O zen\\O and\\O intimate\\B décor\\O will\\O make\\O you\\O feel\\O like\\O you\\O ’\\O re\\O no\\O longer\\O in\\O the\\O city\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#6:3", "sentence": "If you ’ re planning to come here , make sure that your date is someone whom you really like since you ’ ll be ushered to private booths where there will be no people or food watching ( choose the ones on the ground level that have glass ceilings so you may see the stars in the sky ! ) .", "postag": ["IN", "PRP", "VBP", "VBP", "VBG", "TO", "VB", "RB", ",", "VB", "JJ", "IN", "PRP$", "NN", "VBZ", "NN", "WP", "PRP", "RB", "VBP", "IN", "PRP", "''", "MD", "VB", "VBN", "IN", "JJ", "NNS", "WRB", "EX", "MD", "VB", "DT", "NNS", "CC", "NN", "VBG", "-LRB-", "VB", "DT", "NNS", "IN", "DT", "NN", "NN", "WDT", "VBP", "NN", "NNS", "RB", "PRP", "MD", "VB", "DT", "NNS", "IN", "DT", "NN", ".", "-RRB-", "."], "head": [5, 5, 5, 5, 10, 7, 5, 7, 5, 0, 10, 16, 14, 16, 16, 11, 20, 20, 20, 16, 26, 26, 22, 26, 26, 16, 29, 29, 26, 33, 33, 33, 29, 35, 33, 37, 35, 35, 40, 35, 42, 40, 46, 46, 46, 40, 48, 46, 50, 48, 54, 54, 54, 40, 56, 54, 59, 59, 54, 40, 40, 10], "deprel": ["mark", "nsubj", "aux", "aux", "advcl", "mark", "xcomp", "advmod", "punct", "root", "xcomp", "mark", "nmod:poss", "nsubj", "cop", "ccomp", "obj", "nsubj", "advmod", "acl:relcl", "mark", "nsubj:pass", "punct", "aux", "aux:pass", "advcl", "case", "amod", "obl", "mark", "expl", "aux", "acl:relcl", "det", "nsubj", "cc", "conj", "acl", "punct", "parataxis", "det", "obj", "case", "det", "compound", "obl", "nsubj", "acl:relcl", "compound", "obj", "advmod", "nsubj", "aux", "advcl", "det", "obj", "case", "det", "obl", "punct", "punct", "punct"], "triples": [{"uid": "Z#6:3-0", "target_tags": "If\\O you\\O ’\\O re\\O planning\\O to\\O come\\O here\\O ,\\O make\\O sure\\O that\\O your\\O date\\O is\\O someone\\O whom\\O you\\O really\\O like\\O since\\O you\\O ’\\O ll\\O be\\O ushered\\O to\\O private\\B booths\\I where\\O there\\O will\\O be\\O no\\O people\\O or\\O food\\O watching\\O (\\O choose\\O the\\O ones\\O on\\O the\\O ground\\O level\\O that\\O have\\O glass\\O ceilings\\O so\\O you\\O may\\O see\\O the\\O stars\\O in\\O the\\O sky\\O !\\O )\\O .\\O", "opinion_tags": "If\\O you\\O ’\\O re\\O planning\\O to\\O come\\O here\\O ,\\O make\\O sure\\O that\\O your\\O date\\O is\\O someone\\O whom\\O you\\O really\\O like\\O since\\O you\\O ’\\O ll\\O be\\O ushered\\B to\\O private\\O booths\\O where\\O there\\O will\\O be\\O no\\O people\\O or\\O food\\O watching\\O (\\O choose\\O the\\O ones\\O on\\O the\\O ground\\O level\\O that\\O have\\O glass\\O ceilings\\O so\\O you\\O may\\O see\\O the\\O stars\\O in\\O the\\O sky\\O !\\O )\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#6:5", "sentence": "My party had the BBE $ 29 fixe prix menu , which was such a wonderful deal since it also came with a flight of sake !", "postag": ["PRP$", "NN", "VBD", "DT", "NNP", "$", "CD", "NN", "NN", "NN", ",", "WDT", "VBD", "PDT", "DT", "JJ", "NN", "IN", "PRP", "RB", "VBD", "IN", "DT", "NN", "IN", "NN", "."], "head": [2, 3, 0, 10, 10, 10, 6, 10, 10, 3, 10, 17, 17, 17, 17, 17, 10, 21, 21, 21, 17, 24, 24, 21, 26, 24, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "compound", "compound", "nummod", "compound", "compound", "obj", "punct", "nsubj", "cop", "det:predet", "det", "amod", "acl:relcl", "mark", "nsubj", "advmod", "advcl", "case", "det", "obl", "case", "nmod", "punct"], "triples": [{"uid": "Z#6:5-0", "target_tags": "My\\O party\\O had\\O the\\O BBE\\B $\\I 29\\I fixe\\I prix\\I menu\\I ,\\O which\\O was\\O such\\O a\\O wonderful\\O deal\\O since\\O it\\O also\\O came\\O with\\O a\\O flight\\O of\\O sake\\O !\\O", "opinion_tags": "My\\O party\\O had\\O the\\O BBE\\O $\\O 29\\O fixe\\O prix\\O menu\\O ,\\O which\\O was\\O such\\O a\\O wonderful\\B deal\\O since\\O it\\O also\\O came\\O with\\O a\\O flight\\O of\\O sake\\O !\\O", "sentiment": "positive"}]}, {"id": "Z#6:7", "sentence": "We started off with a delightful sashimi amuse bouche .", "postag": ["PRP", "VBD", "RP", "IN", "DT", "JJ", "NN", "NN", "NN", "."], "head": [2, 0, 2, 9, 9, 9, 9, 9, 2, 2], "deprel": ["nsubj", "root", "compound:prt", "case", "det", "amod", "compound", "compound", "obl", "punct"], "triples": [{"uid": "Z#6:7-0", "target_tags": "We\\O started\\O off\\O with\\O a\\O delightful\\O sashimi\\B amuse\\I bouche\\I .\\O", "opinion_tags": "We\\O started\\O off\\O with\\O a\\O delightful\\B sashimi\\O amuse\\O bouche\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#6:9", "sentence": "I picked the Grilled Black Cod as my entree , which I absolutely devoured while someone commented that the Grilled Salmon dish was better .", "postag": ["PRP", "VBD", "DT", "VBN", "NNP", "NNP", "IN", "PRP$", "NN", ",", "WDT", "PRP", "RB", "VBD", "IN", "NN", "VBD", "IN", "DT", "JJ", "NN", "NN", "VBD", "JJR", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 14, 14, 14, 14, 9, 17, 17, 14, 24, 22, 22, 22, 24, 24, 17, 2], "deprel": ["nsubj", "root", "det", "amod", "compound", "obj", "case", "nmod:poss", "obl", "punct", "obj", "nsubj", "advmod", "acl:relcl", "mark", "nsubj", "advcl", "mark", "det", "amod", "compound", "nsubj", "cop", "ccomp", "punct"], "triples": [{"uid": "Z#6:9-0", "target_tags": "I\\O picked\\O the\\O Grilled\\B Black\\I Cod\\I as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\O while\\O someone\\O commented\\O that\\O the\\O Grilled\\O Salmon\\O dish\\O was\\O better\\O .\\O", "opinion_tags": "I\\O picked\\O the\\O Grilled\\O Black\\O Cod\\O as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\B while\\O someone\\O commented\\O that\\O the\\O Grilled\\O Salmon\\O dish\\O was\\O better\\O .\\O", "sentiment": "positive"}, {"uid": "Z#6:9-1", "target_tags": "I\\O picked\\O the\\O Grilled\\O Black\\O Cod\\O as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\O while\\O someone\\O commented\\O that\\O the\\O Grilled\\B Salmon\\I dish\\I was\\O better\\O .\\O", "opinion_tags": "I\\O picked\\O the\\O Grilled\\O Black\\O Cod\\O as\\O my\\O entree\\O ,\\O which\\O I\\O absolutely\\O devoured\\O while\\O someone\\O commented\\O that\\O the\\O Grilled\\O Salmon\\O dish\\O was\\O better\\B .\\O", "sentiment": "positive"}]}, {"id": "Z#6:11", "sentence": "The sake ’ s complimented the courses very well and is successfully easing me into the sake world .", "postag": ["DT", "NN", "POS", "VBZ", "VBN", "DT", "NNS", "RB", "RB", "CC", "VBZ", "RB", "VBG", "PRP", "IN", "DT", "NN", "NN", "."], "head": [2, 5, 2, 5, 0, 7, 5, 9, 5, 13, 13, 13, 5, 13, 18, 18, 18, 13, 5], "deprel": ["det", "nsubj", "case", "aux:pass", "root", "det", "obj", "advmod", "advmod", "cc", "aux", "advmod", "conj", "obj", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "Z#6:11-0", "target_tags": "The\\O sake\\B ’\\I s\\I complimented\\O the\\O courses\\O very\\O well\\O and\\O is\\O successfully\\O easing\\O me\\O into\\O the\\O sake\\O world\\O .\\O", "opinion_tags": "The\\O sake\\O ’\\O s\\O complimented\\O the\\O courses\\O very\\O well\\B and\\O is\\O successfully\\O easing\\O me\\O into\\O the\\O sake\\O world\\O .\\O", "sentiment": "positive"}]}, {"id": "Z#6:12", "sentence": "For desserts , we tried the frozen black sesame mousse ( interesting but not extraordinary ) and matcha ( powdered green tea ) and blueberry cheesecake , which was phenomenal .", "postag": ["IN", "NNS", ",", "PRP", "VBD", "DT", "JJ", "JJ", "NN", "NN", "-LRB-", "JJ", "CC", "RB", "JJ", "-RRB-", "CC", "NN", "-LRB-", "VBN", "JJ", "NN", "-RRB-", "CC", "NN", "NN", ",", "WDT", "VBD", "JJ", "."], "head": [2, 5, 2, 5, 0, 10, 10, 10, 10, 5, 10, 10, 15, 15, 12, 12, 18, 10, 22, 22, 22, 18, 22, 26, 26, 10, 26, 30, 30, 26, 5], "deprel": ["case", "obl", "punct", "nsubj", "root", "det", "amod", "amod", "compound", "obj", "punct", "amod", "cc", "advmod", "conj", "punct", "cc", "conj", "punct", "amod", "amod", "appos", "punct", "cc", "compound", "conj", "punct", "nsubj", "cop", "acl:relcl", "punct"], "triples": [{"uid": "Z#6:12-0", "target_tags": "For\\O desserts\\O ,\\O we\\O tried\\O the\\O frozen\\B black\\I sesame\\I mousse\\I (\\O interesting\\O but\\O not\\O extraordinary\\O )\\O and\\O matcha\\O (\\O powdered\\O green\\O tea\\O )\\O and\\O blueberry\\O cheesecake\\O ,\\O which\\O was\\O phenomenal\\O .\\O", "opinion_tags": "For\\O desserts\\O ,\\O we\\O tried\\O the\\O frozen\\O black\\O sesame\\O mousse\\O (\\O interesting\\B but\\O not\\O extraordinary\\B )\\O and\\O matcha\\O (\\O powdered\\O green\\O tea\\O )\\O and\\O blueberry\\O cheesecake\\O ,\\O which\\O was\\O phenomenal\\O .\\O", "sentiment": "neutral"}, {"uid": "Z#6:12-1", "target_tags": "For\\O desserts\\O ,\\O we\\O tried\\O the\\O frozen\\O black\\O sesame\\O mousse\\O (\\O interesting\\O but\\O not\\O extraordinary\\O )\\O and\\O matcha\\B (\\I powdered\\I green\\I tea\\I )\\I and\\I blueberry\\I cheesecake\\I ,\\O which\\O was\\O phenomenal\\O .\\O", "opinion_tags": "For\\O desserts\\O ,\\O we\\O tried\\O the\\O frozen\\O black\\O sesame\\O mousse\\O (\\O interesting\\O but\\O not\\O extraordinary\\O )\\O and\\O matcha\\O (\\O powdered\\O green\\O tea\\O )\\O and\\O blueberry\\O cheesecake\\O ,\\O which\\O was\\O phenomenal\\B .\\O", "sentiment": "positive"}]}, {"id": "Z#6:13", "sentence": "Maybe it was the great company ( I had friends visiting from Philly – yes , it was not a date this time ) or the super reasonable price point , but I just can ’ t say enough good things about this brasserie .", "postag": ["RB", "PRP", "VBD", "DT", "JJ", "NN", "-LRB-", "PRP", "VBD", "NNS", "VBG", "IN", "NNP", ",", "UH", ",", "PRP", "VBD", "RB", "DT", "NN", "DT", "NN", "-RRB-", "CC", "DT", "RB", "JJ", "NN", "NN", ",", "CC", "PRP", "RB", "MD", "``", "RB", "VB", "JJ", "JJ", "NNS", "IN", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 9, 10, 13, 11, 6, 21, 21, 21, 21, 21, 21, 6, 23, 21, 21, 30, 30, 28, 30, 30, 21, 38, 38, 38, 38, 38, 38, 38, 6, 41, 41, 38, 44, 44, 41, 6], "deprel": ["advmod", "nsubj", "cop", "det", "amod", "root", "punct", "nsubj", "parataxis", "obj", "acl", "case", "obl", "punct", "discourse", "punct", "nsubj", "cop", "advmod", "det", "parataxis", "det", "obl:tmod", "punct", "cc", "det", "advmod", "amod", "compound", "conj", "punct", "cc", "nsubj", "advmod", "aux", "punct", "advmod", "conj", "amod", "amod", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "Z#6:13-0", "target_tags": "Maybe\\O it\\O was\\O the\\O great\\O company\\O (\\O I\\O had\\O friends\\O visiting\\O from\\O Philly\\O –\\O yes\\O ,\\O it\\O was\\O not\\O a\\O date\\O this\\O time\\O )\\O or\\O the\\O super\\O reasonable\\O price\\O point\\O ,\\O but\\O I\\O just\\O can\\O ’\\O t\\O say\\O enough\\O good\\O things\\O about\\O this\\O brasserie\\B .\\O", "opinion_tags": "Maybe\\O it\\O was\\O the\\O great\\O company\\O (\\O I\\O had\\O friends\\O visiting\\O from\\O Philly\\O –\\O yes\\O ,\\O it\\O was\\O not\\O a\\O date\\O this\\O time\\O )\\O or\\O the\\O super\\O reasonable\\O price\\O point\\O ,\\O but\\O I\\O just\\O can\\O ’\\O t\\O say\\O enough\\O good\\B things\\O about\\O this\\O brasserie\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#6:1", "sentence": "The service leaves much to be desired , from feeling like you are rushed the place your order , to being ignored the rest of the night .", "postag": ["DT", "NN", "VBZ", "JJ", "TO", "VB", "VBN", ",", "IN", "VBG", "IN", "PRP", "VBP", "VBN", "DT", "NN", "PRP$", "NN", ",", "IN", "VBG", "VBN", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 3, 0, 3, 7, 7, 4, 10, 10, 3, 14, 14, 14, 10, 16, 14, 18, 14, 14, 22, 22, 14, 24, 22, 27, 27, 24, 3], "deprel": ["det", "nsubj", "root", "obj", "mark", "aux:pass", "acl", "punct", "mark", "advcl", "mark", "nsubj:pass", "aux:pass", "advcl", "det", "obj", "nmod:poss", "obj", "punct", "mark", "aux:pass", "advcl", "det", "obj", "case", "det", "nmod", "punct"], "triples": [{"uid": "CLF#6:1-0", "target_tags": "The\\O service\\B leaves\\O much\\O to\\O be\\O desired\\O ,\\O from\\O feeling\\O like\\O you\\O are\\O rushed\\O the\\O place\\O your\\O order\\O ,\\O to\\O being\\O ignored\\O the\\O rest\\O of\\O the\\O night\\O .\\O", "opinion_tags": "The\\O service\\O leaves\\B much\\I to\\I be\\I desired\\I ,\\O from\\O feeling\\O like\\O you\\O are\\O rushed\\O the\\O place\\O your\\O order\\O ,\\O to\\O being\\O ignored\\O the\\O rest\\O of\\O the\\O night\\O .\\O", "sentiment": "negative"}]}, {"id": "CLF#6:5", "sentence": "They are extremely rude , not even apologizing for the horrible service we got and handing us a bill well over $ 500 for some drinks adn their pita bread !", "postag": ["PRP", "VBP", "RB", "JJ", ",", "RB", "RB", "VBG", "IN", "DT", "JJ", "NN", "PRP", "VBD", "CC", "VBG", "PRP", "DT", "NN", "RB", "RB", "$", "CD", "IN", "DT", "NNS", "IN", "PRP$", "NN", "NN", "."], "head": [4, 4, 4, 0, 4, 8, 8, 4, 12, 12, 12, 8, 14, 12, 16, 8, 16, 19, 16, 22, 22, 16, 22, 26, 26, 22, 30, 30, 30, 26, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct", "advmod", "advmod", "advcl", "case", "det", "amod", "obl", "nsubj", "acl:relcl", "cc", "conj", "i<PERSON><PERSON>", "det", "obj", "advmod", "advmod", "obj", "nummod", "case", "det", "nmod", "cc", "nmod:poss", "compound", "conj", "punct"], "triples": [{"uid": "CLF#6:5-0", "target_tags": "They\\O are\\O extremely\\O rude\\O ,\\O not\\O even\\O apologizing\\O for\\O the\\O horrible\\O service\\B we\\O got\\O and\\O handing\\O us\\O a\\O bill\\O well\\O over\\O $\\O 500\\O for\\O some\\O drinks\\O adn\\O their\\O pita\\O bread\\O !\\O", "opinion_tags": "They\\O are\\O extremely\\O rude\\O ,\\O not\\O even\\O apologizing\\O for\\O the\\O horrible\\B service\\O we\\O got\\O and\\O handing\\O us\\O a\\O bill\\O well\\O over\\O $\\O 500\\O for\\O some\\O drinks\\O adn\\O their\\O pita\\O bread\\O !\\O", "sentiment": "negative"}]}, {"id": "DBG#6:0", "sentence": "Great <PERSON><PERSON><PERSON>", "postag": ["JJ", "NN", "NN"], "head": [3, 3, 0], "deprel": ["amod", "compound", "root"], "triples": [{"uid": "DBG#6:0-0", "target_tags": "Great\\O Shabu\\B Shabu\\I", "opinion_tags": "Great\\B Shabu\\O Shabu\\O", "sentiment": "positive"}]}, {"id": "DBG#6:4", "sentence": "I tried a couple other dishes but was n't too impressed .", "postag": ["PRP", "VBD", "DT", "NN", "JJ", "NNS", "CC", "VBD", "RB", "RB", "JJ", "."], "head": [2, 0, 4, 5, 6, 2, 11, 11, 11, 11, 2, 2], "deprel": ["nsubj", "root", "det", "obl:npmod", "amod", "obj", "cc", "cop", "advmod", "advmod", "conj", "punct"], "triples": [{"uid": "DBG#6:4-0", "target_tags": "I\\O tried\\O a\\O couple\\O other\\O dishes\\B but\\O was\\O n't\\O too\\O impressed\\O .\\O", "opinion_tags": "I\\O tried\\O a\\O couple\\O other\\O dishes\\O but\\O was\\B n't\\I too\\I impressed\\I .\\O", "sentiment": "neutral"}]}, {"id": "DBG#6:7", "sentence": "The meat is fresh , the sauces are great , you get kimchi and a salad free with your meal and service is good too .", "postag": ["DT", "NN", "VBZ", "JJ", ",", "DT", "NNS", "VBP", "JJ", ",", "PRP", "VBP", "NN", "CC", "DT", "NN", "JJ", "IN", "PRP$", "NN", "CC", "NN", "VBZ", "JJ", "RB", "."], "head": [2, 4, 4, 0, 4, 7, 9, 9, 4, 4, 12, 4, 12, 16, 16, 13, 16, 20, 20, 17, 22, 20, 24, 12, 24, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "list", "punct", "nsubj", "parataxis", "obj", "cc", "det", "conj", "amod", "case", "nmod:poss", "obl", "cc", "conj", "cop", "conj", "advmod", "punct"], "triples": [{"uid": "DBG#6:7-0", "target_tags": "The\\O meat\\B is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\O with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "opinion_tags": "The\\O meat\\O is\\O fresh\\B ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\O with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "sentiment": "positive"}, {"uid": "DBG#6:7-1", "target_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\B are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\O with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "opinion_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\B ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\O with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "sentiment": "positive"}, {"uid": "DBG#6:7-2", "target_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\O with\\O your\\O meal\\O and\\O service\\B is\\O good\\O too\\O .\\O", "opinion_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\O with\\O your\\O meal\\O and\\O service\\O is\\O good\\B too\\O .\\O", "sentiment": "positive"}, {"uid": "DBG#6:7-3", "target_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\B and\\O a\\O salad\\O free\\O with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "opinion_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\B with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "sentiment": "positive"}, {"uid": "DBG#6:7-4", "target_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\B free\\O with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "opinion_tags": "The\\O meat\\O is\\O fresh\\O ,\\O the\\O sauces\\O are\\O great\\O ,\\O you\\O get\\O kimchi\\O and\\O a\\O salad\\O free\\B with\\O your\\O meal\\O and\\O service\\O is\\O good\\O too\\O .\\O", "sentiment": "positive"}]}, {"id": "DBG#1:0", "sentence": "<PERSON><PERSON><PERSON> gives <PERSON><PERSON> the right one-two punch of classic Korean food and fusion twists like pork belly tacos .", "postag": ["NNP", "VBZ", "NNP", "DT", "JJ", "JJ", "NN", "IN", "JJ", "JJ", "NN", "CC", "NN", "NNS", "IN", "NN", "NN", "NNS", "."], "head": [2, 0, 2, 7, 7, 7, 2, 11, 11, 11, 7, 14, 14, 11, 18, 18, 18, 11, 2], "deprel": ["nsubj", "root", "i<PERSON><PERSON>", "det", "amod", "amod", "obj", "case", "amod", "amod", "nmod", "cc", "compound", "conj", "case", "compound", "compound", "nmod", "punct"], "triples": [{"uid": "DBG#1:0-0", "target_tags": "Dokebi\\O gives\\O Williamsburg\\O the\\O right\\O one-two\\O punch\\O of\\O classic\\O Korean\\B food\\I and\\O fusion\\O twists\\O like\\O pork\\O belly\\O tacos\\O .\\O", "opinion_tags": "Dokebi\\O gives\\O Williamsburg\\O the\\O right\\O one-two\\O punch\\O of\\O classic\\B Korean\\O food\\O and\\O fusion\\O twists\\O like\\O pork\\O belly\\O tacos\\O .\\O", "sentiment": "positive"}, {"uid": "DBG#1:0-1", "target_tags": "Dokebi\\O gives\\O Williamsburg\\O the\\O right\\O one-two\\O punch\\O of\\O classic\\O Korean\\O food\\O and\\O fusion\\B twists\\I like\\O pork\\O belly\\O tacos\\O .\\O", "opinion_tags": "Dokebi\\O gives\\O Williamsburg\\O the\\O right\\O one-two\\O punch\\O of\\O classic\\B Korean\\O food\\O and\\O fusion\\O twists\\O like\\O pork\\O belly\\O tacos\\O .\\O", "sentiment": "positive"}, {"uid": "DBG#1:0-2", "target_tags": "Dokebi\\O gives\\O Williamsburg\\O the\\O right\\O one-two\\O punch\\O of\\O classic\\O Korean\\O food\\O and\\O fusion\\O twists\\O like\\O pork\\B belly\\I tacos\\I .\\O", "opinion_tags": "Dokebi\\O gives\\O Williamsburg\\O the\\O right\\O one-two\\O punch\\O of\\O classic\\B Korean\\O food\\O and\\O fusion\\O twists\\O like\\O pork\\O belly\\O tacos\\O .\\O", "sentiment": "positive"}]}, {"id": "BHD#1:0", "sentence": "The hot dogs are good , yes , but the reason to get over here is the fantastic pork croquette sandwich , perfect on its supermarket squishy bun .", "postag": ["DT", "JJ", "NNS", "VBP", "JJ", ",", "UH", ",", "CC", "DT", "NN", "TO", "VB", "IN", "RB", "VBZ", "DT", "JJ", "NN", "NN", "NN", ",", "JJ", "IN", "PRP$", "NN", "JJ", "NN", "."], "head": [3, 3, 5, 5, 0, 21, 21, 21, 21, 11, 21, 13, 11, 15, 13, 21, 21, 21, 20, 21, 5, 21, 21, 28, 28, 28, 28, 23, 5], "deprel": ["det", "amod", "nsubj", "cop", "root", "punct", "discourse", "punct", "cc", "det", "nsubj", "mark", "acl", "case", "obl", "cop", "det", "amod", "compound", "compound", "conj", "punct", "amod", "case", "nmod:poss", "compound", "amod", "obl", "punct"], "triples": [{"uid": "BHD#1:0-0", "target_tags": "The\\O hot\\B dogs\\I are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\B ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "sentiment": "positive"}, {"uid": "BHD#1:0-1", "target_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\B croquette\\I sandwich\\I ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\B pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "sentiment": "positive"}, {"uid": "BHD#1:0-2", "target_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\O on\\O its\\O supermarket\\O squishy\\O bun\\B .\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O good\\O ,\\O yes\\O ,\\O but\\O the\\O reason\\O to\\O get\\O over\\O here\\O is\\O the\\O fantastic\\O pork\\O croquette\\O sandwich\\O ,\\O perfect\\B on\\O its\\O supermarket\\O squishy\\O bun\\O .\\O", "sentiment": "positive"}]}, {"id": "TR#5:2", "sentence": "The food tasted very good .", "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 3, 0, 5, 3, 3], "deprel": ["det", "nsubj", "root", "advmod", "xcomp", "punct"], "triples": [{"uid": "TR#5:2-0", "target_tags": "The\\O food\\B tasted\\O very\\O good\\O .\\O", "opinion_tags": "The\\O food\\O tasted\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "TR#5:3", "sentence": "The family seafood entree was very good .", "postag": ["DT", "NN", "NN", "NN", "VBD", "RB", "JJ", "."], "head": [4, 4, 4, 7, 7, 7, 0, 7], "deprel": ["det", "compound", "compound", "nsubj", "cop", "advmod", "root", "punct"], "triples": [{"uid": "TR#5:3-0", "target_tags": "The\\O family\\B seafood\\I entree\\I was\\O very\\O good\\O .\\O", "opinion_tags": "The\\O family\\O seafood\\O entree\\O was\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "TR#5:4", "sentence": "The main entree was also very good .", "postag": ["DT", "JJ", "NN", "VBD", "RB", "RB", "JJ", "."], "head": [3, 3, 7, 7, 7, 7, 0, 7], "deprel": ["det", "amod", "nsubj", "cop", "advmod", "advmod", "root", "punct"], "triples": [{"uid": "TR#5:4-0", "target_tags": "The\\O main\\B entree\\I was\\O also\\O very\\O good\\O .\\O", "opinion_tags": "The\\O main\\O entree\\O was\\O also\\O very\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "TR#5:6", "sentence": "Price is high but the food is good , so I would come back again .", "postag": ["NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "JJ", ",", "RB", "PRP", "MD", "VB", "RB", "RB", "."], "head": [3, 3, 0, 8, 6, 8, 8, 3, 13, 13, 13, 13, 3, 13, 13, 3], "deprel": ["nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct", "advmod", "nsubj", "aux", "conj", "advmod", "advmod", "punct"], "triples": [{"uid": "TR#5:6-0", "target_tags": "Price\\O is\\O high\\O but\\O the\\O food\\B is\\O good\\O ,\\O so\\O I\\O would\\O come\\O back\\O again\\O .\\O", "opinion_tags": "Price\\O is\\O high\\O but\\O the\\O food\\O is\\O good\\B ,\\O so\\O I\\O would\\O come\\O back\\O again\\O .\\O", "sentiment": "negative"}]}, {"id": "Z#7:2", "sentence": "This place has totally weird decor , stairs going up with mirrored walls - I am surprised how no one yet broke their head or fall off the stairs - mirrored walls make you dizzy and delusional ...", "postag": ["DT", "NN", "VBZ", "RB", "JJ", "NN", ",", "NNS", "VBG", "RP", "IN", "JJ", "NNS", ",", "PRP", "VBP", "JJ", "WRB", "DT", "NN", "RB", "VBD", "PRP$", "NN", "CC", "VB", "IN", "DT", "NNS", "HYPH", "VBN", "NNS", "VBP", "PRP", "JJ", "CC", "JJ", "."], "head": [2, 3, 0, 5, 6, 3, 8, 6, 8, 9, 13, 13, 9, 3, 17, 17, 3, 22, 20, 22, 22, 17, 24, 22, 26, 22, 32, 32, 31, 31, 32, 26, 17, 33, 33, 37, 35, 3], "deprel": ["det", "nsubj", "root", "advmod", "amod", "obj", "punct", "appos", "acl", "compound:prt", "case", "amod", "obl", "punct", "nsubj", "cop", "parataxis", "mark", "det", "nsubj", "advmod", "ccomp", "nmod:poss", "obj", "cc", "conj", "case", "det", "compound", "punct", "amod", "obl", "conj", "obj", "xcomp", "cc", "conj", "punct"], "triples": [{"uid": "Z#7:2-0", "target_tags": "This\\O place\\O has\\O totally\\O weird\\O decor\\B ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\O walls\\O make\\O you\\O dizzy\\O and\\O delusional\\O ...\\O", "opinion_tags": "This\\O place\\O has\\O totally\\O weird\\B decor\\O ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\O walls\\O make\\O you\\O dizzy\\O and\\O delusional\\O ...\\O", "sentiment": "negative"}, {"uid": "Z#7:2-1", "target_tags": "This\\O place\\O has\\O totally\\O weird\\O decor\\O ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\B walls\\I make\\O you\\O dizzy\\O and\\O delusional\\O ...\\O", "opinion_tags": "This\\O place\\O has\\O totally\\O weird\\O decor\\O ,\\O stairs\\O going\\O up\\O with\\O mirrored\\O walls\\O -\\O I\\O am\\O surprised\\O how\\O no\\O one\\O yet\\O broke\\O their\\O head\\O or\\O fall\\O off\\O the\\O stairs\\O -\\O mirrored\\O walls\\O make\\O you\\O dizzy\\B and\\O delusional\\B ...\\O", "sentiment": "negative"}]}, {"id": "Z#7:3", "sentence": "This place is not inviting and the food is totally weird .", "postag": ["DT", "NN", "VBZ", "RB", "VBG", "CC", "DT", "NN", "VBZ", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 11, 8, 11, 11, 11, 5, 5], "deprel": ["det", "nsubj", "aux", "advmod", "root", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "Z#7:3-0", "target_tags": "This\\O place\\B is\\O not\\O inviting\\O and\\O the\\O food\\O is\\O totally\\O weird\\O .\\O", "opinion_tags": "This\\O place\\O is\\O not\\B inviting\\I and\\O the\\O food\\O is\\O totally\\O weird\\O .\\O", "sentiment": "negative"}, {"uid": "Z#7:3-1", "target_tags": "This\\O place\\O is\\O not\\O inviting\\O and\\O the\\O food\\B is\\O totally\\O weird\\O .\\O", "opinion_tags": "This\\O place\\O is\\O not\\O inviting\\O and\\O the\\O food\\O is\\O totally\\O weird\\B .\\O", "sentiment": "negative"}]}, {"id": "Z#7:4", "sentence": "The concept of japanese tapas is newly created and clearly does n't work .", "postag": ["DT", "NN", "IN", "JJ", "NN", "VBZ", "RB", "VBN", "CC", "RB", "VBZ", "RB", "VB", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 13, 13, 13, 13, 8, 8], "deprel": ["det", "nsubj:pass", "case", "amod", "nmod", "aux:pass", "advmod", "root", "cc", "advmod", "aux", "advmod", "conj", "punct"], "triples": [{"uid": "Z#7:4-0", "target_tags": "The\\O concept\\O of\\O japanese\\B tapas\\I is\\O newly\\O created\\O and\\O clearly\\O does\\O n't\\O work\\O .\\O", "opinion_tags": "The\\O concept\\O of\\O japanese\\O tapas\\O is\\O newly\\O created\\O and\\O clearly\\O does\\B n't\\I work\\I .\\O", "sentiment": "negative"}]}, {"id": "Z#7:5", "sentence": "The food they serve is not comforting , not appetizing and uncooked .", "postag": ["DT", "NN", "PRP", "VBP", "VBZ", "RB", "JJ", ",", "RB", "JJ", "CC", "JJ", "."], "head": [2, 7, 4, 2, 7, 7, 0, 10, 10, 7, 12, 10, 7], "deprel": ["det", "nsubj", "nsubj", "acl:relcl", "cop", "advmod", "root", "punct", "advmod", "conj", "cc", "conj", "punct"], "triples": [{"uid": "Z#7:5-0", "target_tags": "The\\O food\\B they\\O serve\\O is\\O not\\O comforting\\O ,\\O not\\O appetizing\\O and\\O uncooked\\O .\\O", "opinion_tags": "The\\O food\\O they\\O serve\\O is\\O not\\B comforting\\I ,\\O not\\B appetizing\\I and\\O uncooked\\B .\\O", "sentiment": "negative"}]}, {"id": "Z#10:0", "sentence": "Good Food", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "Z#10:0-0", "target_tags": "Good\\O Food\\B", "opinion_tags": "Good\\B Food\\O", "sentiment": "positive"}]}, {"id": "Z#10:2", "sentence": "The food was great and tasty , but the sitting space was too small , I do n't like being cramp in a corner .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", ",", "CC", "DT", "NN", "NN", "VBD", "RB", "JJ", ",", "PRP", "VBP", "RB", "VB", "VBG", "NN", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 14, 14, 11, 11, 14, 14, 14, 4, 4, 19, 19, 19, 14, 21, 19, 24, 24, 21, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct", "cc", "det", "amod", "nsubj", "cop", "advmod", "conj", "punct", "nsubj", "aux", "advmod", "parataxis", "cop", "xcomp", "case", "det", "nmod", "punct"], "triples": [{"uid": "Z#10:2-0", "target_tags": "The\\O food\\B was\\O great\\O and\\O tasty\\O ,\\O but\\O the\\O sitting\\O space\\O was\\O too\\O small\\O ,\\O I\\O do\\O n't\\O like\\O being\\O cramp\\O in\\O a\\O corner\\O .\\O", "opinion_tags": "The\\O food\\O was\\O great\\B and\\O tasty\\B ,\\O but\\O the\\O sitting\\O space\\O was\\O too\\O small\\O ,\\O I\\O do\\O n't\\O like\\O being\\O cramp\\O in\\O a\\O corner\\O .\\O", "sentiment": "positive"}, {"uid": "Z#10:2-1", "target_tags": "The\\O food\\O was\\O great\\O and\\O tasty\\O ,\\O but\\O the\\O sitting\\B space\\I was\\O too\\O small\\O ,\\O I\\O do\\O n't\\O like\\O being\\O cramp\\O in\\O a\\O corner\\O .\\O", "opinion_tags": "The\\O food\\O was\\O great\\O and\\O tasty\\O ,\\O but\\O the\\O sitting\\O space\\O was\\O too\\B small\\I ,\\O I\\O do\\O n't\\O like\\O being\\O cramp\\O in\\O a\\O corner\\O .\\O", "sentiment": "negative"}]}, {"id": "Z#10:3", "sentence": "Over all it was a very nice romantic place .", "postag": ["IN", "DT", "PRP", "VBD", "DT", "RB", "JJ", "JJ", "NN", "."], "head": [2, 9, 9, 9, 9, 7, 9, 9, 0, 9], "deprel": ["case", "obl", "nsubj", "cop", "det", "advmod", "amod", "amod", "root", "punct"], "triples": [{"uid": "Z#10:3-0", "target_tags": "Over\\O all\\O it\\O was\\O a\\O very\\O nice\\O romantic\\O place\\B .\\O", "opinion_tags": "Over\\O all\\O it\\O was\\O a\\O very\\O nice\\B romantic\\B place\\O .\\O", "sentiment": "positive"}]}, {"id": "P#7:1", "sentence": "A coworker and I tried Pacifico after work a few Fridays and loved it .", "postag": ["DT", "NN", "CC", "PRP", "VBD", "NNP", "IN", "NN", "DT", "JJ", "NNPS", "CC", "VBD", "PRP", "."], "head": [2, 5, 4, 2, 0, 5, 8, 5, 11, 11, 5, 13, 5, 13, 5], "deprel": ["det", "nsubj", "cc", "conj", "root", "obj", "case", "obl", "det", "amod", "obl:tmod", "cc", "conj", "obj", "punct"], "triples": [{"uid": "P#7:1-0", "target_tags": "A\\O coworker\\O and\\O I\\O tried\\O Pacifico\\B after\\O work\\O a\\O few\\O Fridays\\O and\\O loved\\O it\\O .\\O", "opinion_tags": "A\\O coworker\\O and\\O I\\O tried\\O Pacifico\\O after\\O work\\O a\\O few\\O Fridays\\O and\\O loved\\B it\\O .\\O", "sentiment": "positive"}]}, {"id": "P#7:2", "sentence": "The atmosphere was great .", "postag": ["DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "P#7:2-0", "target_tags": "The\\O atmosphere\\B was\\O great\\O .\\O", "opinion_tags": "The\\O atmosphere\\O was\\O great\\B .\\O", "sentiment": "positive"}]}, {"id": "P#7:3", "sentence": "The food we ordered was excellent , although I would n't say the margaritas were anything to write home about .", "postag": ["DT", "NN", "PRP", "VBD", "VBD", "JJ", ",", "IN", "PRP", "MD", "RB", "VB", "DT", "NNS", "VBD", "NN", "TO", "VB", "RB", "IN", "."], "head": [2, 6, 4, 2, 6, 0, 6, 12, 12, 12, 12, 6, 14, 16, 16, 12, 18, 16, 18, 18, 6], "deprel": ["det", "nsubj", "nsubj", "acl:relcl", "cop", "root", "punct", "mark", "nsubj", "aux", "advmod", "advcl", "det", "nsubj", "cop", "ccomp", "mark", "acl", "advmod", "obl", "punct"], "triples": [{"uid": "P#7:3-0", "target_tags": "The\\O food\\B we\\O ordered\\O was\\O excellent\\O ,\\O although\\O I\\O would\\O n't\\O say\\O the\\O margaritas\\O were\\O anything\\O to\\O write\\O home\\O about\\O .\\O", "opinion_tags": "The\\O food\\O we\\O ordered\\O was\\O excellent\\B ,\\O although\\O I\\O would\\O n't\\O say\\O the\\O margaritas\\O were\\O anything\\O to\\O write\\O home\\O about\\O .\\O", "sentiment": "positive"}]}, {"id": "P#7:4", "sentence": "Our waitress was n't mean , but not especially warm or attentive either .", "postag": ["PRP$", "NN", "VBD", "RB", "JJ", ",", "CC", "RB", "RB", "JJ", "CC", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 10, 10, 10, 10, 5, 12, 10, 10, 5], "deprel": ["nmod:poss", "nsubj", "cop", "advmod", "root", "punct", "cc", "advmod", "advmod", "conj", "cc", "conj", "advmod", "punct"], "triples": [{"uid": "P#7:4-0", "target_tags": "Our\\O waitress\\B was\\O n't\\O mean\\O ,\\O but\\O not\\O especially\\O warm\\O or\\O attentive\\O either\\O .\\O", "opinion_tags": "Our\\O waitress\\O was\\B n't\\I mean\\I ,\\O but\\O not\\B especially\\I warm\\I or\\I attentive\\I either\\O .\\O", "sentiment": "neutral"}]}, {"id": "P#7:5", "sentence": "I must say I am surprised by the bad reviews of the restaurant earlier in the year , though .", "postag": ["PRP", "MD", "VB", "PRP", "VBP", "JJ", "IN", "DT", "JJ", "NNS", "IN", "DT", "NN", "RBR", "IN", "DT", "NN", ",", "RB", "."], "head": [3, 3, 0, 6, 6, 3, 10, 10, 10, 6, 13, 13, 10, 6, 17, 17, 6, 6, 6, 3], "deprel": ["nsubj", "aux", "root", "nsubj", "cop", "ccomp", "case", "det", "amod", "obl", "case", "det", "nmod", "advmod", "case", "det", "obl", "punct", "advmod", "punct"], "triples": [{"uid": "P#7:5-0", "target_tags": "I\\O must\\O say\\O I\\O am\\O surprised\\O by\\O the\\O bad\\O reviews\\O of\\O the\\O restaurant\\B earlier\\O in\\O the\\O year\\O ,\\O though\\O .\\O", "opinion_tags": "I\\O must\\O say\\O I\\O am\\O surprised\\O by\\O the\\O bad\\B reviews\\O of\\O the\\O restaurant\\O earlier\\O in\\O the\\O year\\O ,\\O though\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#4:3", "sentence": "The servers at Flatbush Farm appear to have perfected that ghastly technique of making you feel guilty and ashamed for deigning to attract their attention .", "postag": ["DT", "NNS", "IN", "NNP", "NNP", "VBP", "TO", "VB", "VBN", "DT", "JJ", "NN", "IN", "VBG", "PRP", "VB", "JJ", "CC", "JJ", "IN", "VBG", "TO", "VB", "PRP$", "NN", "."], "head": [2, 6, 5, 5, 2, 0, 9, 9, 6, 12, 12, 9, 14, 12, 14, 14, 16, 19, 17, 21, 16, 23, 21, 25, 23, 6], "deprel": ["det", "nsubj", "case", "compound", "nmod", "root", "mark", "aux", "xcomp", "det", "amod", "obj", "mark", "acl", "obj", "xcomp", "xcomp", "cc", "conj", "mark", "advcl", "mark", "xcomp", "nmod:poss", "obj", "punct"], "triples": [{"uid": "FF#4:3-0", "target_tags": "The\\O servers\\B at\\O Flatbush\\O Farm\\O appear\\O to\\O have\\O perfected\\O that\\O ghastly\\O technique\\O of\\O making\\O you\\O feel\\O guilty\\O and\\O ashamed\\O for\\O deigning\\O to\\O attract\\O their\\O attention\\O .\\O", "opinion_tags": "The\\O servers\\O at\\O Flatbush\\O Farm\\O appear\\O to\\O have\\O perfected\\B that\\O ghastly\\O technique\\O of\\O making\\O you\\O feel\\O guilty\\O and\\O ashamed\\O for\\O deigning\\O to\\O attract\\O their\\O attention\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#4:7", "sentence": "A different server enhanced the fun , dumping our entrees in front of us halfway through our appetizer ( which was delicious ) .", "postag": ["DT", "JJ", "NN", "VBD", "DT", "NN", ",", "VBG", "PRP$", "NNS", "IN", "NN", "IN", "PRP", "RB", "IN", "PRP$", "NN", "-LRB-", "WDT", "VBD", "JJ", "-RRB-", "."], "head": [3, 3, 4, 0, 6, 4, 8, 4, 10, 8, 12, 8, 14, 12, 18, 18, 18, 8, 22, 22, 22, 18, 22, 4], "deprel": ["det", "amod", "nsubj", "root", "det", "obj", "punct", "advcl", "nmod:poss", "obj", "case", "obl", "case", "nmod", "advmod", "case", "nmod:poss", "obl", "punct", "nsubj", "cop", "acl:relcl", "punct", "punct"], "triples": [{"uid": "FF#4:7-0", "target_tags": "A\\O different\\O server\\B enhanced\\O the\\O fun\\O ,\\O dumping\\O our\\O entrees\\O in\\O front\\O of\\O us\\O halfway\\O through\\O our\\O appetizer\\O (\\O which\\O was\\O delicious\\O )\\O .\\O", "opinion_tags": "A\\O different\\O server\\O enhanced\\B the\\O fun\\O ,\\O dumping\\O our\\O entrees\\O in\\O front\\O of\\O us\\O halfway\\O through\\O our\\O appetizer\\O (\\O which\\O was\\O delicious\\O )\\O .\\O", "sentiment": "negative"}, {"uid": "FF#4:7-1", "target_tags": "A\\O different\\O server\\O enhanced\\O the\\O fun\\O ,\\O dumping\\O our\\O entrees\\O in\\O front\\O of\\O us\\O halfway\\O through\\O our\\O appetizer\\B (\\O which\\O was\\O delicious\\O )\\O .\\O", "opinion_tags": "A\\O different\\O server\\O enhanced\\O the\\O fun\\O ,\\O dumping\\O our\\O entrees\\O in\\O front\\O of\\O us\\O halfway\\O through\\O our\\O appetizer\\O (\\O which\\O was\\O delicious\\B )\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#4:8", "sentence": "Overall the food quality was pretty good , though I hear the salmon is much better when it has n't sat cooling in front of the guest .", "postag": ["RB", "DT", "NN", "NN", "VBD", "RB", "JJ", ",", "IN", "PRP", "VBP", "DT", "NN", "VBZ", "RB", "JJR", "WRB", "PRP", "VBZ", "RB", "VBN", "VBG", "IN", "NN", "IN", "DT", "NN", "."], "head": [7, 4, 4, 7, 7, 7, 0, 7, 11, 11, 7, 13, 16, 16, 16, 11, 21, 21, 21, 21, 16, 21, 24, 21, 27, 27, 24, 7], "deprel": ["advmod", "det", "compound", "nsubj", "cop", "advmod", "root", "punct", "mark", "nsubj", "advcl", "det", "nsubj", "cop", "advmod", "ccomp", "mark", "nsubj", "aux", "advmod", "advcl", "xcomp", "case", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "FF#4:8-0", "target_tags": "Overall\\O the\\O food\\B quality\\O was\\O pretty\\O good\\O ,\\O though\\O I\\O hear\\O the\\O salmon\\O is\\O much\\O better\\O when\\O it\\O has\\O n't\\O sat\\O cooling\\O in\\O front\\O of\\O the\\O guest\\O .\\O", "opinion_tags": "Overall\\O the\\O food\\O quality\\O was\\O pretty\\O good\\B ,\\O though\\O I\\O hear\\O the\\O salmon\\O is\\O much\\O better\\O when\\O it\\O has\\O n't\\O sat\\O cooling\\O in\\O front\\O of\\O the\\O guest\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#4:9", "sentence": "The place has a nice fit-out , some attractive furnishings and , from what I could tell , a reasonable wine list ( I was given the food menu when I asked for the carte des vins )", "postag": ["DT", "NN", "VBZ", "DT", "JJ", "NN", ",", "DT", "JJ", "NNS", "CC", ",", "IN", "WP", "PRP", "MD", "VB", ",", "DT", "JJ", "NN", "NN", "-LRB-", "PRP", "VBD", "VBN", "DT", "NN", "NN", "WRB", "PRP", "VBD", "IN", "DT", "NN", "NN", "NN", "-RRB-"], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 6, 22, 22, 14, 17, 17, 17, 22, 22, 22, 22, 22, 6, 26, 26, 26, 3, 29, 29, 26, 32, 32, 26, 37, 37, 37, 37, 32, 26], "deprel": ["det", "nsubj", "root", "det", "amod", "obj", "punct", "det", "amod", "conj", "cc", "punct", "case", "obj", "nsubj", "aux", "acl", "punct", "det", "amod", "compound", "conj", "punct", "nsubj:pass", "aux:pass", "parataxis", "det", "compound", "obj", "mark", "nsubj", "advcl", "case", "det", "compound", "compound", "obl", "punct"], "triples": [{"uid": "FF#4:9-0", "target_tags": "The\\O place\\O has\\O a\\O nice\\O fit-out\\B ,\\O some\\O attractive\\O furnishings\\O and\\O ,\\O from\\O what\\O I\\O could\\O tell\\O ,\\O a\\O reasonable\\O wine\\O list\\O (\\O I\\O was\\O given\\O the\\O food\\O menu\\O when\\O I\\O asked\\O for\\O the\\O carte\\O des\\O vins\\O )\\O", "opinion_tags": "The\\O place\\O has\\O a\\O nice\\B fit-out\\O ,\\O some\\O attractive\\O furnishings\\O and\\O ,\\O from\\O what\\O I\\O could\\O tell\\O ,\\O a\\O reasonable\\O wine\\O list\\O (\\O I\\O was\\O given\\O the\\O food\\O menu\\O when\\O I\\O asked\\O for\\O the\\O carte\\O des\\O vins\\O )\\O", "sentiment": "positive"}, {"uid": "FF#4:9-1", "target_tags": "The\\O place\\O has\\O a\\O nice\\O fit-out\\O ,\\O some\\O attractive\\O furnishings\\B and\\O ,\\O from\\O what\\O I\\O could\\O tell\\O ,\\O a\\O reasonable\\O wine\\O list\\O (\\O I\\O was\\O given\\O the\\O food\\O menu\\O when\\O I\\O asked\\O for\\O the\\O carte\\O des\\O vins\\O )\\O", "opinion_tags": "The\\O place\\O has\\O a\\O nice\\O fit-out\\O ,\\O some\\O attractive\\B furnishings\\O and\\O ,\\O from\\O what\\O I\\O could\\O tell\\O ,\\O a\\O reasonable\\O wine\\O list\\O (\\O I\\O was\\O given\\O the\\O food\\O menu\\O when\\O I\\O asked\\O for\\O the\\O carte\\O des\\O vins\\O )\\O", "sentiment": "positive"}, {"uid": "FF#4:9-2", "target_tags": "The\\O place\\O has\\O a\\O nice\\O fit-out\\O ,\\O some\\O attractive\\O furnishings\\O and\\O ,\\O from\\O what\\O I\\O could\\O tell\\O ,\\O a\\O reasonable\\O wine\\B list\\I (\\O I\\O was\\O given\\O the\\O food\\O menu\\O when\\O I\\O asked\\O for\\O the\\O carte\\O des\\O vins\\O )\\O", "opinion_tags": "The\\O place\\O has\\O a\\O nice\\O fit-out\\O ,\\O some\\O attractive\\O furnishings\\O and\\O ,\\O from\\O what\\O I\\O could\\O tell\\O ,\\O a\\O reasonable\\B wine\\O list\\O (\\O I\\O was\\O given\\O the\\O food\\O menu\\O when\\O I\\O asked\\O for\\O the\\O carte\\O des\\O vins\\O )\\O", "sentiment": "positive"}]}, {"id": "FF#3:5", "sentence": "Everything was going good until we got our meals .", "postag": ["NN", "VBD", "VBG", "JJ", "IN", "PRP", "VBD", "PRP$", "NNS", "."], "head": [3, 3, 0, 3, 7, 7, 3, 9, 7, 3], "deprel": ["nsubj", "aux", "root", "xcomp", "mark", "nsubj", "advcl", "nmod:poss", "obj", "punct"], "triples": [{"uid": "FF#3:5-0", "target_tags": "Everything\\O was\\O going\\O good\\O until\\O we\\O got\\O our\\O meals\\B .\\O", "opinion_tags": "Everything\\O was\\O going\\O good\\B until\\O we\\O got\\O our\\O meals\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#3:6", "sentence": "I took one look at the chicken and I was appalled .", "postag": ["PRP", "VBD", "CD", "NN", "IN", "DT", "NN", "CC", "PRP", "VBD", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 4, 11, 11, 11, 2, 2], "deprel": ["nsubj", "root", "nummod", "obj", "case", "det", "nmod", "cc", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "FF#3:6-0", "target_tags": "I\\O took\\O one\\O look\\O at\\O the\\O chicken\\B and\\O I\\O was\\O appalled\\O .\\O", "opinion_tags": "I\\O took\\O one\\O look\\O at\\O the\\O chicken\\O and\\O I\\O was\\O appalled\\B .\\O", "sentiment": "negative"}]}, {"id": "FF#3:7", "sentence": "It was served with skin , over a bed of extremely undercooked spinach and mashed potatoes .", "postag": ["PRP", "VBD", "VBN", "IN", "NN", ",", "IN", "DT", "NN", "IN", "RB", "JJ", "NN", "CC", "VBN", "NNS", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 3, 13, 12, 13, 9, 16, 16, 13, 3], "deprel": ["nsubj:pass", "aux:pass", "root", "case", "obl", "punct", "case", "det", "obl", "case", "advmod", "amod", "nmod", "cc", "amod", "conj", "punct"], "triples": [{"uid": "FF#3:7-0", "target_tags": "It\\O was\\O served\\O with\\O skin\\O ,\\O over\\O a\\O bed\\O of\\O extremely\\O undercooked\\O spinach\\B and\\O mashed\\O potatoes\\O .\\O", "opinion_tags": "It\\O was\\O served\\O with\\O skin\\O ,\\O over\\O a\\O bed\\O of\\O extremely\\O undercooked\\B spinach\\O and\\O mashed\\O potatoes\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#3:9", "sentence": "I took one bite from the $ 24 salmon , and I have never , in the 17 years I have been going to restaurants tasted salmon as fishy , as dry , and as bland as the one in Flatbush Farms .", "postag": ["PRP", "VBD", "CD", "NN", "IN", "DT", "$", "CD", "NN", ",", "CC", "PRP", "VBP", "RB", ",", "IN", "DT", "CD", "NNS", "PRP", "VBP", "VBN", "VBG", "IN", "NNS", "VBN", "NN", "RB", "JJ", ",", "RB", "JJ", ",", "CC", "RB", "JJ", "IN", "DT", "NN", "IN", "NNP", "NNPS", "."], "head": [2, 0, 4, 2, 9, 9, 9, 7, 2, 13, 13, 13, 2, 13, 13, 19, 19, 19, 23, 23, 23, 23, 13, 25, 23, 25, 26, 29, 26, 32, 32, 29, 36, 36, 36, 23, 39, 39, 36, 42, 42, 39, 2], "deprel": ["nsubj", "root", "nummod", "obj", "case", "det", "compound", "nummod", "obl", "punct", "cc", "nsubj", "conj", "advmod", "punct", "case", "det", "nummod", "obl", "nsubj", "aux", "aux", "ccomp", "case", "obl", "acl", "xcomp", "advmod", "xcomp", "punct", "advmod", "conj", "punct", "cc", "advmod", "conj", "case", "det", "obl", "case", "compound", "nmod", "punct"], "triples": [{"uid": "FF#3:9-0", "target_tags": "I\\O took\\O one\\O bite\\O from\\O the\\O $\\O 24\\O salmon\\O ,\\O and\\O I\\O have\\O never\\O ,\\O in\\O the\\O 17\\O years\\O I\\O have\\O been\\O going\\O to\\O restaurants\\O tasted\\O salmon\\B as\\O fishy\\O ,\\O as\\O dry\\O ,\\O and\\O as\\O bland\\O as\\O the\\O one\\O in\\O Flatbush\\O Farms\\O .\\O", "opinion_tags": "I\\O took\\O one\\O bite\\O from\\O the\\O $\\O 24\\O salmon\\O ,\\O and\\O I\\O have\\O never\\O ,\\O in\\O the\\O 17\\O years\\O I\\O have\\O been\\O going\\O to\\O restaurants\\O tasted\\O salmon\\O as\\O fishy\\B ,\\O as\\O dry\\B ,\\O and\\O as\\O bland\\B as\\O the\\O one\\O in\\O Flatbush\\O Farms\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#3:11", "sentence": "So , I switch with my boyfriend again to see if maybe I could stomach the meat and spinach again , but the spinach was so undercooked that I just could not bite through it .", "postag": ["RB", ",", "PRP", "VBP", "IN", "PRP$", "NN", "RB", "TO", "VB", "IN", "RB", "PRP", "MD", "VB", "DT", "NN", "CC", "NN", "RB", ",", "CC", "DT", "NN", "VBD", "RB", "JJ", "IN", "PRP", "RB", "MD", "RB", "VB", "IN", "PRP", "."], "head": [4, 4, 4, 0, 7, 7, 4, 4, 10, 4, 15, 15, 15, 15, 10, 17, 15, 19, 17, 15, 27, 27, 24, 27, 27, 27, 4, 33, 33, 33, 33, 33, 27, 35, 33, 4], "deprel": ["advmod", "punct", "nsubj", "root", "case", "nmod:poss", "obl", "advmod", "mark", "advcl", "mark", "advmod", "nsubj", "aux", "advcl", "det", "obj", "cc", "conj", "advmod", "punct", "cc", "det", "nsubj", "cop", "advmod", "conj", "mark", "nsubj", "advmod", "aux", "advmod", "ccomp", "case", "obl", "punct"], "triples": [{"uid": "FF#3:11-0", "target_tags": "So\\O ,\\O I\\O switch\\O with\\O my\\O boyfriend\\O again\\O to\\O see\\O if\\O maybe\\O I\\O could\\O stomach\\O the\\O meat\\O and\\O spinach\\O again\\O ,\\O but\\O the\\O spinach\\B was\\O so\\O undercooked\\O that\\O I\\O just\\O could\\O not\\O bite\\O through\\O it\\O .\\O", "opinion_tags": "So\\O ,\\O I\\O switch\\O with\\O my\\O boyfriend\\O again\\O to\\O see\\O if\\O maybe\\O I\\O could\\O stomach\\O the\\O meat\\O and\\O spinach\\O again\\O ,\\O but\\O the\\O spinach\\O was\\O so\\O undercooked\\B that\\O I\\O just\\O could\\O not\\O bite\\O through\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#3:19", "sentence": "This is where it really really gets bad : the manager said , there is absolutely nothing we can do , it 's a matter of taste that she did n't like it , and I can not comp it .", "postag": ["DT", "VBZ", "WRB", "PRP", "RB", "RB", "VBZ", "JJ", ":", "DT", "NN", "VBD", ",", "EX", "VBZ", "RB", "NN", "PRP", "MD", "VB", ",", "PRP", "VBZ", "DT", "NN", "IN", "NN", "WDT", "PRP", "VBD", "RB", "VB", "PRP", ",", "CC", "PRP", "MD", "RB", "VB", "PRP", "."], "head": [3, 3, 0, 7, 7, 7, 3, 7, 3, 11, 12, 3, 15, 15, 12, 15, 15, 20, 20, 17, 3, 25, 25, 25, 3, 27, 25, 32, 32, 32, 32, 27, 32, 39, 39, 39, 39, 39, 3, 39, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "advmod", "advmod", "acl:relcl", "xcomp", "punct", "det", "nsubj", "parataxis", "punct", "expl", "ccomp", "advmod", "nsubj", "nsubj", "aux", "acl:relcl", "punct", "nsubj", "cop", "det", "parataxis", "case", "nmod", "obj", "nsubj", "aux", "advmod", "acl:relcl", "obj", "punct", "cc", "nsubj", "aux", "advmod", "conj", "obj", "punct"], "triples": [{"uid": "FF#3:19-0", "target_tags": "This\\O is\\O where\\O it\\O really\\O really\\O gets\\O bad\\O :\\O the\\O manager\\B said\\O ,\\O there\\O is\\O absolutely\\O nothing\\O we\\O can\\O do\\O ,\\O it\\O 's\\O a\\O matter\\O of\\O taste\\O that\\O she\\O did\\O n't\\O like\\O it\\O ,\\O and\\O I\\O can\\O not\\O comp\\O it\\O .\\O", "opinion_tags": "This\\O is\\O where\\O it\\O really\\O really\\O gets\\O bad\\B :\\O the\\O manager\\O said\\O ,\\O there\\O is\\O absolutely\\O nothing\\O we\\O can\\O do\\O ,\\O it\\O 's\\O a\\O matter\\O of\\O taste\\O that\\O she\\O did\\O n't\\O like\\O it\\O ,\\O and\\O I\\O can\\O not\\O comp\\O it\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#3:23", "sentence": "The manager came to the table and said we can do what we want , so we paid for what we did enjoy , the drinks and appetizers , and walked out .", "postag": ["DT", "NN", "VBD", "IN", "DT", "NN", "CC", "VBD", "PRP", "MD", "VB", "WP", "PRP", "VBP", ",", "RB", "PRP", "VBD", "IN", "WP", "PRP", "VBD", "VB", ",", "DT", "NNS", "CC", "NNS", ",", "CC", "VBD", "RP", "."], "head": [2, 3, 0, 6, 6, 3, 8, 3, 11, 11, 8, 11, 14, 11, 18, 18, 18, 3, 20, 18, 23, 23, 20, 26, 26, 20, 28, 26, 31, 31, 18, 31, 3], "deprel": ["det", "nsubj", "root", "case", "det", "obl", "cc", "conj", "nsubj", "aux", "ccomp", "obj", "nsubj", "ccomp", "punct", "advmod", "nsubj", "parataxis", "case", "obl", "nsubj", "aux", "acl:relcl", "punct", "det", "appos", "cc", "conj", "punct", "cc", "conj", "compound:prt", "punct"], "triples": [{"uid": "FF#3:23-0", "target_tags": "The\\O manager\\O came\\O to\\O the\\O table\\O and\\O said\\O we\\O can\\O do\\O what\\O we\\O want\\O ,\\O so\\O we\\O paid\\O for\\O what\\O we\\O did\\O enjoy\\O ,\\O the\\O drinks\\B and\\O appetizers\\O ,\\O and\\O walked\\O out\\O .\\O", "opinion_tags": "The\\O manager\\O came\\O to\\O the\\O table\\O and\\O said\\O we\\O can\\O do\\O what\\O we\\O want\\O ,\\O so\\O we\\O paid\\O for\\O what\\O we\\O did\\O enjoy\\B ,\\O the\\O drinks\\O and\\O appetizers\\O ,\\O and\\O walked\\O out\\O .\\O", "sentiment": "positive"}, {"uid": "FF#3:23-1", "target_tags": "The\\O manager\\O came\\O to\\O the\\O table\\O and\\O said\\O we\\O can\\O do\\O what\\O we\\O want\\O ,\\O so\\O we\\O paid\\O for\\O what\\O we\\O did\\O enjoy\\O ,\\O the\\O drinks\\O and\\O appetizers\\B ,\\O and\\O walked\\O out\\O .\\O", "opinion_tags": "The\\O manager\\O came\\O to\\O the\\O table\\O and\\O said\\O we\\O can\\O do\\O what\\O we\\O want\\O ,\\O so\\O we\\O paid\\O for\\O what\\O we\\O did\\O enjoy\\B ,\\O the\\O drinks\\O and\\O appetizers\\O ,\\O and\\O walked\\O out\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#3:25", "sentence": "THIS STAFF SHOULD BE FIRED .", "postag": ["DT", "NN", "MD", "VB", "VBN", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj:pass", "aux", "aux:pass", "root", "punct"], "triples": [{"uid": "FF#3:25-0", "target_tags": "THIS\\O STAFF\\B SHOULD\\O BE\\O FIRED\\O .\\O", "opinion_tags": "THIS\\O STAFF\\O SHOULD\\O BE\\O FIRED\\B .\\O", "sentiment": "negative"}]}, {"id": "EVPK#9:0", "sentence": "cirspy crust margherita pizza", "postag": ["JJ", "NN", "NNP", "NN"], "head": [2, 4, 4, 0], "deprel": ["amod", "compound", "compound", "root"], "triples": [{"uid": "EVPK#9:0-0", "target_tags": "cirspy\\O crust\\O margherita\\B pizza\\I", "opinion_tags": "cirspy\\B crust\\B margherita\\O pizza\\O", "sentiment": "positive"}]}, {"id": "EVPK#9:2", "sentence": "it was really good pizza .", "postag": ["PRP", "VBD", "RB", "JJ", "NN", "."], "head": [5, 5, 4, 5, 0, 5], "deprel": ["nsubj", "cop", "advmod", "amod", "root", "punct"], "triples": [{"uid": "EVPK#9:2-0", "target_tags": "it\\O was\\O really\\O good\\O pizza\\B .\\O", "opinion_tags": "it\\O was\\O really\\O good\\B pizza\\O .\\O", "sentiment": "positive"}]}, {"id": "EVPK#9:3", "sentence": "the crust was imazingly cooked well and pizza was fully loaded : ) : ) : )", "postag": ["DT", "NN", "VBD", "RB", "VBN", "RB", "CC", "NN", "VBD", "RB", "VBN", ":", "-RRB-", ":", "-RRB-", ":", "-RRB-"], "head": [2, 5, 5, 5, 0, 5, 11, 11, 11, 11, 5, 5, 5, 5, 5, 5, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "advmod", "cc", "nsubj:pass", "aux:pass", "advmod", "conj", "punct", "punct", "punct", "punct", "punct", "punct"], "triples": [{"uid": "EVPK#9:3-0", "target_tags": "the\\O crust\\B was\\O imazingly\\O cooked\\O well\\O and\\O pizza\\O was\\O fully\\O loaded\\O :\\O )\\O :\\O )\\O :\\O )\\O", "opinion_tags": "the\\O crust\\O was\\O imazingly\\O cooked\\B well\\I and\\O pizza\\O was\\O fully\\O loaded\\O :\\O )\\O :\\O )\\O :\\O )\\O", "sentiment": "positive"}, {"uid": "EVPK#9:3-1", "target_tags": "the\\O crust\\O was\\O imazingly\\O cooked\\O well\\O and\\O pizza\\B was\\O fully\\O loaded\\O :\\O )\\O :\\O )\\O :\\O )\\O", "opinion_tags": "the\\O crust\\O was\\O imazingly\\O cooked\\O well\\O and\\O pizza\\O was\\O fully\\B loaded\\I :\\O )\\O :\\O )\\O :\\O )\\O", "sentiment": "positive"}]}, {"id": "CLF#4:0", "sentence": "Single Worst Restaurant in Manhattan", "postag": ["NNP", "JJS", "NN", "IN", "NNP"], "head": [3, 3, 0, 5, 3], "deprel": ["amod", "amod", "root", "case", "nmod"], "triples": [{"uid": "CLF#4:0-0", "target_tags": "Single\\O Worst\\O Restaurant\\B in\\O Manhattan\\O", "opinion_tags": "Single\\O Worst\\B Restaurant\\O in\\O Manhattan\\O", "sentiment": "negative"}]}, {"id": "CLF#4:1", "sentence": "I 'll being with a couple of positives : cool decor , good pita and hummus , and grilled octopus that was actually pretty tasty .", "postag": ["PRP", "MD", "VBG", "IN", "DT", "NN", "IN", "NNS", ":", "JJ", "NN", ",", "JJ", "NN", "CC", "NN", ",", "CC", "JJ", "NN", "WDT", "VBD", "RB", "RB", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 8, 6, 11, 11, 6, 14, 14, 11, 16, 11, 20, 20, 20, 11, 25, 25, 25, 25, 20, 3], "deprel": ["nsubj", "aux", "root", "case", "det", "obl", "case", "nmod", "punct", "amod", "appos", "punct", "amod", "conj", "cc", "conj", "punct", "cc", "amod", "conj", "nsubj", "cop", "advmod", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "CLF#4:1-0", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\B ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\B decor\\O ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#4:1-1", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\B and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\B pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#4:1-2", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\O and\\O hummus\\B ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\B pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "sentiment": "positive"}, {"uid": "CLF#4:1-3", "target_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\B octopus\\I that\\O was\\O actually\\O pretty\\O tasty\\O .\\O", "opinion_tags": "I\\O 'll\\O being\\O with\\O a\\O couple\\O of\\O positives\\O :\\O cool\\O decor\\O ,\\O good\\O pita\\O and\\O hummus\\O ,\\O and\\O grilled\\O octopus\\O that\\O was\\O actually\\O pretty\\O tasty\\B .\\O", "sentiment": "positive"}]}, {"id": "CLF#4:11", "sentence": "It is quite a spectacular scene i 'll give them that .", "postag": ["PRP", "VBZ", "PDT", "DT", "JJ", "NN", "PRP", "MD", "VB", "PRP", "DT", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 9, 9, 6], "deprel": ["nsubj", "cop", "advmod", "det", "amod", "root", "nsubj", "aux", "acl:relcl", "i<PERSON><PERSON>", "obj", "punct"], "triples": [{"uid": "CLF#4:11-0", "target_tags": "It\\O is\\O quite\\O a\\O spectacular\\O scene\\B i\\O 'll\\O give\\O them\\O that\\O .\\O", "opinion_tags": "It\\O is\\O quite\\O a\\O spectacular\\B scene\\O i\\O 'll\\O give\\O them\\O that\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#4:12", "sentence": "The decor however seems to be the distraction so you wo n't notice that you just payed 300 bucks for some cold eggplant that took 2 FRICKIN HOURS TO COME ! ! ! !", "postag": ["DT", "NN", "RB", "VBZ", "TO", "VB", "DT", "NN", "RB", "PRP", "MD", "RB", "VB", "IN", "PRP", "RB", "VBD", "CD", "NNS", "IN", "DT", "JJ", "NN", "WDT", "VBD", "CD", "NN", "NNS", "TO", "VB", ".", ".", ".", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 13, 13, 13, 13, 4, 17, 17, 17, 13, 19, 17, 23, 23, 23, 17, 25, 23, 28, 28, 25, 30, 25, 4, 4, 4, 4], "deprel": ["det", "nsubj", "advmod", "root", "mark", "cop", "det", "xcomp", "advmod", "nsubj", "aux", "advmod", "parataxis", "mark", "nsubj", "advmod", "ccomp", "nummod", "obj", "case", "det", "amod", "obl", "nsubj", "acl:relcl", "nummod", "compound", "obj", "mark", "advcl", "punct", "punct", "punct", "punct"], "triples": [{"uid": "CLF#4:12-0", "target_tags": "The\\O decor\\B however\\O seems\\O to\\O be\\O the\\O distraction\\O so\\O you\\O wo\\O n't\\O notice\\O that\\O you\\O just\\O payed\\O 300\\O bucks\\O for\\O some\\O cold\\O eggplant\\O that\\O took\\O 2\\O FRICKIN\\O HOURS\\O TO\\O COME\\O !\\O !\\O !\\O !\\O", "opinion_tags": "The\\O decor\\O however\\O seems\\O to\\O be\\O the\\O distraction\\B so\\O you\\O wo\\O n't\\O notice\\O that\\O you\\O just\\O payed\\O 300\\O bucks\\O for\\O some\\O cold\\O eggplant\\O that\\O took\\O 2\\O FRICKIN\\O HOURS\\O TO\\O COME\\O !\\O !\\O !\\O !\\O", "sentiment": "neutral"}, {"uid": "CLF#4:12-1", "target_tags": "The\\O decor\\O however\\O seems\\O to\\O be\\O the\\O distraction\\O so\\O you\\O wo\\O n't\\O notice\\O that\\O you\\O just\\O payed\\O 300\\O bucks\\O for\\O some\\O cold\\O eggplant\\B that\\O took\\O 2\\O FRICKIN\\O HOURS\\O TO\\O COME\\O !\\O !\\O !\\O !\\O", "opinion_tags": "The\\O decor\\O however\\O seems\\O to\\O be\\O the\\O distraction\\O so\\O you\\O wo\\O n't\\O notice\\O that\\O you\\O just\\O payed\\O 300\\O bucks\\O for\\O some\\O cold\\B eggplant\\O that\\O took\\O 2\\O FRICKIN\\O HOURS\\O TO\\O COME\\O !\\O !\\O !\\O !\\O", "sentiment": "negative"}]}, {"id": "BHD#4:0", "sentence": "Great Hot Dogs !", "postag": ["JJ", "JJ", "NNS", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "amod", "root", "punct"], "triples": [{"uid": "BHD#4:0-0", "target_tags": "Great\\O Hot\\B Dogs\\I !\\O", "opinion_tags": "Great\\B Hot\\O Dogs\\O !\\O", "sentiment": "positive"}]}, {"id": "BHD#4:3", "sentence": "The hot dogs are top notch , and they 're <PERSON><PERSON> is amazing !", "postag": ["DT", "JJ", "NNS", "VBP", "JJ", "NN", ",", "CC", "PRP", "VBP", "NNP", "VBZ", "JJ", "."], "head": [3, 3, 6, 6, 6, 0, 13, 13, 13, 13, 13, 13, 6, 6], "deprel": ["det", "amod", "nsubj", "cop", "amod", "root", "punct", "cc", "nsubj", "cop", "nsubj", "cop", "conj", "punct"], "triples": [{"uid": "BHD#4:3-0", "target_tags": "The\\O hot\\B dogs\\I are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\O !\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O top\\B notch\\I ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\O !\\O", "sentiment": "positive"}, {"uid": "BHD#4:3-1", "target_tags": "The\\O hot\\O dogs\\O are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\B is\\O amazing\\O !\\O", "opinion_tags": "The\\O hot\\O dogs\\O are\\O top\\O notch\\O ,\\O and\\O they\\O 're\\O Slamwich\\O is\\O amazing\\B !\\O", "sentiment": "positive"}]}, {"id": "BHD#4:4", "sentence": "Going to Bark is always worth the train ride , and will make your tongue and belly very happy !", "postag": ["VBG", "IN", "NNP", "VBZ", "RB", "JJ", "DT", "NN", "NN", ",", "CC", "MD", "VB", "PRP$", "NN", "CC", "NN", "RB", "JJ", "."], "head": [6, 3, 1, 6, 6, 0, 9, 9, 6, 13, 13, 13, 6, 15, 13, 17, 15, 19, 13, 6], "deprel": ["csubj", "case", "obl", "cop", "advmod", "root", "det", "compound", "obj", "punct", "cc", "aux", "conj", "nmod:poss", "obj", "cc", "conj", "advmod", "xcomp", "punct"], "triples": [{"uid": "BHD#4:4-0", "target_tags": "Going\\O to\\O Bark\\B is\\O always\\O worth\\O the\\O train\\O ride\\O ,\\O and\\O will\\O make\\O your\\O tongue\\O and\\O belly\\O very\\O happy\\O !\\O", "opinion_tags": "Going\\O to\\O Bark\\O is\\O always\\O worth\\B the\\O train\\O ride\\O ,\\O and\\O will\\O make\\O your\\O tongue\\O and\\O belly\\O very\\O happy\\O !\\O", "sentiment": "positive"}]}, {"id": "BHD#4:6", "sentence": "But nonetheless -- great spot , great food .", "postag": ["CC", "RB", ",", "JJ", "NN", ",", "JJ", "NN", "."], "head": [5, 5, 5, 5, 0, 5, 8, 5, 5], "deprel": ["cc", "advmod", "punct", "amod", "root", "punct", "amod", "conj", "punct"], "triples": [{"uid": "BHD#4:6-0", "target_tags": "But\\O nonetheless\\O --\\O great\\O spot\\B ,\\O great\\O food\\O .\\O", "opinion_tags": "But\\O nonetheless\\O --\\O great\\B spot\\O ,\\O great\\O food\\O .\\O", "sentiment": "positive"}, {"uid": "BHD#4:6-1", "target_tags": "But\\O nonetheless\\O --\\O great\\O spot\\O ,\\O great\\O food\\B .\\O", "opinion_tags": "But\\O nonetheless\\O --\\O great\\O spot\\O ,\\O great\\B food\\O .\\O", "sentiment": "positive"}]}, {"id": "BFC#7:0", "sentence": "Fabulous food - if the front of house staff do n't put you off –", "postag": ["JJ", "NN", ",", "IN", "DT", "NN", "IN", "NN", "NN", "VBP", "RB", "VB", "PRP", "RP", "."], "head": [2, 0, 2, 12, 6, 12, 9, 9, 6, 12, 12, 2, 12, 12, 2], "deprel": ["amod", "root", "punct", "mark", "det", "nsubj", "case", "compound", "nmod", "aux", "advmod", "advcl", "obj", "compound:prt", "punct"], "triples": [{"uid": "BFC#7:0-0", "target_tags": "Fabulous\\O food\\B -\\O if\\O the\\O front\\O of\\O house\\O staff\\O do\\O n't\\O put\\O you\\O off\\O –\\O", "opinion_tags": "Fabulous\\B food\\O -\\O if\\O the\\O front\\O of\\O house\\O staff\\O do\\O n't\\O put\\O you\\O off\\O –\\O", "sentiment": "positive"}]}, {"id": "BFC#7:3", "sentence": "Each time we 've been , the front of house staff ( not the waiters - they 're fantastic - but the people who greet and seat you ) has been so hideous to us that were it not for the exceptional fish dishes I would never return .", "postag": ["DT", "NN", "PRP", "VBP", "VBN", ",", "DT", "NN", "IN", "NN", "NN", "-LRB-", "RB", "DT", "NNS", ",", "PRP", "VBP", "JJ", ",", "CC", "DT", "NNS", "WP", "VBP", "CC", "VBP", "PRP", "-RRB-", "VBZ", "VBN", "RB", "JJ", "IN", "PRP", "WDT", "VBD", "PRP", "RB", "IN", "DT", "JJ", "NN", "NNS", "PRP", "MD", "RB", "VB", "."], "head": [2, 33, 8, 8, 2, 33, 8, 33, 11, 11, 8, 15, 15, 15, 11, 15, 19, 19, 15, 19, 23, 23, 33, 25, 23, 27, 25, 27, 19, 33, 33, 33, 0, 35, 33, 37, 38, 44, 44, 44, 44, 44, 44, 33, 48, 48, 48, 44, 33], "deprel": ["det", "obl:tmod", "nsubj", "aux", "acl:relcl", "punct", "det", "nsubj", "case", "compound", "nmod", "punct", "advmod", "det", "appos", "punct", "nsubj", "cop", "parataxis", "punct", "cc", "det", "nsubj", "nsubj", "acl:relcl", "cc", "conj", "obj", "punct", "aux", "cop", "advmod", "root", "case", "obl", "nsubj", "cop", "nsubj", "advmod", "case", "det", "amod", "compound", "obl", "nsubj", "aux", "advmod", "acl:relcl", "punct"], "triples": [{"uid": "BFC#7:3-0", "target_tags": "Each\\O time\\O we\\O 've\\O been\\O ,\\O the\\O front\\O of\\O house\\O staff\\O (\\O not\\O the\\O waiters\\B -\\O they\\O 're\\O fantastic\\O -\\O but\\O the\\O people\\O who\\O greet\\O and\\O seat\\O you\\O )\\O has\\O been\\O so\\O hideous\\O to\\O us\\O that\\O were\\O it\\O not\\O for\\O the\\O exceptional\\O fish\\O dishes\\O I\\O would\\O never\\O return\\O .\\O", "opinion_tags": "Each\\O time\\O we\\O 've\\O been\\O ,\\O the\\O front\\O of\\O house\\O staff\\O (\\O not\\O the\\O waiters\\O -\\O they\\O 're\\O fantastic\\B -\\O but\\O the\\O people\\O who\\O greet\\O and\\O seat\\O you\\O )\\O has\\O been\\O so\\O hideous\\O to\\O us\\O that\\O were\\O it\\O not\\O for\\O the\\O exceptional\\O fish\\O dishes\\O I\\O would\\O never\\O return\\O .\\O", "sentiment": "positive"}, {"uid": "BFC#7:3-1", "target_tags": "Each\\O time\\O we\\O 've\\O been\\O ,\\O the\\O front\\B of\\I house\\I staff\\I (\\O not\\O the\\O waiters\\O -\\O they\\O 're\\O fantastic\\O -\\O but\\O the\\O people\\O who\\O greet\\O and\\O seat\\O you\\O )\\O has\\O been\\O so\\O hideous\\O to\\O us\\O that\\O were\\O it\\O not\\O for\\O the\\O exceptional\\O fish\\O dishes\\O I\\O would\\O never\\O return\\O .\\O", "opinion_tags": "Each\\O time\\O we\\O 've\\O been\\O ,\\O the\\O front\\O of\\O house\\O staff\\O (\\O not\\O the\\O waiters\\O -\\O they\\O 're\\O fantastic\\O -\\O but\\O the\\O people\\O who\\O greet\\O and\\O seat\\O you\\O )\\O has\\O been\\O so\\O hideous\\B to\\O us\\O that\\O were\\O it\\O not\\O for\\O the\\O exceptional\\O fish\\O dishes\\O I\\O would\\O never\\O return\\O .\\O", "sentiment": "negative"}, {"uid": "BFC#7:3-2", "target_tags": "Each\\O time\\O we\\O 've\\O been\\O ,\\O the\\O front\\O of\\O house\\O staff\\O (\\O not\\O the\\O waiters\\O -\\O they\\O 're\\O fantastic\\O -\\O but\\O the\\O people\\O who\\O greet\\O and\\O seat\\O you\\O )\\O has\\O been\\O so\\O hideous\\O to\\O us\\O that\\O were\\O it\\O not\\O for\\O the\\O exceptional\\O fish\\B dishes\\I I\\O would\\O never\\O return\\O .\\O", "opinion_tags": "Each\\O time\\O we\\O 've\\O been\\O ,\\O the\\O front\\O of\\O house\\O staff\\O (\\O not\\O the\\O waiters\\O -\\O they\\O 're\\O fantastic\\O -\\O but\\O the\\O people\\O who\\O greet\\O and\\O seat\\O you\\O )\\O has\\O been\\O so\\O hideous\\O to\\O us\\O that\\O were\\O it\\O not\\O for\\O the\\O exceptional\\B fish\\O dishes\\O I\\O would\\O never\\O return\\O .\\O", "sentiment": "positive"}]}, {"id": "BFC#7:4", "sentence": "As BFC does n't take reservations you almost always have to wait by the bar - and be abused by the front of house staff until you are seated , which can be over an hour later !", "postag": ["IN", "NNP", "VBZ", "RB", "VB", "NNS", "PRP", "RB", "RB", "VBP", "TO", "VB", "IN", "DT", "NN", ",", "CC", "VB", "VBN", "IN", "DT", "NN", "IN", "NN", "NN", "IN", "PRP", "VBP", "VBN", ",", "WDT", "MD", "VB", "RB", "DT", "NN", "RBR", "."], "head": [5, 5, 5, 5, 10, 5, 10, 9, 10, 0, 12, 10, 15, 15, 12, 10, 19, 19, 10, 22, 22, 19, 25, 25, 22, 29, 29, 29, 19, 34, 34, 34, 34, 29, 36, 37, 34, 10], "deprel": ["mark", "nsubj", "aux", "advmod", "advcl", "obj", "nsubj", "advmod", "advmod", "root", "mark", "xcomp", "case", "det", "obl", "punct", "cc", "aux:pass", "conj", "case", "det", "obl", "case", "compound", "nmod", "mark", "nsubj:pass", "aux:pass", "advcl", "punct", "nsubj", "aux", "cop", "ccomp", "det", "obl:npmod", "advmod", "punct"], "triples": [{"uid": "BFC#7:4-0", "target_tags": "As\\O BFC\\O does\\O n't\\O take\\O reservations\\O you\\O almost\\O always\\O have\\O to\\O wait\\O by\\O the\\O bar\\O -\\O and\\O be\\O abused\\O by\\O the\\O front\\B of\\I house\\I staff\\I until\\O you\\O are\\O seated\\O ,\\O which\\O can\\O be\\O over\\O an\\O hour\\O later\\O !\\O", "opinion_tags": "As\\O BFC\\O does\\O n't\\O take\\O reservations\\O you\\O almost\\O always\\O have\\O to\\O wait\\O by\\O the\\O bar\\O -\\O and\\O be\\O abused\\B by\\O the\\O front\\O of\\O house\\O staff\\O until\\O you\\O are\\O seated\\O ,\\O which\\O can\\O be\\O over\\O an\\O hour\\O later\\O !\\O", "sentiment": "negative"}]}, {"id": "BFC#7:5", "sentence": "The frizzy retro girl ( with winged/ Dame Edna glasses ) will yell at you if you try to order a drink .", "postag": ["DT", "JJ", "JJ", "NN", "-LRB-", "IN", "VBN", "NNP", "NNP", "NNS", "-RRB-", "MD", "VB", "IN", "PRP", "IN", "PRP", "VBP", "TO", "VB", "DT", "NN", "."], "head": [4, 4, 4, 13, 10, 10, 10, 10, 10, 4, 10, 13, 0, 15, 13, 18, 18, 13, 20, 18, 22, 20, 13], "deprel": ["det", "amod", "amod", "nsubj", "punct", "case", "amod", "compound", "compound", "nmod", "punct", "aux", "root", "case", "obl", "mark", "nsubj", "advcl", "mark", "xcomp", "det", "obj", "punct"], "triples": [{"uid": "BFC#7:5-0", "target_tags": "The\\O frizzy\\O retro\\O girl\\B (\\O with\\O winged/\\O Dame\\O Edna\\O glasses\\O )\\O will\\O yell\\O at\\O you\\O if\\O you\\O try\\O to\\O order\\O a\\O drink\\O .\\O", "opinion_tags": "The\\O frizzy\\B retro\\O girl\\O (\\O with\\O winged/\\O Dame\\O Edna\\O glasses\\O )\\O will\\O yell\\O at\\O you\\O if\\O you\\O try\\O to\\O order\\O a\\O drink\\O .\\O", "sentiment": "negative"}]}, {"id": "BFC#7:7", "sentence": "I 'd be horrified if my staff were turning away customers so early and so rudely !", "postag": ["PRP", "MD", "VB", "VBN", "IN", "PRP$", "NN", "VBD", "VBG", "RB", "NNS", "RB", "RB", "CC", "RB", "RB", "."], "head": [4, 4, 4, 0, 9, 7, 9, 9, 4, 11, 9, 13, 9, 16, 13, 13, 4], "deprel": ["nsubj:pass", "aux", "aux:pass", "root", "mark", "nmod:poss", "nsubj", "aux", "advcl", "advmod", "obj", "advmod", "advmod", "cc", "conj", "conj", "punct"], "triples": [{"uid": "BFC#7:7-0", "target_tags": "I\\O 'd\\O be\\O horrified\\O if\\O my\\O staff\\B were\\O turning\\O away\\O customers\\O so\\O early\\O and\\O so\\O rudely\\O !\\O", "opinion_tags": "I\\O 'd\\O be\\O horrified\\B if\\O my\\O staff\\O were\\O turning\\O away\\O customers\\O so\\O early\\O and\\O so\\O rudely\\O !\\O", "sentiment": "negative"}]}, {"id": "BFC#7:8", "sentence": "There 's another girl who I ca n't describe , she is about 5'6 '' with brown hair , who eavesdrops on your conversation and chimes in - except she only hears the last part of what you said , so her uninvited opinions are often out of context and nothing to do with what you 're *really* talking about .", "postag": ["EX", "VBZ", "DT", "NN", "WP", "PRP", "MD", "RB", "VB", ",", "PRP", "VBZ", "RB", "CD", "''", "IN", "JJ", "NN", ",", "WP", "VBZ", "IN", "PRP$", "NN", "CC", "VBZ", "RB", ",", "IN", "PRP", "RB", "VBZ", "DT", "JJ", "NN", "IN", "WP", "PRP", "VBD", ",", "RB", "PRP$", "JJ", "NNS", "VBP", "RB", "IN", "IN", "NN", "CC", "NN", "TO", "VB", "IN", "WP", "PRP", "VBP", "RB", "VBG", "IN", "."], "head": [2, 0, 4, 2, 9, 9, 9, 9, 4, 2, 14, 14, 14, 2, 14, 18, 18, 14, 18, 21, 18, 24, 24, 21, 26, 21, 26, 2, 32, 32, 32, 2, 35, 35, 32, 37, 35, 39, 37, 32, 49, 44, 44, 49, 49, 49, 49, 49, 32, 51, 49, 53, 51, 55, 53, 59, 59, 59, 55, 59, 2], "deprel": ["expl", "root", "det", "nsubj", "obj", "nsubj", "aux", "advmod", "acl:relcl", "punct", "nsubj", "cop", "advmod", "parataxis", "punct", "case", "amod", "nmod", "punct", "nsubj", "acl:relcl", "case", "nmod:poss", "obl", "cc", "conj", "advmod", "punct", "mark", "nsubj", "advmod", "advcl", "det", "amod", "obj", "case", "nmod", "nsubj", "acl:relcl", "punct", "advmod", "nmod:poss", "amod", "nsubj", "cop", "advmod", "case", "case", "advcl", "cc", "conj", "mark", "acl", "case", "obl", "nsubj", "aux", "advmod", "acl:relcl", "obl", "punct"], "triples": [{"uid": "BFC#7:8-0", "target_tags": "There\\O 's\\O another\\O girl\\B who\\O I\\O ca\\O n't\\O describe\\O ,\\O she\\O is\\O about\\O 5'6\\O ''\\O with\\O brown\\O hair\\O ,\\O who\\O eavesdrops\\O on\\O your\\O conversation\\O and\\O chimes\\O in\\O -\\O except\\O she\\O only\\O hears\\O the\\O last\\O part\\O of\\O what\\O you\\O said\\O ,\\O so\\O her\\O uninvited\\O opinions\\O are\\O often\\O out\\O of\\O context\\O and\\O nothing\\O to\\O do\\O with\\O what\\O you\\O 're\\O *really*\\O talking\\O about\\O .\\O", "opinion_tags": "There\\O 's\\O another\\O girl\\O who\\O I\\O ca\\O n't\\O describe\\O ,\\O she\\O is\\O about\\O 5'6\\O ''\\O with\\O brown\\O hair\\O ,\\O who\\O eavesdrops\\O on\\O your\\O conversation\\O and\\O chimes\\O in\\O -\\O except\\O she\\O only\\O hears\\O the\\O last\\O part\\O of\\O what\\O you\\O said\\O ,\\O so\\O her\\O uninvited\\B opinions\\O are\\O often\\O out\\O of\\O context\\O and\\O nothing\\O to\\O do\\O with\\O what\\O you\\O 're\\O *really*\\O talking\\O about\\O .\\O", "sentiment": "negative"}]}, {"id": "BFC#7:11", "sentence": "Considering you will spend at least $ 60 a head , I expect better service .", "postag": ["VBG", "PRP", "MD", "VB", "RB", "RBS", "$", "CD", "DT", "NN", ",", "PRP", "VBP", "JJR", "NN", "."], "head": [4, 4, 4, 13, 6, 7, 4, 7, 10, 7, 4, 13, 0, 15, 13, 13], "deprel": ["mark", "nsubj", "aux", "advcl", "case", "nmod", "obj", "nummod", "det", "nmod:npmod", "punct", "nsubj", "root", "amod", "obj", "punct"], "triples": [{"uid": "BFC#7:11-0", "target_tags": "Considering\\O you\\O will\\O spend\\O at\\O least\\O $\\O 60\\O a\\O head\\O ,\\O I\\O expect\\O better\\O service\\B .\\O", "opinion_tags": "Considering\\O you\\O will\\O spend\\O at\\O least\\O $\\O 60\\O a\\O head\\O ,\\O I\\O expect\\B better\\I service\\O .\\O", "sentiment": "negative"}]}, {"id": "ADLT#6:2", "sentence": "The food and service were fine , however the maitre-D was incredibly unwelcoming and arrogant .", "postag": ["DT", "NN", "CC", "NN", "VBD", "JJ", ",", "RB", "DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "."], "head": [2, 6, 4, 2, 6, 0, 6, 13, 10, 13, 13, 13, 6, 15, 13, 6], "deprel": ["det", "nsubj", "cc", "conj", "cop", "root", "punct", "advmod", "det", "nsubj", "cop", "advmod", "parataxis", "cc", "conj", "punct"], "triples": [{"uid": "ADLT#6:2-0", "target_tags": "The\\O food\\B and\\O service\\O were\\O fine\\O ,\\O however\\O the\\O maitre-D\\O was\\O incredibly\\O unwelcoming\\O and\\O arrogant\\O .\\O", "opinion_tags": "The\\O food\\O and\\O service\\O were\\O fine\\B ,\\O however\\O the\\O maitre-D\\O was\\O incredibly\\O unwelcoming\\O and\\O arrogant\\O .\\O", "sentiment": "positive"}, {"uid": "ADLT#6:2-1", "target_tags": "The\\O food\\O and\\O service\\B were\\O fine\\O ,\\O however\\O the\\O maitre-D\\O was\\O incredibly\\O unwelcoming\\O and\\O arrogant\\O .\\O", "opinion_tags": "The\\O food\\O and\\O service\\O were\\O fine\\B ,\\O however\\O the\\O maitre-D\\O was\\O incredibly\\O unwelcoming\\O and\\O arrogant\\O .\\O", "sentiment": "positive"}, {"uid": "ADLT#6:2-2", "target_tags": "The\\O food\\O and\\O service\\O were\\O fine\\O ,\\O however\\O the\\O maitre-D\\B was\\O incredibly\\O unwelcoming\\O and\\O arrogant\\O .\\O", "opinion_tags": "The\\O food\\O and\\O service\\O were\\O fine\\O ,\\O however\\O the\\O maitre-D\\O was\\O incredibly\\O unwelcoming\\B and\\O arrogant\\B .\\O", "sentiment": "negative"}]}, {"id": "ADLT#6:3", "sentence": "While finishing our meals which included a high-end bottle of wine , our son 's fiance joined us for a glass of wine and dessert .", "postag": ["IN", "VBG", "PRP$", "NNS", "WDT", "VBD", "DT", "JJ", "NN", "IN", "NN", ",", "PRP$", "NN", "POS", "NN", "VBD", "PRP", "IN", "DT", "NN", "IN", "NN", "CC", "NN", "."], "head": [2, 17, 4, 2, 6, 4, 9, 9, 6, 11, 9, 2, 14, 16, 14, 17, 0, 17, 21, 21, 17, 23, 21, 25, 23, 17], "deprel": ["mark", "advcl", "nmod:poss", "obj", "nsubj", "acl:relcl", "det", "amod", "obj", "case", "nmod", "punct", "nmod:poss", "nmod:poss", "case", "nsubj", "root", "obj", "case", "det", "obl", "case", "nmod", "cc", "conj", "punct"], "triples": [{"uid": "ADLT#6:3-0", "target_tags": "While\\O finishing\\O our\\O meals\\O which\\O included\\O a\\O high-end\\O bottle\\B of\\I wine\\I ,\\O our\\O son\\O 's\\O fiance\\O joined\\O us\\O for\\O a\\O glass\\O of\\O wine\\O and\\O dessert\\O .\\O", "opinion_tags": "While\\O finishing\\O our\\O meals\\O which\\O included\\O a\\O high-end\\B bottle\\O of\\O wine\\O ,\\O our\\O son\\O 's\\O fiance\\O joined\\O us\\O for\\O a\\O glass\\O of\\O wine\\O and\\O dessert\\O .\\O", "sentiment": "positive"}]}, {"id": "ADLT#6:4", "sentence": "This guy refused to seat her and she left , followed shortly by the four of us , but not before I told him that in my 40 years of world travel , including Paris , that I had never seen such a display of bad behavior by a frontman in a restaurant .", "postag": ["DT", "NN", "VBD", "TO", "VB", "PRP", "CC", "PRP", "VBD", ",", "VBN", "RB", "IN", "DT", "CD", "IN", "PRP", ",", "CC", "RB", "IN", "PRP", "VBD", "PRP", "IN", "IN", "PRP$", "CD", "NNS", "IN", "NN", "NN", ",", "VBG", "NNP", ",", "IN", "PRP", "VBD", "RB", "VBN", "PDT", "DT", "NN", "IN", "JJ", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 3, 0, 5, 3, 5, 9, 9, 3, 11, 9, 11, 15, 15, 11, 17, 15, 23, 23, 23, 23, 23, 9, 23, 41, 29, 29, 29, 41, 32, 32, 29, 29, 35, 32, 41, 41, 41, 41, 41, 23, 44, 44, 41, 47, 47, 44, 50, 50, 41, 53, 53, 50, 3], "deprel": ["det", "nsubj", "root", "mark", "xcomp", "obj", "cc", "nsubj", "conj", "punct", "advcl", "advmod", "case", "det", "obl", "case", "nmod", "punct", "cc", "advmod", "mark", "nsubj", "advcl", "obj", "mark", "case", "nmod:poss", "nummod", "obl", "case", "compound", "nmod", "punct", "case", "nmod", "punct", "mark", "nsubj", "aux", "advmod", "ccomp", "det:predet", "det", "obj", "case", "amod", "nmod", "case", "det", "obl", "case", "det", "nmod", "punct"], "triples": [{"uid": "ADLT#6:4-0", "target_tags": "This\\O guy\\O refused\\O to\\O seat\\O her\\O and\\O she\\O left\\O ,\\O followed\\O shortly\\O by\\O the\\O four\\O of\\O us\\O ,\\O but\\O not\\O before\\O I\\O told\\O him\\O that\\O in\\O my\\O 40\\O years\\O of\\O world\\O travel\\O ,\\O including\\O Paris\\O ,\\O that\\O I\\O had\\O never\\O seen\\O such\\O a\\O display\\O of\\O bad\\O behavior\\O by\\O a\\O frontman\\B in\\O a\\O restaurant\\O .\\O", "opinion_tags": "This\\O guy\\O refused\\O to\\O seat\\O her\\O and\\O she\\O left\\O ,\\O followed\\O shortly\\O by\\O the\\O four\\O of\\O us\\O ,\\O but\\O not\\O before\\O I\\O told\\O him\\O that\\O in\\O my\\O 40\\O years\\O of\\O world\\O travel\\O ,\\O including\\O Paris\\O ,\\O that\\O I\\O had\\O never\\O seen\\O such\\O a\\O display\\O of\\O bad\\B behavior\\O by\\O a\\O frontman\\O in\\O a\\O restaurant\\O .\\O", "sentiment": "negative"}]}, {"id": "NP#9:0", "sentence": "Best meal in a long time !", "postag": ["JJS", "NN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 2], "deprel": ["amod", "root", "case", "det", "amod", "nmod", "punct"], "triples": [{"uid": "NP#9:0-0", "target_tags": "Best\\O meal\\B in\\O a\\O long\\O time\\O !\\O", "opinion_tags": "Best\\B meal\\O in\\O a\\O long\\O time\\O !\\O", "sentiment": "positive"}]}, {"id": "NP#9:1", "sentence": "Mussles and calamari were superb Saturday evening .", "postag": ["NNS", "CC", "NN", "VBD", "JJ", "NNP", "NN", "."], "head": [5, 3, 1, 5, 0, 7, 5, 5], "deprel": ["nsubj", "cc", "conj", "cop", "root", "compound", "obl:tmod", "punct"], "triples": [{"uid": "NP#9:1-0", "target_tags": "Mussles\\B and\\O calamari\\O were\\O superb\\O Saturday\\O evening\\O .\\O", "opinion_tags": "Mussles\\O and\\O calamari\\O were\\O superb\\B Saturday\\O evening\\O .\\O", "sentiment": "positive"}, {"uid": "NP#9:1-1", "target_tags": "Mussles\\O and\\O calamari\\B were\\O superb\\O Saturday\\O evening\\O .\\O", "opinion_tags": "Mussles\\O and\\O calamari\\O were\\O superb\\B Saturday\\O evening\\O .\\O", "sentiment": "positive"}]}, {"id": "NP#9:2", "sentence": "I had the Lamb special which was perfect .", "postag": ["PRP", "VBD", "DT", "NNP", "NN", "WDT", "VBD", "JJ", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 2], "deprel": ["nsubj", "root", "det", "compound", "obj", "nsubj", "cop", "acl:relcl", "punct"], "triples": [{"uid": "NP#9:2-0", "target_tags": "I\\O had\\O the\\O Lamb\\B special\\I which\\O was\\O perfect\\O .\\O", "opinion_tags": "I\\O had\\O the\\O Lamb\\O special\\O which\\O was\\O perfect\\B .\\O", "sentiment": "positive"}]}, {"id": "NP#9:3", "sentence": "My father had the flank steak which was very good , and my mother had the swordfish .", "postag": ["PRP$", "NN", "VBD", "DT", "NN", "NN", "WDT", "VBD", "RB", "JJ", ",", "CC", "PRP$", "NN", "VBD", "DT", "NN", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 6, 15, 15, 14, 15, 3, 17, 15, 3], "deprel": ["nmod:poss", "nsubj", "root", "det", "compound", "obj", "nsubj", "cop", "advmod", "acl:relcl", "punct", "cc", "nmod:poss", "nsubj", "conj", "det", "obj", "punct"], "triples": [{"uid": "NP#9:3-0", "target_tags": "My\\O father\\O had\\O the\\O flank\\B steak\\I which\\O was\\O very\\O good\\O ,\\O and\\O my\\O mother\\O had\\O the\\O swordfish\\O .\\O", "opinion_tags": "My\\O father\\O had\\O the\\O flank\\O steak\\O which\\O was\\O very\\O good\\B ,\\O and\\O my\\O mother\\O had\\O the\\O swordfish\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#2:0", "sentence": "The Four Seasons restaurant is a great experience .", "postag": ["DT", "CD", "NNPS", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [4, 3, 4, 8, 8, 8, 8, 0, 8], "deprel": ["det", "nummod", "compound", "nsubj", "cop", "det", "amod", "root", "punct"], "triples": [{"uid": "TFS#2:0-0", "target_tags": "The\\B Four\\I Seasons\\I restaurant\\I is\\O a\\O great\\O experience\\O .\\O", "opinion_tags": "The\\O Four\\O Seasons\\O restaurant\\O is\\O a\\O great\\B experience\\O .\\O", "sentiment": "positive"}]}, {"id": "TFS#2:1", "sentence": "The food is great and the environment is even better .", "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "RB", "JJR", "."], "head": [2, 4, 4, 0, 10, 7, 10, 10, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "advmod", "conj", "punct"], "triples": [{"uid": "TFS#2:1-0", "target_tags": "The\\O food\\B is\\O great\\O and\\O the\\O environment\\O is\\O even\\O better\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\B and\\O the\\O environment\\O is\\O even\\O better\\O .\\O", "sentiment": "positive"}, {"uid": "TFS#2:1-1", "target_tags": "The\\O food\\O is\\O great\\O and\\O the\\O environment\\B is\\O even\\O better\\O .\\O", "opinion_tags": "The\\O food\\O is\\O great\\O and\\O the\\O environment\\O is\\O even\\O better\\B .\\O", "sentiment": "positive"}]}, {"id": "BHD#8:2", "sentence": "Here the hot dog is elevated to the level of a real entree with numerous variations available .", "postag": ["RB", "DT", "JJ", "NN", "VBZ", "VBN", "IN", "DT", "NN", "IN", "DT", "JJ", "NN", "IN", "JJ", "NNS", "JJ", "."], "head": [6, 4, 4, 6, 6, 0, 9, 9, 6, 13, 13, 13, 9, 16, 16, 13, 16, 1], "deprel": ["advmod", "det", "amod", "nsubj:pass", "aux:pass", "root", "case", "det", "obl", "case", "det", "amod", "nmod", "case", "amod", "nmod", "amod", "punct"], "triples": [{"uid": "BHD#8:2-0", "target_tags": "Here\\O the\\O hot\\B dog\\I is\\O elevated\\O to\\O the\\O level\\O of\\O a\\O real\\O entree\\O with\\O numerous\\O variations\\O available\\O .\\O", "opinion_tags": "Here\\O the\\O hot\\O dog\\O is\\O elevated\\B to\\O the\\O level\\O of\\O a\\O real\\O entree\\O with\\O numerous\\O variations\\O available\\O .\\O", "sentiment": "positive"}]}, {"id": "P#3:0", "sentence": "Great Atmosphere", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "P#3:0-0", "target_tags": "Great\\O Atmosphere\\B", "opinion_tags": "Great\\B Atmosphere\\O", "sentiment": "positive"}]}, {"id": "P#3:2", "sentence": "I highly recommend the fish tacos , everything else was ok .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "NNS", ",", "NN", "JJ", "VBD", "JJ", "."], "head": [3, 3, 0, 6, 6, 3, 3, 11, 8, 11, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "compound", "obj", "punct", "nsubj", "amod", "cop", "parataxis", "punct"], "triples": [{"uid": "P#3:2-0", "target_tags": "I\\O highly\\O recommend\\O the\\O fish\\B tacos\\I ,\\O everything\\O else\\O was\\O ok\\O .\\O", "opinion_tags": "I\\O highly\\O recommend\\B the\\O fish\\O tacos\\O ,\\O everything\\O else\\O was\\O ok\\O .\\O", "sentiment": "positive"}]}, {"id": "P#3:3", "sentence": "Cool atmosphere , the fire place in the back really ads to it but needs a bit more heat throughout on a cold night .", "postag": ["JJ", "NN", ",", "DT", "NN", "NN", "IN", "DT", "NN", "RB", "VBZ", "IN", "PRP", "CC", "VBZ", "DT", "NN", "JJR", "NN", "IN", "IN", "DT", "JJ", "NN", "."], "head": [2, 0, 2, 6, 6, 2, 9, 9, 6, 11, 2, 13, 11, 15, 11, 17, 18, 19, 15, 24, 24, 24, 24, 15, 2], "deprel": ["amod", "root", "punct", "det", "compound", "conj", "case", "det", "nmod", "advmod", "parataxis", "case", "obl", "cc", "conj", "det", "obl:npmod", "amod", "obj", "case", "case", "det", "amod", "obl", "punct"], "triples": [{"uid": "P#3:3-0", "target_tags": "Cool\\O atmosphere\\B ,\\O the\\O fire\\O place\\O in\\O the\\O back\\O really\\O ads\\O to\\O it\\O but\\O needs\\O a\\O bit\\O more\\O heat\\O throughout\\O on\\O a\\O cold\\O night\\O .\\O", "opinion_tags": "Cool\\B atmosphere\\O ,\\O the\\O fire\\O place\\O in\\O the\\O back\\O really\\O ads\\O to\\O it\\O but\\O needs\\O a\\O bit\\O more\\O heat\\O throughout\\O on\\O a\\O cold\\O night\\O .\\O", "sentiment": "positive"}]}, {"id": "CLF#8:0", "sentence": "Poor service and management", "postag": ["JJ", "NN", "CC", "NN"], "head": [2, 0, 4, 2], "deprel": ["amod", "root", "cc", "conj"], "triples": [{"uid": "CLF#8:0-0", "target_tags": "Poor\\O service\\B and\\O management\\O", "opinion_tags": "Poor\\B service\\O and\\O management\\O", "sentiment": "negative"}, {"uid": "CLF#8:0-1", "target_tags": "Poor\\O service\\O and\\O management\\B", "opinion_tags": "Poor\\B service\\O and\\O management\\O", "sentiment": "negative"}]}, {"id": "CLF#8:2", "sentence": "Had an awful experience at Casa la Femme on a Saturday dinner .", "postag": ["VBD", "DT", "JJ", "NN", "IN", "NNP", "NNP", "NNP", "IN", "DT", "NNP", "NN", "."], "head": [0, 4, 4, 1, 7, 7, 4, 7, 12, 12, 12, 1, 1], "deprel": ["root", "det", "amod", "obj", "case", "compound", "nmod", "flat", "case", "det", "compound", "obl", "punct"], "triples": [{"uid": "CLF#8:2-0", "target_tags": "Had\\O an\\O awful\\O experience\\O at\\O Casa\\B la\\I Femme\\I on\\O a\\O Saturday\\O dinner\\O .\\O", "opinion_tags": "Had\\O an\\O awful\\B experience\\O at\\O Casa\\O la\\O Femme\\O on\\O a\\O Saturday\\O dinner\\O .\\O", "sentiment": "negative"}]}, {"id": "CLF#8:6", "sentence": "The manager was rude and handled the situation extremely poorly .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "VBD", "DT", "NN", "RB", "RB", "."], "head": [2, 4, 4, 0, 6, 4, 8, 6, 10, 6, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "det", "obj", "advmod", "advmod", "punct"], "triples": [{"uid": "CLF#8:6-0", "target_tags": "The\\O manager\\B was\\O rude\\O and\\O handled\\O the\\O situation\\O extremely\\O poorly\\O .\\O", "opinion_tags": "The\\O manager\\O was\\O rude\\B and\\O handled\\O the\\O situation\\O extremely\\O poorly\\O .\\O", "sentiment": "negative"}]}, {"id": "CLF#8:9", "sentence": "Can ’ t believe how an expensive NYC restaurant can be so disrespectful to its clients .", "postag": ["MD", "``", "NN", "VB", "WRB", "DT", "JJ", "NNP", "NN", "MD", "VB", "RB", "JJ", "IN", "PRP$", "NNS", "."], "head": [4, 4, 4, 0, 13, 9, 9, 9, 13, 13, 13, 13, 4, 16, 16, 13, 4], "deprel": ["aux", "punct", "nsubj", "root", "mark", "det", "amod", "compound", "nsubj", "aux", "cop", "advmod", "ccomp", "case", "nmod:poss", "obl", "punct"], "triples": [{"uid": "CLF#8:9-0", "target_tags": "Can\\O ’\\O t\\O believe\\O how\\O an\\O expensive\\O NYC\\O restaurant\\B can\\O be\\O so\\O disrespectful\\O to\\O its\\O clients\\O .\\O", "opinion_tags": "Can\\O ’\\O t\\O believe\\O how\\O an\\O expensive\\B NYC\\O restaurant\\O can\\O be\\O so\\O disrespectful\\O to\\O its\\O clients\\O .\\O", "sentiment": "negative"}]}, {"id": "ADLT#11:1", "sentence": "The food is very good , but not outstanding .", "postag": ["DT", "NN", "VBZ", "RB", "JJ", ",", "CC", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "cc", "advmod", "conj", "punct"], "triples": [{"uid": "ADLT#11:1-0", "target_tags": "The\\O food\\B is\\O very\\O good\\O ,\\O but\\O not\\O outstanding\\O .\\O", "opinion_tags": "The\\O food\\O is\\O very\\O good\\B ,\\O but\\O not\\B outstanding\\I .\\O", "sentiment": "neutral"}]}, {"id": "WE#1:3", "sentence": "The bread was stale , the salad was overpriced and empty .", "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 4, 7, 9, 9, 4, 11, 9, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "parataxis", "cc", "conj", "punct"], "triples": [{"uid": "WE#1:3-0", "target_tags": "The\\O bread\\B was\\O stale\\O ,\\O the\\O salad\\O was\\O overpriced\\O and\\O empty\\O .\\O", "opinion_tags": "The\\O bread\\O was\\O stale\\B ,\\O the\\O salad\\O was\\O overpriced\\O and\\O empty\\O .\\O", "sentiment": "negative"}, {"uid": "WE#1:3-1", "target_tags": "The\\O bread\\O was\\O stale\\O ,\\O the\\O salad\\B was\\O overpriced\\O and\\O empty\\O .\\O", "opinion_tags": "The\\O bread\\O was\\O stale\\O ,\\O the\\O salad\\O was\\O overpriced\\B and\\O empty\\B .\\O", "sentiment": "negative"}]}, {"id": "WE#1:4", "sentence": "The pasta was well cooked , did n't have enough sauce though or flavor .", "postag": ["DT", "NN", "VBD", "RB", "VBN", ",", "VBD", "RB", "VB", "JJ", "NN", "RB", "CC", "NN", "."], "head": [2, 5, 5, 5, 0, 9, 9, 9, 5, 11, 9, 9, 14, 12, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "punct", "aux", "advmod", "conj", "amod", "obj", "advmod", "cc", "conj", "punct"], "triples": [{"uid": "WE#1:4-0", "target_tags": "The\\O pasta\\B was\\O well\\O cooked\\O ,\\O did\\O n't\\O have\\O enough\\O sauce\\O though\\O or\\O flavor\\O .\\O", "opinion_tags": "The\\O pasta\\O was\\O well\\B cooked\\I ,\\O did\\O n't\\O have\\O enough\\O sauce\\O though\\O or\\O flavor\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#6:5", "sentence": "The hostess was rude and I got a distinct feeling that they did not want to serve us .", "postag": ["DT", "NN", "VBD", "JJ", "CC", "PRP", "VBD", "DT", "JJ", "NN", "IN", "PRP", "VBD", "RB", "VB", "TO", "VB", "PRP", "."], "head": [2, 4, 4, 0, 7, 7, 4, 10, 10, 7, 15, 15, 15, 15, 10, 17, 15, 17, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "nsubj", "conj", "det", "amod", "obj", "mark", "nsubj", "aux", "advmod", "acl", "mark", "xcomp", "obj", "punct"], "triples": [{"uid": "FF#6:5-0", "target_tags": "The\\O hostess\\B was\\O rude\\O and\\O I\\O got\\O a\\O distinct\\O feeling\\O that\\O they\\O did\\O not\\O want\\O to\\O serve\\O us\\O .\\O", "opinion_tags": "The\\O hostess\\O was\\O rude\\B and\\O I\\O got\\O a\\O distinct\\O feeling\\O that\\O they\\O did\\O not\\O want\\O to\\O serve\\O us\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#6:6", "sentence": "The only thing that my friend left out is that when we sat down at the bar the bartender disappeared .", "postag": ["DT", "JJ", "NN", "WDT", "PRP$", "NN", "VBD", "RP", "VBZ", "IN", "WRB", "PRP", "VBD", "RP", "IN", "DT", "NN", "DT", "NN", "VBD", "."], "head": [3, 3, 9, 7, 6, 7, 3, 7, 0, 20, 13, 13, 20, 13, 17, 17, 13, 19, 20, 9, 9], "deprel": ["det", "amod", "nsubj", "obj", "nmod:poss", "nsubj", "acl:relcl", "compound:prt", "root", "mark", "mark", "nsubj", "advcl", "compound:prt", "case", "det", "obl", "det", "nsubj", "ccomp", "punct"], "triples": [{"uid": "FF#6:6-0", "target_tags": "The\\O only\\O thing\\O that\\O my\\O friend\\O left\\O out\\O is\\O that\\O when\\O we\\O sat\\O down\\O at\\O the\\O bar\\O the\\O bartender\\B disappeared\\O .\\O", "opinion_tags": "The\\O only\\O thing\\O that\\O my\\O friend\\O left\\O out\\O is\\O that\\O when\\O we\\O sat\\O down\\O at\\O the\\O bar\\O the\\O bartender\\O disappeared\\B .\\O", "sentiment": "negative"}]}, {"id": "FF#6:12", "sentence": "Shame on this place for the horrible rude staff and non-existent customer service .", "postag": ["NN", "IN", "DT", "NN", "IN", "DT", "JJ", "JJ", "NN", "CC", "JJ", "NN", "NN", "."], "head": [0, 4, 4, 1, 9, 9, 9, 9, 1, 13, 13, 13, 9, 1], "deprel": ["root", "case", "det", "nmod", "case", "det", "amod", "amod", "nmod", "cc", "amod", "compound", "conj", "punct"], "triples": [{"uid": "FF#6:12-0", "target_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\O staff\\B and\\O non-existent\\O customer\\O service\\O .\\O", "opinion_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\B staff\\O and\\O non-existent\\O customer\\O service\\O .\\O", "sentiment": "negative"}, {"uid": "FF#6:12-1", "target_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\O staff\\O and\\O non-existent\\O customer\\B service\\I .\\O", "opinion_tags": "Shame\\O on\\O this\\O place\\O for\\O the\\O horrible\\O rude\\O staff\\O and\\O non-existent\\B customer\\O service\\O .\\O", "sentiment": "negative"}]}, {"id": "FF#10:0", "sentence": "bad staff", "postag": ["JJ", "NN"], "head": [2, 0], "deprel": ["amod", "root"], "triples": [{"uid": "FF#10:0-0", "target_tags": "bad\\O staff\\B", "opinion_tags": "bad\\B staff\\O", "sentiment": "negative"}]}, {"id": "FF#10:1", "sentence": "I generally like this place .", "postag": ["PRP", "RB", "VBP", "DT", "NN", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "obj", "punct"], "triples": [{"uid": "FF#10:1-0", "target_tags": "I\\O generally\\O like\\O this\\O place\\B .\\O", "opinion_tags": "I\\O generally\\O like\\B this\\O place\\O .\\O", "sentiment": "positive"}]}, {"id": "FF#10:2", "sentence": "The food is good .", "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "FF#10:2-0", "target_tags": "The\\O food\\B is\\O good\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "FF#10:3", "sentence": "The design of the space is good .", "postag": ["DT", "NN", "IN", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 7, 5, 5, 2, 7, 0, 7], "deprel": ["det", "nsubj", "case", "det", "nmod", "cop", "root", "punct"], "triples": [{"uid": "FF#10:3-0", "target_tags": "The\\O design\\O of\\O the\\O space\\B is\\O good\\O .\\O", "opinion_tags": "The\\O design\\O of\\O the\\O space\\O is\\O good\\B .\\O", "sentiment": "positive"}]}, {"id": "FF#10:4", "sentence": "But the service is HORRID !", "postag": ["CC", "DT", "NN", "VBZ", "JJ", "."], "head": [5, 3, 5, 5, 0, 5], "deprel": ["cc", "det", "nsubj", "cop", "root", "punct"], "triples": [{"uid": "FF#10:4-0", "target_tags": "But\\O the\\O service\\B is\\O HORRID\\O !\\O", "opinion_tags": "But\\O the\\O service\\O is\\O HORRID\\B !\\O", "sentiment": "negative"}]}]