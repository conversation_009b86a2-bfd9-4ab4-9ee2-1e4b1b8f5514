[{"id": "0", "sentence": "The food is very average ... the Thai fusion stuff is a bit too sweet , every thing they serve is too sweet here .", "triples": [{"uid": "0-0", "sentiment": "negative", "target_tags": "The\\O food\\B is\\O very\\O average\\O ...\\O the\\O Thai\\O fusion\\O stuff\\O is\\O a\\O bit\\O too\\O sweet\\O ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O", "opinion_tags": "The\\O food\\O is\\O very\\O average\\B ...\\O the\\O Thai\\O fusion\\O stuff\\O is\\O a\\O bit\\O too\\O sweet\\O ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O"}, {"uid": "0-1", "sentiment": "negative", "target_tags": "The\\O food\\O is\\O very\\O average\\O ...\\O the\\O Thai\\B fusion\\I stuff\\I is\\O a\\O bit\\O too\\O sweet\\O ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O", "opinion_tags": "The\\O food\\O is\\O very\\O average\\O ...\\O the\\O Thai\\O fusion\\O stuff\\O is\\O a\\O bit\\O too\\B sweet\\I ,\\O every\\O thing\\O they\\O serve\\O is\\O too\\O sweet\\O here\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", ",", "DT", "JJ", "NN", "NN", "VBZ", "DT", "NN", "RB", "JJ", ",", "DT", "NN", "PRP", "VBP", "VBZ", "RB", "JJ", "RB", "."], "head": [2, 5, 5, 5, 0, 5, 10, 10, 10, 15, 15, 13, 14, 15, 5, 23, 18, 23, 20, 18, 23, 23, 5, 23, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "det", "amod", "compound", "nsubj", "cop", "det", "nmod:npmod", "advmod", "parataxis", "punct", "det", "nsubj", "nsubj", "acl:relcl", "cop", "advmod", "parataxis", "advmod", "punct"]}, {"id": "1", "sentence": "The decor is night tho ... but they REALLY need to clean that vent in the ceiling ... its quite un-appetizing , and kills your effort to make this place look sleek and modern .", "triples": [{"uid": "1-0", "sentiment": "negative", "target_tags": "The\\O decor\\O is\\O night\\O tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\O in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\O ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\B look\\O sleek\\O and\\O modern\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O night\\O tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\O in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\O ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\O look\\O sleek\\B and\\O modern\\O .\\O"}, {"uid": "1-1", "sentiment": "negative", "target_tags": "The\\O decor\\O is\\O night\\O tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\O in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\O ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\B look\\O sleek\\O and\\O modern\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O night\\O tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\O in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\O ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\O look\\O sleek\\O and\\O modern\\B .\\O"}, {"uid": "1-2", "sentiment": "positive", "target_tags": "The\\O decor\\B is\\O night\\O tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\O in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\O ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\O look\\O sleek\\O and\\O modern\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O night\\B tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\O in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\O ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\O look\\O sleek\\O and\\O modern\\O .\\O"}, {"uid": "1-3", "sentiment": "negative", "target_tags": "The\\O decor\\O is\\O night\\O tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\B in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\O ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\O look\\O sleek\\O and\\O modern\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O night\\O tho\\O ...\\O but\\O they\\O REALLY\\O need\\O to\\O clean\\O that\\O vent\\O in\\O the\\O ceiling\\O ...\\O its\\O quite\\O un-appetizing\\B ,\\O and\\O kills\\O your\\O effort\\O to\\O make\\O this\\O place\\O look\\O sleek\\O and\\O modern\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "NN", "NN", ",", "CC", "PRP", "RB", "VBP", "TO", "VB", "DT", "NN", "IN", "DT", "NN", ",", "PRP$", "RB", "JJ", ",", "CC", "VBZ", "PRP$", "NN", "TO", "VB", "DT", "NN", "VB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 5, 10, 10, 10, 5, 12, 10, 14, 12, 17, 17, 12, 21, 21, 21, 12, 24, 24, 10, 26, 24, 28, 24, 30, 28, 28, 31, 34, 32, 5], "deprel": ["det", "nsubj", "cop", "compound", "root", "punct", "cc", "nsubj", "advmod", "conj", "mark", "xcomp", "det", "obj", "case", "det", "obl", "punct", "nmod:poss", "advmod", "advcl", "punct", "cc", "conj", "nmod:poss", "obj", "mark", "advcl", "det", "obj", "xcomp", "xcomp", "cc", "conj", "punct"]}, {"id": "2", "sentence": "The spicy tuna roll was unusually good and the rock shrimp tempura was awesome , great appetizer to share !", "triples": [{"uid": "2-0", "sentiment": "positive", "target_tags": "The\\O spicy\\B tuna\\I roll\\I was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\B and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O"}, {"uid": "2-1", "sentiment": "positive", "target_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\B shrimp\\I tempura\\I was\\O awesome\\O ,\\O great\\O appetizer\\O to\\O share\\O !\\O", "opinion_tags": "The\\O spicy\\O tuna\\O roll\\O was\\O unusually\\O good\\O and\\O the\\O rock\\O shrimp\\O tempura\\O was\\O awesome\\B ,\\O great\\O appetizer\\O to\\O share\\O !\\O"}], "postag": ["DT", "JJ", "NN", "NN", "VBD", "RB", "JJ", "CC", "DT", "NN", "NN", "NN", "VBD", "JJ", ",", "JJ", "NN", "TO", "VB", "."], "head": [4, 4, 4, 7, 7, 7, 0, 14, 12, 12, 12, 14, 14, 7, 17, 17, 14, 19, 17, 7], "deprel": ["det", "amod", "compound", "nsubj", "cop", "advmod", "root", "cc", "det", "compound", "compound", "nsubj", "cop", "conj", "punct", "amod", "conj", "mark", "acl", "punct"]}, {"id": "3", "sentence": "we love th pink pony .", "triples": [{"uid": "3-0", "sentiment": "positive", "target_tags": "we\\O love\\O th\\O pink\\B pony\\I .\\O", "opinion_tags": "we\\O love\\B th\\O pink\\O pony\\O .\\O"}], "postag": ["PRP", "VBP", "DT", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "obj", "punct"]}, {"id": "4", "sentence": "THe perfect spot .", "triples": [{"uid": "4-0", "sentiment": "positive", "target_tags": "THe\\O perfect\\O spot\\B .\\O", "opinion_tags": "THe\\O perfect\\B spot\\O .\\O"}], "postag": ["DT", "JJ", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["det", "amod", "root", "punct"]}, {"id": "5", "sentence": "Ambiance relaxed and stylish .", "triples": [{"uid": "5-0", "sentiment": "positive", "target_tags": "Ambiance\\B relaxed\\O and\\O stylish\\O .\\O", "opinion_tags": "Ambiance\\O relaxed\\B and\\O stylish\\O .\\O"}, {"uid": "5-1", "sentiment": "positive", "target_tags": "Ambiance\\B relaxed\\O and\\O stylish\\O .\\O", "opinion_tags": "Ambiance\\O relaxed\\O and\\O stylish\\B .\\O"}], "postag": ["NN", "JJ", "CC", "JJ", "."], "head": [0, 1, 4, 2, 1], "deprel": ["root", "amod", "cc", "conj", "punct"]}, {"id": "6", "sentence": "however , it 's the service that leaves a bad taste in my mouth .", "triples": [{"uid": "6-0", "sentiment": "negative", "target_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\B that\\O leaves\\O a\\O bad\\O taste\\O in\\O my\\O mouth\\O .\\O", "opinion_tags": "however\\O ,\\O it\\O 's\\O the\\O service\\O that\\O leaves\\O a\\O bad\\B taste\\I in\\O my\\O mouth\\O .\\O"}], "postag": ["RB", ",", "PRP", "VBZ", "DT", "NN", "WDT", "VBZ", "DT", "JJ", "NN", "IN", "PRP$", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 8, 6, 11, 11, 8, 14, 14, 8, 6], "deprel": ["advmod", "punct", "nsubj", "cop", "det", "root", "nsubj", "acl:relcl", "det", "amod", "obj", "case", "nmod:poss", "obl", "punct"]}, {"id": "7", "sentence": "i happen to have a policy that goes along with a little bit of self-respect , which includes not letting a waiter intimidate me , i.e . make me feel bad asking for trivialities like water , or the check .", "triples": [{"uid": "7-0", "sentiment": "negative", "target_tags": "i\\O happen\\O to\\O have\\O a\\O policy\\O that\\O goes\\O along\\O with\\O a\\O little\\O bit\\O of\\O self-respect\\O ,\\O which\\O includes\\O not\\O letting\\O a\\O waiter\\B intimidate\\O me\\O ,\\O i.e\\O .\\O make\\O me\\O feel\\O bad\\O asking\\O for\\O trivialities\\O like\\O water\\O ,\\O or\\O the\\O check\\O .\\O", "opinion_tags": "i\\O happen\\O to\\O have\\O a\\O policy\\O that\\O goes\\O along\\O with\\O a\\O little\\O bit\\O of\\O self-respect\\O ,\\O which\\O includes\\O not\\O letting\\O a\\O waiter\\O intimidate\\O me\\O ,\\O i.e\\O .\\O make\\O me\\O feel\\O bad\\B asking\\O for\\O trivialities\\O like\\O water\\O ,\\O or\\O the\\O check\\O .\\O"}], "postag": ["PRP", "VBP", "TO", "VB", "DT", "NN", "WDT", "VBZ", "RB", "IN", "DT", "JJ", "NN", "IN", "NN", ",", "WDT", "VBZ", "RB", "VBG", "DT", "NN", "VB", "PRP", ",", "RB", ".", "VB", "PRP", "VB", "JJ", "VBG", "IN", "NNS", "IN", "NN", ",", "CC", "DT", "NN", "."], "head": [2, 0, 4, 2, 6, 4, 8, 6, 8, 13, 13, 13, 8, 15, 13, 18, 18, 6, 20, 18, 22, 20, 20, 23, 28, 28, 28, 2, 28, 28, 30, 30, 34, 32, 36, 34, 40, 40, 40, 36, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "det", "obj", "nsubj", "acl:relcl", "advmod", "case", "det", "amod", "obl", "case", "nmod", "punct", "nsubj", "acl:relcl", "advmod", "xcomp", "det", "obj", "xcomp", "obj", "punct", "advmod", "punct", "parataxis", "obj", "xcomp", "xcomp", "xcomp", "case", "obl", "case", "nmod", "punct", "cc", "det", "conj", "punct"]}, {"id": "8", "sentence": "I tend to judge a sushi restaurant by its sea urchin , which was heavenly at sushi rose .", "triples": [{"uid": "8-0", "sentiment": "positive", "target_tags": "I\\O tend\\O to\\O judge\\O a\\O sushi\\O restaurant\\O by\\O its\\O sea\\B urchin\\I ,\\O which\\O was\\O heavenly\\O at\\O sushi\\O rose\\O .\\O", "opinion_tags": "I\\O tend\\O to\\O judge\\O a\\O sushi\\O restaurant\\O by\\O its\\O sea\\O urchin\\O ,\\O which\\O was\\O heavenly\\B at\\O sushi\\O rose\\O .\\O"}], "postag": ["PRP", "VBP", "TO", "VB", "DT", "NN", "NN", "IN", "PRP$", "NN", "NN", ",", "WDT", "VBD", "JJ", "IN", "NN", "VBD", "."], "head": [2, 0, 4, 2, 7, 7, 4, 11, 11, 11, 4, 11, 15, 15, 11, 17, 15, 15, 2], "deprel": ["nsubj", "root", "mark", "xcomp", "det", "compound", "obj", "case", "nmod:poss", "compound", "obl", "punct", "nsubj", "cop", "acl:relcl", "case", "obl", "advcl", "punct"]}, {"id": "9", "sentence": "The sushi seemed pretty fresh and was adequately proportioned .", "triples": [{"uid": "9-0", "sentiment": "positive", "target_tags": "The\\O sushi\\B seemed\\O pretty\\O fresh\\O and\\O was\\O adequately\\O proportioned\\O .\\O", "opinion_tags": "The\\O sushi\\O seemed\\O pretty\\O fresh\\B and\\O was\\O adequately\\O proportioned\\O .\\O"}, {"uid": "9-1", "sentiment": "positive", "target_tags": "The\\O sushi\\B seemed\\O pretty\\O fresh\\O and\\O was\\O adequately\\O proportioned\\O .\\O", "opinion_tags": "The\\O sushi\\O seemed\\O pretty\\O fresh\\O and\\O was\\O adequately\\O proportioned\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "VBD", "RB", "VBN", "."], "head": [2, 3, 0, 5, 3, 9, 9, 9, 3, 3], "deprel": ["det", "nsubj", "root", "advmod", "xcomp", "cc", "aux:pass", "advmod", "conj", "punct"]}, {"id": "10", "sentence": "The rice to fish ration was also good -- they did n't try to overpack the rice .", "triples": [{"uid": "10-0", "sentiment": "positive", "target_tags": "The\\O rice\\B to\\I fish\\I ration\\I was\\O also\\O good\\O --\\O they\\O did\\O n't\\O try\\O to\\O overpack\\O the\\O rice\\O .\\O", "opinion_tags": "The\\O rice\\O to\\O fish\\O ration\\O was\\O also\\O good\\B --\\O they\\O did\\O n't\\O try\\O to\\O overpack\\O the\\O rice\\O .\\O"}], "postag": ["DT", "NN", "IN", "NN", "NN", "VBD", "RB", "JJ", ",", "PRP", "VBD", "RB", "VB", "TO", "VB", "DT", "NN", "."], "head": [2, 8, 5, 5, 2, 8, 8, 0, 8, 13, 13, 13, 8, 15, 13, 17, 15, 8], "deprel": ["det", "nsubj", "case", "compound", "nmod", "cop", "advmod", "root", "punct", "nsubj", "aux", "advmod", "parataxis", "mark", "xcomp", "det", "obj", "punct"]}, {"id": "11", "sentence": "The food was well prepared and the service impecable .", "triples": [{"uid": "11-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O well\\O prepared\\O and\\O the\\O service\\O impecable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O well\\B prepared\\I and\\O the\\O service\\O impecable\\O .\\O"}, {"uid": "11-1", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O well\\O prepared\\O and\\O the\\O service\\B impecable\\O .\\O", "opinion_tags": "The\\O food\\O was\\O well\\O prepared\\O and\\O the\\O service\\O impecable\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "VBN", "CC", "DT", "NN", "JJ", "."], "head": [2, 5, 5, 5, 0, 9, 8, 9, 5, 5], "deprel": ["det", "nsubj:pass", "aux:pass", "advmod", "root", "cc", "det", "nsubj", "conj", "punct"]}, {"id": "12", "sentence": "The Prix Fixe menu is worth every penny and you get more than enough ( both in quantity AND quality ) .", "triples": [{"uid": "12-0", "sentiment": "positive", "target_tags": "The\\O Prix\\B Fixe\\I menu\\I is\\O worth\\O every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O", "opinion_tags": "The\\O Prix\\O Fixe\\O menu\\O is\\O worth\\B every\\O penny\\O and\\O you\\O get\\O more\\O than\\O enough\\O (\\O both\\O in\\O quantity\\O AND\\O quality\\O )\\O .\\O"}], "postag": ["DT", "NNP", "NNP", "NN", "VBZ", "JJ", "DT", "NN", "CC", "PRP", "VBP", "JJR", "IN", "JJ", "-LRB-", "CC", "IN", "NN", "CC", "NN", "-RRB-", "."], "head": [4, 3, 4, 6, 6, 0, 8, 6, 11, 11, 6, 14, 12, 11, 18, 18, 18, 11, 20, 18, 18, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "det", "obj", "cc", "nsubj", "conj", "advmod", "fixed", "obj", "punct", "cc:preconj", "case", "obl", "cc", "conj", "punct", "punct"]}, {"id": "13", "sentence": "The kitchen however , is almost always slow .", "triples": [{"uid": "13-0", "sentiment": "negative", "target_tags": "The\\O kitchen\\B however\\O ,\\O is\\O almost\\O always\\O slow\\O .\\O", "opinion_tags": "The\\O kitchen\\O however\\O ,\\O is\\O almost\\O always\\O slow\\B .\\O"}], "postag": ["DT", "NN", "RB", ",", "VBZ", "RB", "RB", "JJ", "."], "head": [2, 8, 8, 8, 8, 7, 8, 0, 8], "deprel": ["det", "nsubj", "advmod", "punct", "cop", "advmod", "advmod", "root", "punct"]}, {"id": "14", "sentence": "Add to that great service and great food at a reasonable price and you have yourself the beginning of a great evening .", "triples": [{"uid": "14-0", "sentiment": "positive", "target_tags": "Add\\O to\\O that\\O great\\O service\\B and\\O great\\O food\\O at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O", "opinion_tags": "Add\\O to\\O that\\O great\\B service\\O and\\O great\\O food\\O at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O"}, {"uid": "14-1", "sentiment": "positive", "target_tags": "Add\\O to\\O that\\O great\\O service\\O and\\O great\\O food\\B at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O", "opinion_tags": "Add\\O to\\O that\\O great\\O service\\O and\\O great\\B food\\O at\\O a\\O reasonable\\O price\\O and\\O you\\O have\\O yourself\\O the\\O beginning\\O of\\O a\\O great\\O evening\\O .\\O"}], "postag": ["VB", "IN", "DT", "JJ", "NN", "CC", "JJ", "NN", "IN", "DT", "JJ", "NN", "CC", "PRP", "VBP", "PRP", "DT", "NN", "IN", "DT", "JJ", "NN", "."], "head": [0, 5, 5, 5, 1, 8, 8, 5, 12, 12, 12, 1, 15, 15, 1, 15, 18, 15, 22, 22, 22, 18, 1], "deprel": ["root", "case", "det", "amod", "obl", "cc", "amod", "conj", "case", "det", "amod", "obl", "cc", "nsubj", "conj", "i<PERSON><PERSON>", "det", "obj", "case", "det", "amod", "nmod", "punct"]}, {"id": "15", "sentence": "The food was average to above-average ; the French Onion soup filling yet not overly impressive , and the desserts not brilliant in any way .", "triples": [{"uid": "15-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O", "opinion_tags": "The\\O food\\O was\\O average\\B to\\I above-average\\I ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O"}, {"uid": "15-1", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\B Onion\\I soup\\I filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O", "opinion_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\B overly\\I impressive\\I ,\\O and\\O the\\O desserts\\O not\\O brilliant\\O in\\O any\\O way\\O .\\O"}, {"uid": "15-2", "sentiment": "negative", "target_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\B not\\O brilliant\\O in\\O any\\O way\\O .\\O", "opinion_tags": "The\\O food\\O was\\O average\\O to\\O above-average\\O ;\\O the\\O French\\O Onion\\O soup\\O filling\\O yet\\O not\\O overly\\O impressive\\O ,\\O and\\O the\\O desserts\\O not\\B brilliant\\I in\\O any\\O way\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "IN", "JJ", ",", "DT", "JJ", "NN", "NN", "VBG", "RB", "RB", "RB", "JJ", ",", "CC", "DT", "NNS", "RB", "JJ", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4, 11, 11, 11, 4, 11, 16, 16, 16, 12, 22, 22, 20, 22, 22, 4, 25, 25, 22, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "obl", "punct", "det", "amod", "compound", "parataxis", "acl", "advmod", "advmod", "advmod", "xcomp", "punct", "cc", "det", "nsubj", "advmod", "conj", "case", "det", "obl", "punct"]}, {"id": "16", "sentence": "This place is the most Japanese it can ever get .", "triples": [{"uid": "16-0", "sentiment": "positive", "target_tags": "This\\O place\\B is\\O the\\O most\\O Japanese\\O it\\O can\\O ever\\O get\\O .\\O", "opinion_tags": "This\\O place\\O is\\O the\\O most\\O Japanese\\B it\\O can\\O ever\\O get\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "RBS", "JJ", "PRP", "MD", "RB", "VB", "."], "head": [2, 6, 6, 6, 6, 0, 10, 10, 10, 6, 6], "deprel": ["det", "nsubj", "cop", "det", "advmod", "root", "nsubj", "aux", "advmod", "ccomp", "punct"]}, {"id": "17", "sentence": "<PERSON> is an East Village gem : casual but hip , with well prepared basic French bistro fare , good specials , a warm and lively atmosphere .", "triples": [{"uid": "17-0", "sentiment": "positive", "target_tags": "Leon\\B is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\B but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "17-1", "sentiment": "positive", "target_tags": "Leon\\B is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\B ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "17-2", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\B ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\B specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "17-3", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\B .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\B and\\O lively\\O atmosphere\\O .\\O"}, {"uid": "17-4", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\B .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\B atmosphere\\O .\\O"}, {"uid": "17-5", "sentiment": "positive", "target_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\O prepared\\O basic\\O French\\B bistro\\I fare\\I ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O", "opinion_tags": "Leon\\O is\\O an\\O East\\O Village\\O gem\\O :\\O casual\\O but\\O hip\\O ,\\O with\\O well\\B prepared\\I basic\\O French\\O bistro\\O fare\\O ,\\O good\\O specials\\O ,\\O a\\O warm\\O and\\O lively\\O atmosphere\\O .\\O"}], "postag": ["NNP", "VBZ", "DT", "NNP", "NNP", "NN", ":", "JJ", "CC", "NN", ",", "IN", "RB", "VBN", "JJ", "JJ", "NN", "NN", ",", "JJ", "NNS", ",", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [6, 6, 6, 5, 6, 0, 6, 6, 10, 8, 18, 18, 14, 18, 18, 18, 18, 8, 21, 21, 18, 27, 27, 27, 26, 24, 18, 6], "deprel": ["nsubj", "cop", "det", "compound", "compound", "root", "punct", "amod", "cc", "conj", "punct", "case", "advmod", "amod", "amod", "amod", "compound", "obl", "punct", "amod", "conj", "punct", "det", "amod", "cc", "conj", "conj", "punct"]}, {"id": "18", "sentence": "The food was bland oily .", "triples": [{"uid": "18-0", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O bland\\O oily\\O .\\O", "opinion_tags": "The\\O food\\O was\\O bland\\B oily\\I .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "amod", "root", "punct"]}, {"id": "19", "sentence": "I went there for lunch and it was not as good as I expected from the reviews I read .", "triples": [{"uid": "19-0", "sentiment": "negative", "target_tags": "I\\O went\\O there\\O for\\O lunch\\B and\\O it\\O was\\O not\\O as\\O good\\O as\\O I\\O expected\\O from\\O the\\O reviews\\O I\\O read\\O .\\O", "opinion_tags": "I\\O went\\O there\\O for\\O lunch\\O and\\O it\\O was\\O not\\B as\\I good\\I as\\I I\\I expected\\I from\\O the\\O reviews\\O I\\O read\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "IN", "NN", "CC", "PRP", "VBD", "RB", "RB", "JJ", "IN", "PRP", "VBD", "IN", "DT", "NNS", "PRP", "VBD", "."], "head": [2, 0, 2, 5, 2, 11, 11, 11, 11, 11, 2, 14, 14, 11, 17, 17, 14, 19, 17, 2], "deprel": ["nsubj", "root", "advmod", "case", "obl", "cc", "nsubj", "cop", "advmod", "advmod", "conj", "mark", "nsubj", "advcl", "case", "det", "obl", "nsubj", "acl:relcl", "punct"]}, {"id": "20", "sentence": "The place is small and cramped but the food is fantastic .", "triples": [{"uid": "20-0", "sentiment": "negative", "target_tags": "The\\O place\\B is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\B and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O"}, {"uid": "20-1", "sentiment": "negative", "target_tags": "The\\O place\\B is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\B but\\O the\\O food\\O is\\O fantastic\\O .\\O"}, {"uid": "20-2", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\O but\\O the\\O food\\B is\\O fantastic\\O .\\O", "opinion_tags": "The\\O place\\O is\\O small\\O and\\O cramped\\O but\\O the\\O food\\O is\\O fantastic\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 11, 9, 11, 11, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "21", "sentence": "those rolls were big , but not good and sashimi was n't fresh .", "triples": [{"uid": "21-0", "sentiment": "negative", "target_tags": "those\\O rolls\\B were\\O big\\O ,\\O but\\O not\\O good\\O and\\O sashimi\\O was\\O n't\\O fresh\\O .\\O", "opinion_tags": "those\\O rolls\\O were\\O big\\B ,\\O but\\O not\\O good\\O and\\O sashimi\\O was\\O n't\\O fresh\\O .\\O"}, {"uid": "21-1", "sentiment": "negative", "target_tags": "those\\O rolls\\B were\\O big\\O ,\\O but\\O not\\O good\\O and\\O sashimi\\O was\\O n't\\O fresh\\O .\\O", "opinion_tags": "those\\O rolls\\O were\\O big\\O ,\\O but\\O not\\B good\\I and\\O sashimi\\O was\\O n't\\O fresh\\O .\\O"}, {"uid": "21-2", "sentiment": "negative", "target_tags": "those\\O rolls\\O were\\O big\\O ,\\O but\\O not\\O good\\O and\\O sashimi\\B was\\O n't\\O fresh\\O .\\O", "opinion_tags": "those\\O rolls\\O were\\O big\\O ,\\O but\\O not\\O good\\O and\\O sashimi\\O was\\B n't\\I fresh\\I .\\O"}], "postag": ["DT", "NNS", "VBD", "JJ", ",", "CC", "RB", "JJ", "CC", "NN", "VBD", "RB", "JJ", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 13, 13, 13, 13, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "advmod", "conj", "cc", "nsubj", "cop", "advmod", "conj", "punct"]}, {"id": "22", "sentence": "Admittedly some nights inside the restaurant were rather warm , but the open kitchen is part of the charm .", "triples": [{"uid": "22-0", "sentiment": "positive", "target_tags": "Admittedly\\O some\\O nights\\O inside\\O the\\O restaurant\\O were\\O rather\\O warm\\O ,\\O but\\O the\\O open\\B kitchen\\I is\\O part\\O of\\O the\\O charm\\O .\\O", "opinion_tags": "Admittedly\\O some\\O nights\\O inside\\O the\\O restaurant\\O were\\O rather\\O warm\\O ,\\O but\\O the\\O open\\O kitchen\\O is\\O part\\O of\\O the\\O charm\\B .\\O"}, {"uid": "22-1", "sentiment": "negative", "target_tags": "Admittedly\\O some\\O nights\\O inside\\O the\\O restaurant\\B were\\O rather\\O warm\\O ,\\O but\\O the\\O open\\O kitchen\\O is\\O part\\O of\\O the\\O charm\\O .\\O", "opinion_tags": "Admittedly\\O some\\O nights\\O inside\\O the\\O restaurant\\O were\\O rather\\O warm\\B ,\\O but\\O the\\O open\\O kitchen\\O is\\O part\\O of\\O the\\O charm\\O .\\O"}], "postag": ["RB", "DT", "NNS", "IN", "DT", "NN", "VBD", "RB", "JJ", ",", "CC", "DT", "JJ", "NN", "VBZ", "NN", "IN", "DT", "NN", "."], "head": [9, 3, 9, 6, 6, 3, 9, 9, 0, 16, 16, 14, 14, 16, 16, 9, 19, 19, 16, 9], "deprel": ["advmod", "det", "nsubj", "case", "det", "nmod", "cop", "advmod", "root", "punct", "cc", "det", "amod", "nsubj", "cop", "conj", "case", "det", "nmod", "punct"]}, {"id": "23", "sentence": "Great wine selection , Gigondas is worth the price , and the house champagne is a great value .", "triples": [{"uid": "23-0", "sentiment": "positive", "target_tags": "Great\\O wine\\B selection\\I ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O", "opinion_tags": "Great\\B wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O"}, {"uid": "23-1", "sentiment": "positive", "target_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\B is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O", "opinion_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\B the\\I price\\I ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\O value\\O .\\O"}, {"uid": "23-2", "sentiment": "positive", "target_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\B champagne\\I is\\O a\\O great\\O value\\O .\\O", "opinion_tags": "Great\\O wine\\O selection\\O ,\\O Gigondas\\O is\\O worth\\O the\\O price\\O ,\\O and\\O the\\O house\\O champagne\\O is\\O a\\O great\\B value\\I .\\O"}], "postag": ["JJ", "NN", "NN", ",", "NNP", "VBZ", "JJ", "DT", "NN", ",", "CC", "DT", "NN", "NN", "VBZ", "DT", "JJ", "NN", "."], "head": [3, 3, 0, 3, 7, 7, 3, 9, 7, 18, 18, 14, 14, 18, 18, 18, 18, 3, 3], "deprel": ["amod", "compound", "root", "punct", "nsubj", "cop", "parataxis", "det", "obj", "punct", "cc", "det", "compound", "nsubj", "cop", "det", "amod", "conj", "punct"]}, {"id": "24", "sentence": "Have recommended the place to friends , always gets good response .", "triples": [{"uid": "24-0", "sentiment": "positive", "target_tags": "Have\\O recommended\\O the\\O place\\B to\\O friends\\O ,\\O always\\O gets\\O good\\O response\\O .\\O", "opinion_tags": "Have\\O recommended\\B the\\O place\\O to\\O friends\\O ,\\O always\\O gets\\O good\\O response\\O .\\O"}], "postag": ["VBP", "VBN", "DT", "NN", "IN", "NNS", ",", "RB", "VBZ", "JJ", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 9, 9, 2, 11, 9, 2], "deprel": ["aux", "root", "det", "obj", "case", "obl", "punct", "advmod", "parataxis", "amod", "obj", "punct"]}, {"id": "25", "sentence": "Service is not exactly five star , but thats not really a big deal .", "triples": [{"uid": "25-0", "sentiment": "neutral", "target_tags": "Service\\B is\\O not\\O exactly\\O five\\O star\\O ,\\O but\\O thats\\O not\\O really\\O a\\O big\\O deal\\O .\\O", "opinion_tags": "Service\\O is\\O not\\B exactly\\I five\\I star\\I ,\\O but\\O thats\\O not\\O really\\O a\\O big\\O deal\\O .\\O"}], "postag": ["NN", "VBZ", "RB", "RB", "CD", "NN", ",", "CC", "VBZ", "RB", "RB", "DT", "JJ", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 14, 14, 14, 14, 14, 14, 14, 6, 6], "deprel": ["nsubj", "cop", "advmod", "advmod", "nummod", "root", "punct", "cc", "cop", "advmod", "advmod", "det", "amod", "conj", "punct"]}, {"id": "26", "sentence": "The tuna and wasabe potatoes are excellent .", "triples": [{"uid": "26-0", "sentiment": "positive", "target_tags": "The\\O tuna\\B and\\O wasabe\\O potatoes\\O are\\O excellent\\O .\\O", "opinion_tags": "The\\O tuna\\O and\\O wasabe\\O potatoes\\O are\\O excellent\\B .\\O"}, {"uid": "26-1", "sentiment": "positive", "target_tags": "The\\O tuna\\O and\\O wasabe\\B potatoes\\I are\\O excellent\\O .\\O", "opinion_tags": "The\\O tuna\\O and\\O wasabe\\O potatoes\\O are\\O excellent\\B .\\O"}], "postag": ["DT", "NN", "CC", "NN", "NNS", "VBP", "JJ", "."], "head": [5, 5, 4, 2, 7, 7, 0, 7], "deprel": ["det", "compound", "cc", "conj", "nsubj", "cop", "root", "punct"]}, {"id": "27", "sentence": "Service was prompt and courteous .", "triples": [{"uid": "27-0", "sentiment": "positive", "target_tags": "Service\\B was\\O prompt\\O and\\O courteous\\O .\\O", "opinion_tags": "Service\\O was\\O prompt\\B and\\O courteous\\O .\\O"}, {"uid": "27-1", "sentiment": "positive", "target_tags": "Service\\B was\\O prompt\\O and\\O courteous\\O .\\O", "opinion_tags": "Service\\O was\\O prompt\\O and\\O courteous\\B .\\O"}], "postag": ["NN", "VBD", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"]}, {"id": "28", "sentence": "The pizza is delicious - they use fresh mozzarella instead of the cheap , frozen , shredded cheese common to most pizzaria 's .", "triples": [{"uid": "28-0", "sentiment": "positive", "target_tags": "The\\O pizza\\B is\\O delicious\\O -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O", "opinion_tags": "The\\O pizza\\O is\\O delicious\\B -\\O they\\O use\\O fresh\\O mozzarella\\O instead\\O of\\O the\\O cheap\\O ,\\O frozen\\O ,\\O shredded\\O cheese\\O common\\O to\\O most\\O pizzaria\\O 's\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "PRP", "VBP", "JJ", "NN", "RB", "IN", "DT", "JJ", ",", "JJ", ",", "VBN", "NN", "JJ", "IN", "JJS", "NN", "POS", "."], "head": [2, 4, 4, 0, 4, 7, 4, 9, 7, 18, 10, 18, 18, 15, 18, 18, 18, 7, 18, 22, 22, 19, 22, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "parataxis", "amod", "obj", "case", "fixed", "det", "amod", "punct", "amod", "punct", "amod", "obl", "amod", "case", "amod", "obl", "case", "punct"]}, {"id": "29", "sentence": "Wine list selection is good and wine-by-the-glass was generously filled to the top .", "triples": [{"uid": "29-0", "sentiment": "positive", "target_tags": "Wine\\B list\\I selection\\I is\\O good\\O and\\O wine-by-the-glass\\O was\\O generously\\O filled\\O to\\O the\\O top\\O .\\O", "opinion_tags": "Wine\\O list\\O selection\\O is\\O good\\B and\\O wine-by-the-glass\\O was\\O generously\\O filled\\O to\\O the\\O top\\O .\\O"}, {"uid": "29-1", "sentiment": "positive", "target_tags": "Wine\\O list\\O selection\\O is\\O good\\O and\\O wine-by-the-glass\\B was\\O generously\\O filled\\O to\\O the\\O top\\O .\\O", "opinion_tags": "Wine\\O list\\O selection\\O is\\O good\\O and\\O wine-by-the-glass\\O was\\O generously\\B filled\\I to\\O the\\O top\\O .\\O"}], "postag": ["NN", "NN", "NN", "VBZ", "JJ", "CC", "NN", "VBD", "RB", "VBN", "IN", "DT", "NN", "."], "head": [2, 3, 5, 5, 0, 10, 10, 10, 10, 5, 13, 13, 10, 5], "deprel": ["compound", "compound", "nsubj", "cop", "root", "cc", "nsubj:pass", "aux:pass", "advmod", "conj", "case", "det", "obl", "punct"]}, {"id": "30", "sentence": "I have NEVER been disappointed in the Red Eye .", "triples": [{"uid": "30-0", "sentiment": "positive", "target_tags": "I\\O have\\O NEVER\\O been\\O disappointed\\O in\\O the\\O Red\\B Eye\\I .\\O", "opinion_tags": "I\\O have\\O NEVER\\B been\\I disappointed\\I in\\O the\\O Red\\O Eye\\O .\\O"}], "postag": ["PRP", "VBP", "RB", "VBN", "VBN", "IN", "DT", "NNP", "NNP", "."], "head": [5, 5, 5, 5, 0, 9, 9, 9, 5, 5], "deprel": ["nsubj:pass", "aux", "advmod", "aux:pass", "root", "case", "det", "amod", "obl", "punct"]}, {"id": "31", "sentence": "The first time I went , and was completely taken by the live jazz band and atmosphere , I ordered the Lobster Cobb Salad .", "triples": [{"uid": "31-0", "sentiment": "positive", "target_tags": "The\\O first\\O time\\O I\\O went\\O ,\\O and\\O was\\O completely\\O taken\\O by\\O the\\O live\\B jazz\\I band\\I and\\O atmosphere\\O ,\\O I\\O ordered\\O the\\O Lobster\\O Cobb\\O Salad\\O .\\O", "opinion_tags": "The\\O first\\O time\\O I\\O went\\O ,\\O and\\O was\\O completely\\O taken\\B by\\O the\\O live\\O jazz\\O band\\O and\\O atmosphere\\O ,\\O I\\O ordered\\O the\\O Lobster\\O Cobb\\O Salad\\O .\\O"}, {"uid": "31-1", "sentiment": "positive", "target_tags": "The\\O first\\O time\\O I\\O went\\O ,\\O and\\O was\\O completely\\O taken\\O by\\O the\\O live\\O jazz\\O band\\O and\\O atmosphere\\B ,\\O I\\O ordered\\O the\\O Lobster\\O Cobb\\O Salad\\O .\\O", "opinion_tags": "The\\O first\\O time\\O I\\O went\\O ,\\O and\\O was\\O completely\\O taken\\B by\\O the\\O live\\O jazz\\O band\\O and\\O atmosphere\\O ,\\O I\\O ordered\\O the\\O Lobster\\O Cobb\\O Salad\\O .\\O"}], "postag": ["DT", "JJ", "NN", "PRP", "VBD", ",", "CC", "VBD", "RB", "VBN", "IN", "DT", "JJ", "NN", "NN", "CC", "NN", ",", "PRP", "VBD", "DT", "NNP", "NNP", "NNP", "."], "head": [3, 3, 20, 5, 3, 10, 10, 10, 10, 5, 15, 15, 15, 15, 10, 17, 15, 20, 20, 0, 24, 24, 24, 20, 20], "deprel": ["det", "amod", "obl:tmod", "nsubj", "acl:relcl", "punct", "cc", "aux:pass", "advmod", "conj", "case", "det", "amod", "compound", "obl", "cc", "conj", "punct", "nsubj", "root", "det", "compound", "compound", "obj", "punct"]}, {"id": "32", "sentence": "My husband and I thougt it would be great to go to the Jekyll and Hyde Pub for our anniversary , and to our surprise it was fantastic .", "triples": [{"uid": "32-0", "sentiment": "positive", "target_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\O to\\O go\\O to\\O the\\O Jekyll\\B and\\I Hyde\\I Pub\\I for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\O .\\O", "opinion_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\B to\\O go\\O to\\O the\\O Jekyll\\O and\\O Hyde\\O Pub\\O for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\O .\\O"}, {"uid": "32-1", "sentiment": "positive", "target_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\O to\\O go\\O to\\O the\\O Jekyll\\B and\\I Hyde\\I Pub\\I for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\O .\\O", "opinion_tags": "My\\O husband\\O and\\O I\\O thougt\\O it\\O would\\O be\\O great\\O to\\O go\\O to\\O the\\O Jekyll\\O and\\O Hyde\\O Pub\\O for\\O our\\O anniversary\\O ,\\O and\\O to\\O our\\O surprise\\O it\\O was\\O fantastic\\B .\\O"}], "postag": ["PRP$", "NN", "CC", "PRP", "VBD", "PRP", "MD", "VB", "JJ", "TO", "VB", "IN", "DT", "NNP", "CC", "NNP", "NNP", "IN", "PRP$", "NN", ",", "CC", "IN", "PRP$", "NN", "PRP", "VBD", "JJ", "."], "head": [2, 5, 4, 2, 0, 9, 9, 9, 5, 11, 9, 14, 14, 11, 17, 17, 14, 20, 20, 11, 28, 28, 25, 25, 28, 28, 28, 5, 5], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "root", "expl", "aux", "cop", "ccomp", "mark", "csubj", "case", "det", "obl", "cc", "compound", "conj", "case", "nmod:poss", "obl", "punct", "cc", "case", "nmod:poss", "obl", "nsubj", "cop", "conj", "punct"]}, {"id": "33", "sentence": "The workers there also absolutely load the bagel with cream cheese ( gets a little messy ) .", "triples": [{"uid": "33-0", "sentiment": "negative", "target_tags": "The\\O workers\\O there\\O also\\O absolutely\\O load\\O the\\O bagel\\B with\\O cream\\O cheese\\O (\\O gets\\O a\\O little\\O messy\\O )\\O .\\O", "opinion_tags": "The\\O workers\\O there\\O also\\O absolutely\\O load\\O the\\O bagel\\O with\\O cream\\O cheese\\O (\\O gets\\O a\\O little\\O messy\\B )\\O .\\O"}], "postag": ["DT", "NNS", "EX", "RB", "RB", "VBP", "DT", "NN", "IN", "NN", "NN", "-LRB-", "VBZ", "DT", "JJ", "JJ", "-RRB-", "."], "head": [2, 6, 2, 6, 6, 0, 8, 6, 11, 11, 6, 13, 6, 15, 16, 13, 13, 6], "deprel": ["det", "nsubj", "advmod", "advmod", "advmod", "root", "det", "obj", "case", "compound", "obl", "punct", "parataxis", "det", "obl:npmod", "xcomp", "punct", "punct"]}, {"id": "34", "sentence": "I have to highly recommend the lobster roll - not to much mayo ; you can tell it was a fresh lobster .", "triples": [{"uid": "34-0", "sentiment": "positive", "target_tags": "I\\O have\\O to\\O highly\\O recommend\\O the\\O lobster\\B roll\\I -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\O lobster\\O .\\O", "opinion_tags": "I\\O have\\O to\\O highly\\O recommend\\B the\\O lobster\\O roll\\O -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\O lobster\\O .\\O"}, {"uid": "34-1", "sentiment": "positive", "target_tags": "I\\O have\\O to\\O highly\\O recommend\\O the\\O lobster\\O roll\\O -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\O lobster\\B .\\O", "opinion_tags": "I\\O have\\O to\\O highly\\O recommend\\O the\\O lobster\\O roll\\O -\\O not\\O to\\O much\\O mayo\\O ;\\O you\\O can\\O tell\\O it\\O was\\O a\\O fresh\\B lobster\\O .\\O"}], "postag": ["PRP", "VBP", "TO", "RB", "VB", "DT", "NN", "NN", ",", "RB", "IN", "JJ", "NN", ",", "PRP", "MD", "VB", "PRP", "VBD", "DT", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 5, 8, 13, 13, 13, 8, 2, 17, 17, 2, 22, 22, 22, 22, 17, 2], "deprel": ["nsubj", "root", "mark", "advmod", "xcomp", "det", "compound", "obj", "punct", "advmod", "case", "amod", "nmod", "punct", "nsubj", "aux", "parataxis", "nsubj", "cop", "det", "amod", "ccomp", "punct"]}, {"id": "35", "sentence": "The scallion pancakes and fried dumplings were nothing out of the ordinary .", "triples": [{"uid": "35-0", "sentiment": "neutral", "target_tags": "The\\O scallion\\B pancakes\\I and\\O fried\\O dumplings\\O were\\O nothing\\O out\\O of\\O the\\O ordinary\\O .\\O", "opinion_tags": "The\\O scallion\\O pancakes\\O and\\O fried\\O dumplings\\O were\\O nothing\\O out\\O of\\O the\\O ordinary\\B .\\O"}, {"uid": "35-1", "sentiment": "neutral", "target_tags": "The\\O scallion\\O pancakes\\O and\\O fried\\B dumplings\\I were\\O nothing\\O out\\O of\\O the\\O ordinary\\O .\\O", "opinion_tags": "The\\O scallion\\O pancakes\\O and\\O fried\\O dumplings\\O were\\O nothing\\O out\\O of\\O the\\O ordinary\\B .\\O"}], "postag": ["DT", "NN", "NNS", "CC", "JJ", "NNS", "VBD", "NN", "IN", "IN", "DT", "JJ", "."], "head": [3, 3, 8, 6, 6, 3, 8, 0, 12, 12, 12, 8, 8], "deprel": ["det", "compound", "nsubj", "cc", "amod", "conj", "cop", "root", "case", "case", "det", "nmod", "punct"]}, {"id": "36", "sentence": "Salads were fantastic .", "triples": [{"uid": "36-0", "sentiment": "positive", "target_tags": "Salads\\B were\\O fantastic\\O .\\O", "opinion_tags": "Salads\\O were\\O fantastic\\B .\\O"}], "postag": ["NNS", "VBD", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"]}, {"id": "37", "sentence": "The takeout is great too since they give high quality tupperware as well .", "triples": [{"uid": "37-0", "sentiment": "positive", "target_tags": "The\\O takeout\\B is\\O great\\O too\\O since\\O they\\O give\\O high\\O quality\\O tupperware\\O as\\O well\\O .\\O", "opinion_tags": "The\\O takeout\\O is\\O great\\B too\\O since\\O they\\O give\\O high\\O quality\\O tupperware\\O as\\O well\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "RB", "IN", "PRP", "VBP", "JJ", "NN", "NN", "RB", "RB", "."], "head": [2, 4, 4, 0, 4, 8, 8, 4, 11, 11, 8, 8, 12, 4], "deprel": ["det", "nsubj", "cop", "root", "advmod", "mark", "nsubj", "advcl", "amod", "compound", "obj", "advmod", "fixed", "punct"]}, {"id": "38", "sentence": "The staff was accomodating , the food was absolutely delicious and the place is lovely .", "triples": [{"uid": "38-0", "sentiment": "positive", "target_tags": "The\\O staff\\B was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\B ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O"}, {"uid": "38-1", "sentiment": "positive", "target_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\B was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\B and\\O the\\O place\\O is\\O lovely\\O .\\O"}, {"uid": "38-2", "sentiment": "positive", "target_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\B is\\O lovely\\O .\\O", "opinion_tags": "The\\O staff\\O was\\O accomodating\\O ,\\O the\\O food\\O was\\O absolutely\\O delicious\\O and\\O the\\O place\\O is\\O lovely\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "DT", "NN", "VBD", "RB", "JJ", "CC", "DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 10, 7, 10, 10, 10, 4, 15, 13, 15, 15, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "det", "nsubj", "cop", "advmod", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "39", "sentence": "The place is a BISTRO which means : simple dishes and wine served efficiently in a bustling atmosphere .", "triples": [{"uid": "39-0", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O a\\O BISTRO\\O which\\O means\\O :\\O simple\\O dishes\\B and\\O wine\\O served\\O efficiently\\O in\\O a\\O bustling\\O atmosphere\\O .\\O", "opinion_tags": "The\\O place\\O is\\O a\\O BISTRO\\O which\\O means\\O :\\O simple\\B dishes\\O and\\O wine\\O served\\O efficiently\\O in\\O a\\O bustling\\O atmosphere\\O .\\O"}, {"uid": "39-1", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O a\\O BISTRO\\O which\\O means\\O :\\O simple\\O dishes\\O and\\O wine\\B served\\O efficiently\\O in\\O a\\O bustling\\O atmosphere\\O .\\O", "opinion_tags": "The\\O place\\O is\\O a\\O BISTRO\\O which\\O means\\O :\\O simple\\O dishes\\O and\\O wine\\O served\\B efficiently\\I in\\O a\\O bustling\\O atmosphere\\O .\\O"}, {"uid": "39-2", "sentiment": "positive", "target_tags": "The\\O place\\O is\\O a\\O BISTRO\\O which\\O means\\O :\\O simple\\O dishes\\O and\\O wine\\O served\\O efficiently\\O in\\O a\\O bustling\\O atmosphere\\B .\\O", "opinion_tags": "The\\O place\\O is\\O a\\O BISTRO\\O which\\O means\\O :\\O simple\\O dishes\\O and\\O wine\\O served\\O efficiently\\O in\\O a\\O bustling\\B atmosphere\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "NN", "WDT", "VBZ", ":", "JJ", "NNS", "CC", "NN", "VBN", "RB", "IN", "DT", "VBG", "NN", "."], "head": [2, 5, 5, 5, 0, 7, 5, 10, 10, 7, 12, 10, 10, 13, 18, 18, 18, 13, 5], "deprel": ["det", "nsubj", "cop", "det", "root", "nsubj", "acl:relcl", "punct", "amod", "obj", "cc", "conj", "acl", "advmod", "case", "det", "amod", "obl", "punct"]}, {"id": "40", "sentence": "Right off the L in Brooklyn this is a nice cozy place with good pizza .", "triples": [{"uid": "40-0", "sentiment": "positive", "target_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\O cozy\\O place\\O with\\O good\\O pizza\\B .\\O", "opinion_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\O cozy\\O place\\O with\\O good\\B pizza\\O .\\O"}, {"uid": "40-1", "sentiment": "positive", "target_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\O cozy\\O place\\B with\\O good\\O pizza\\O .\\O", "opinion_tags": "Right\\O off\\O the\\O L\\O in\\O Brooklyn\\O this\\O is\\O a\\O nice\\B cozy\\I place\\O with\\O good\\O pizza\\O .\\O"}], "postag": ["RB", "IN", "DT", "NNP", "IN", "NNP", "DT", "VBZ", "DT", "JJ", "JJ", "NN", "IN", "JJ", "NN", "."], "head": [4, 4, 4, 12, 6, 4, 12, 12, 12, 12, 12, 0, 15, 15, 12, 12], "deprel": ["advmod", "case", "det", "obl", "case", "nmod", "nsubj", "cop", "det", "amod", "amod", "root", "case", "amod", "nmod", "punct"]}, {"id": "41", "sentence": "It 's a nice place to relax and have conversation .", "triples": [{"uid": "41-0", "sentiment": "positive", "target_tags": "It\\O 's\\O a\\O nice\\O place\\B to\\O relax\\O and\\O have\\O conversation\\O .\\O", "opinion_tags": "It\\O 's\\O a\\O nice\\B place\\O to\\O relax\\O and\\O have\\O conversation\\O .\\O"}], "postag": ["PRP", "VBZ", "DT", "JJ", "NN", "TO", "VB", "CC", "VB", "NN", "."], "head": [5, 5, 5, 5, 0, 7, 5, 9, 7, 9, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "cc", "conj", "obj", "punct"]}, {"id": "42", "sentence": "This is my first time writing a review for a restaurant because the food and service was excellent .", "triples": [{"uid": "42-0", "sentiment": "positive", "target_tags": "This\\O is\\O my\\O first\\O time\\O writing\\O a\\O review\\O for\\O a\\O restaurant\\O because\\O the\\O food\\B and\\O service\\O was\\O excellent\\O .\\O", "opinion_tags": "This\\O is\\O my\\O first\\O time\\O writing\\O a\\O review\\O for\\O a\\O restaurant\\O because\\O the\\O food\\O and\\O service\\O was\\O excellent\\B .\\O"}, {"uid": "42-1", "sentiment": "positive", "target_tags": "This\\O is\\O my\\O first\\O time\\O writing\\O a\\O review\\O for\\O a\\O restaurant\\O because\\O the\\O food\\O and\\O service\\B was\\O excellent\\O .\\O", "opinion_tags": "This\\O is\\O my\\O first\\O time\\O writing\\O a\\O review\\O for\\O a\\O restaurant\\O because\\O the\\O food\\O and\\O service\\O was\\O excellent\\B .\\O"}], "postag": ["DT", "VBZ", "PRP$", "JJ", "NN", "VBG", "DT", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "CC", "NN", "VBD", "JJ", "."], "head": [5, 5, 5, 5, 0, 5, 8, 6, 11, 11, 6, 18, 14, 18, 16, 14, 18, 5, 5], "deprel": ["nsubj", "cop", "nmod:poss", "amod", "root", "acl", "det", "obj", "case", "det", "obl", "mark", "det", "nsubj", "cc", "conj", "cop", "advcl", "punct"]}, {"id": "43", "sentence": "The filet mignon dish was superb !", "triples": [{"uid": "43-0", "sentiment": "positive", "target_tags": "The\\O filet\\B mignon\\I dish\\I was\\O superb\\O !\\O", "opinion_tags": "The\\O filet\\O mignon\\O dish\\O was\\O superb\\B !\\O"}], "postag": ["DT", "NN", "NN", "NN", "VBD", "JJ", "."], "head": [4, 3, 4, 6, 6, 0, 6], "deprel": ["det", "compound", "compound", "nsubj", "cop", "root", "punct"]}, {"id": "44", "sentence": "I like the ambience , it 's very dark and original .", "triples": [{"uid": "44-0", "sentiment": "positive", "target_tags": "I\\O like\\O the\\O ambience\\B ,\\O it\\O 's\\O very\\O dark\\O and\\O original\\O .\\O", "opinion_tags": "I\\O like\\B the\\O ambience\\O ,\\O it\\O 's\\O very\\O dark\\O and\\O original\\O .\\O"}, {"uid": "44-1", "sentiment": "positive", "target_tags": "I\\O like\\O the\\O ambience\\B ,\\O it\\O 's\\O very\\O dark\\O and\\O original\\O .\\O", "opinion_tags": "I\\O like\\O the\\O ambience\\O ,\\O it\\O 's\\O very\\O dark\\B and\\O original\\O .\\O"}, {"uid": "44-2", "sentiment": "positive", "target_tags": "I\\O like\\O the\\O ambience\\B ,\\O it\\O 's\\O very\\O dark\\O and\\O original\\O .\\O", "opinion_tags": "I\\O like\\O the\\O ambience\\O ,\\O it\\O 's\\O very\\O dark\\O and\\O original\\B .\\O"}], "postag": ["PRP", "VBP", "DT", "NN", ",", "PRP", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 0, 4, 2, 2, 9, 9, 9, 2, 11, 9, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "nsubj", "cop", "advmod", "parataxis", "cc", "conj", "punct"]}, {"id": "45", "sentence": "The sushi is amazing ! ! !", "triples": [{"uid": "45-0", "sentiment": "positive", "target_tags": "The\\O sushi\\B is\\O amazing\\O !\\O !\\O !\\O", "opinion_tags": "The\\O sushi\\O is\\O amazing\\B !\\O !\\O !\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ".", ".", "."], "head": [2, 4, 4, 0, 4, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "punct", "punct"]}, {"id": "46", "sentence": "Very affordable and excellent ambient !", "triples": [{"uid": "46-0", "sentiment": "positive", "target_tags": "Very\\O affordable\\O and\\O excellent\\O ambient\\B !\\O", "opinion_tags": "Very\\O affordable\\B and\\O excellent\\O ambient\\O !\\O"}, {"uid": "46-1", "sentiment": "positive", "target_tags": "Very\\O affordable\\O and\\O excellent\\O ambient\\B !\\O", "opinion_tags": "Very\\O affordable\\O and\\O excellent\\B ambient\\O !\\O"}], "postag": ["RB", "JJ", "CC", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 2], "deprel": ["advmod", "root", "cc", "amod", "conj", "punct"]}, {"id": "47", "sentence": "My Girlfriend and I stumbled onto this hopping place the other night and had a great time !", "triples": [{"uid": "47-0", "sentiment": "positive", "target_tags": "My\\O Girlfriend\\O and\\O I\\O stumbled\\O onto\\O this\\O hopping\\O place\\B the\\O other\\O night\\O and\\O had\\O a\\O great\\O time\\O !\\O", "opinion_tags": "My\\O Girlfriend\\O and\\O I\\O stumbled\\O onto\\O this\\O hopping\\B place\\O the\\O other\\O night\\O and\\O had\\O a\\O great\\O time\\O !\\O"}, {"uid": "47-1", "sentiment": "positive", "target_tags": "My\\O Girlfriend\\O and\\O I\\O stumbled\\O onto\\O this\\O hopping\\O place\\B the\\O other\\O night\\O and\\O had\\O a\\O great\\O time\\O !\\O", "opinion_tags": "My\\O Girlfriend\\O and\\O I\\O stumbled\\O onto\\O this\\O hopping\\O place\\O the\\O other\\O night\\O and\\O had\\O a\\O great\\B time\\I !\\O"}], "postag": ["PRP$", "NN", "CC", "PRP", "VBD", "IN", "DT", "NN", "NN", "DT", "JJ", "NN", "CC", "VBD", "DT", "JJ", "NN", "."], "head": [2, 5, 4, 2, 0, 9, 9, 9, 5, 12, 12, 5, 14, 5, 17, 17, 14, 5], "deprel": ["nmod:poss", "nsubj", "cc", "conj", "root", "case", "det", "amod", "obl", "det", "amod", "obl:tmod", "cc", "conj", "det", "amod", "obj", "punct"]}, {"id": "48", "sentence": "To be completely fair , the only redeeming factor was the food , which was above average , but could n't make up for all the other deficiencies of Teodora .", "triples": [{"uid": "48-0", "sentiment": "positive", "target_tags": "To\\O be\\O completely\\O fair\\O ,\\O the\\O only\\O redeeming\\O factor\\O was\\O the\\O food\\B ,\\O which\\O was\\O above\\O average\\O ,\\O but\\O could\\O n't\\O make\\O up\\O for\\O all\\O the\\O other\\O deficiencies\\O of\\O Teodora\\O .\\O", "opinion_tags": "To\\O be\\O completely\\O fair\\O ,\\O the\\O only\\O redeeming\\O factor\\O was\\O the\\O food\\O ,\\O which\\O was\\O above\\B average\\I ,\\O but\\O could\\O n't\\O make\\O up\\O for\\O all\\O the\\O other\\O deficiencies\\O of\\O Teodora\\O .\\O"}, {"uid": "48-1", "sentiment": "negative", "target_tags": "To\\O be\\O completely\\O fair\\O ,\\O the\\O only\\O redeeming\\O factor\\O was\\O the\\O food\\O ,\\O which\\O was\\O above\\O average\\O ,\\O but\\O could\\O n't\\O make\\O up\\O for\\O all\\O the\\O other\\O deficiencies\\O of\\O Teodora\\B .\\O", "opinion_tags": "To\\O be\\O completely\\O fair\\O ,\\O the\\O only\\O redeeming\\O factor\\O was\\O the\\O food\\O ,\\O which\\O was\\O above\\O average\\O ,\\O but\\O could\\O n't\\O make\\O up\\O for\\O all\\O the\\O other\\O deficiencies\\B of\\O Teodora\\O .\\O"}], "postag": ["TO", "VB", "RB", "JJ", ",", "DT", "JJ", "NN", "NN", "VBD", "DT", "NN", ",", "WDT", "VBD", "IN", "NN", ",", "CC", "MD", "RB", "VB", "RP", "IN", "PDT", "DT", "JJ", "NNS", "IN", "NNP", "."], "head": [4, 4, 4, 12, 12, 9, 9, 9, 12, 12, 12, 0, 12, 17, 17, 17, 12, 22, 22, 22, 22, 12, 22, 28, 28, 28, 28, 22, 30, 28, 12], "deprel": ["mark", "cop", "advmod", "advcl", "punct", "det", "amod", "compound", "nsubj", "cop", "det", "root", "punct", "nsubj", "cop", "case", "acl:relcl", "punct", "cc", "aux", "advmod", "conj", "compound:prt", "case", "det:predet", "det", "amod", "obl", "case", "nmod", "punct"]}, {"id": "49", "sentence": "One of us actually liked the expresso - that 's it .", "triples": [{"uid": "49-0", "sentiment": "positive", "target_tags": "One\\O of\\O us\\O actually\\O liked\\O the\\O expresso\\B -\\O that\\O 's\\O it\\O .\\O", "opinion_tags": "One\\O of\\O us\\O actually\\O liked\\B the\\O expresso\\O -\\O that\\O 's\\O it\\O .\\O"}], "postag": ["CD", "IN", "PRP", "RB", "VBD", "DT", "NN", ",", "DT", "VBZ", "PRP", "."], "head": [5, 3, 1, 5, 0, 7, 5, 5, 11, 11, 5, 5], "deprel": ["nsubj", "case", "nmod", "advmod", "root", "det", "obj", "punct", "nsubj", "cop", "parataxis", "punct"]}, {"id": "50", "sentence": "We did n't want a bottle of bubbly on a weekday so we each got little bottles of Korbett it was just enough .", "triples": [{"uid": "50-0", "sentiment": "positive", "target_tags": "We\\O did\\O n't\\O want\\O a\\O bottle\\O of\\O bubbly\\O on\\O a\\O weekday\\O so\\O we\\O each\\O got\\O little\\O bottles\\B of\\I Korbett\\I it\\O was\\O just\\O enough\\O .\\O", "opinion_tags": "We\\O did\\O n't\\O want\\O a\\O bottle\\O of\\O bubbly\\O on\\O a\\O weekday\\O so\\O we\\O each\\O got\\O little\\O bottles\\O of\\O Korbett\\O it\\O was\\O just\\O enough\\B .\\O"}], "postag": ["PRP", "VBD", "RB", "VB", "DT", "NN", "IN", "JJ", "IN", "DT", "NN", "RB", "PRP", "DT", "VBD", "JJ", "NNS", "IN", "NNP", "PRP", "VBD", "RB", "JJ", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 11, 11, 4, 15, 15, 13, 4, 17, 15, 19, 17, 23, 23, 23, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "obj", "case", "nmod", "case", "det", "obl", "advmod", "nsubj", "det", "conj", "amod", "obj", "case", "nmod", "nsubj", "cop", "advmod", "parataxis", "punct"]}, {"id": "51", "sentence": "Problem is nothing at <PERSON><PERSON><PERSON> is particularly memorable .", "triples": [{"uid": "51-0", "sentiment": "negative", "target_tags": "Problem\\O is\\O nothing\\O at\\O Prune\\B is\\O particularly\\O memorable\\O .\\O", "opinion_tags": "Problem\\O is\\O nothing\\O at\\O Prune\\O is\\O particularly\\O memorable\\B .\\O"}], "postag": ["NN", "VBZ", "NN", "IN", "NNP", "VBZ", "RB", "JJ", "."], "head": [3, 3, 0, 5, 3, 8, 8, 3, 3], "deprel": ["nsubj", "cop", "root", "case", "nmod", "cop", "advmod", "acl:relcl", "punct"]}, {"id": "52", "sentence": "Our food was great too !", "triples": [{"uid": "52-0", "sentiment": "positive", "target_tags": "Our\\O food\\B was\\O great\\O too\\O !\\O", "opinion_tags": "Our\\O food\\O was\\O great\\B too\\O !\\O"}], "postag": ["PRP$", "NN", "VBD", "JJ", "RB", "."], "head": [2, 4, 4, 0, 4, 4], "deprel": ["nmod:poss", "nsubj", "cop", "root", "advmod", "punct"]}, {"id": "53", "sentence": "And really large portions .", "triples": [{"uid": "53-0", "sentiment": "positive", "target_tags": "And\\O really\\O large\\O portions\\B .\\O", "opinion_tags": "And\\O really\\O large\\B portions\\O .\\O"}], "postag": ["CC", "RB", "JJ", "NNS", "."], "head": [4, 3, 4, 0, 4], "deprel": ["cc", "advmod", "amod", "root", "punct"]}, {"id": "54", "sentence": "The food however , is what one might expect .", "triples": [{"uid": "54-0", "sentiment": "negative", "target_tags": "The\\O food\\B however\\O ,\\O is\\O what\\O one\\O might\\O expect\\O .\\O", "opinion_tags": "The\\O food\\O however\\O ,\\O is\\O what\\O one\\O might\\O expect\\B .\\O"}], "postag": ["DT", "NN", "RB", ",", "VBZ", "WP", "PRP", "MD", "VB", "."], "head": [2, 6, 6, 6, 6, 0, 9, 9, 6, 6], "deprel": ["det", "nsubj", "advmod", "punct", "cop", "root", "nsubj", "aux", "acl:relcl", "punct"]}, {"id": "55", "sentence": "Prices too high for this cramped and unappealing resturant .", "triples": [{"uid": "55-0", "sentiment": "negative", "target_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\O and\\O unappealing\\O resturant\\B .\\O", "opinion_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\B and\\O unappealing\\O resturant\\O .\\O"}, {"uid": "55-1", "sentiment": "negative", "target_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\O and\\O unappealing\\O resturant\\B .\\O", "opinion_tags": "Prices\\O too\\O high\\O for\\O this\\O cramped\\O and\\O unappealing\\B resturant\\O .\\O"}], "postag": ["NNS", "RB", "JJ", "IN", "DT", "JJ", "CC", "JJ", "NN", "."], "head": [3, 3, 0, 9, 9, 9, 8, 6, 3, 3], "deprel": ["nsubj", "advmod", "root", "case", "det", "amod", "cc", "conj", "obl", "punct"]}, {"id": "56", "sentence": "<PERSON><PERSON><PERSON> is a must for anyone who loves <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> .", "triples": [{"uid": "56-0", "sentiment": "positive", "target_tags": "Thius\\O is\\O a\\O must\\O for\\O anyone\\O who\\O loves\\O Shabu-Shabu\\B .\\O", "opinion_tags": "Thius\\O is\\O a\\O must\\O for\\O anyone\\O who\\O loves\\B Shabu-Shabu\\O .\\O"}], "postag": ["NNP", "VBZ", "DT", "NN", "IN", "NN", "WP", "VBZ", "NNP", "."], "head": [4, 4, 4, 0, 6, 4, 8, 6, 8, 4], "deprel": ["nsubj", "cop", "det", "root", "case", "nmod", "nsubj", "acl:relcl", "obj", "punct"]}, {"id": "57", "sentence": "Despite a slightly limited menu , everything prepared is done to perfection , ultra fresh and a work of food art .", "triples": [{"uid": "57-0", "sentiment": "negative", "target_tags": "Despite\\O a\\O slightly\\O limited\\O menu\\B ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\O fresh\\O and\\O a\\O work\\O of\\O food\\O art\\O .\\O", "opinion_tags": "Despite\\O a\\O slightly\\O limited\\B menu\\O ,\\O everything\\O prepared\\O is\\O done\\O to\\O perfection\\O ,\\O ultra\\O fresh\\O and\\O a\\O work\\O of\\O food\\O art\\O .\\O"}], "postag": ["IN", "DT", "RB", "JJ", "NN", ",", "NN", "VBN", "VBZ", "VBN", "IN", "NN", ",", "NN", "JJ", "CC", "DT", "NN", "IN", "NN", "NN", "."], "head": [5, 5, 4, 5, 10, 5, 10, 7, 10, 0, 12, 10, 15, 15, 12, 18, 18, 12, 21, 21, 18, 10], "deprel": ["case", "det", "advmod", "amod", "obl", "punct", "nsubj:pass", "acl", "aux:pass", "root", "case", "obl", "punct", "compound", "amod", "cc", "det", "conj", "case", "compound", "nmod", "punct"]}, {"id": "58", "sentence": "The location and ambience is Ok but the food is what makes up for it .", "triples": [{"uid": "58-0", "sentiment": "neutral", "target_tags": "The\\O location\\B and\\O ambience\\O is\\O Ok\\O but\\O the\\O food\\O is\\O what\\O makes\\O up\\O for\\O it\\O .\\O", "opinion_tags": "The\\O location\\O and\\O ambience\\O is\\O Ok\\B but\\O the\\O food\\O is\\O what\\O makes\\O up\\O for\\O it\\O .\\O"}, {"uid": "58-1", "sentiment": "neutral", "target_tags": "The\\O location\\O and\\O ambience\\B is\\O Ok\\O but\\O the\\O food\\O is\\O what\\O makes\\O up\\O for\\O it\\O .\\O", "opinion_tags": "The\\O location\\O and\\O ambience\\O is\\O Ok\\B but\\O the\\O food\\O is\\O what\\O makes\\O up\\O for\\O it\\O .\\O"}], "postag": ["DT", "NN", "CC", "NN", "VBZ", "JJ", "CC", "DT", "NN", "VBZ", "WP", "VBZ", "RP", "IN", "PRP", "."], "head": [2, 6, 4, 2, 6, 0, 11, 9, 11, 11, 6, 11, 12, 15, 12, 6], "deprel": ["det", "nsubj", "cc", "conj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "acl:relcl", "compound:prt", "case", "obl", "punct"]}, {"id": "59", "sentence": "I found the food , service and value exceptional everytime I have been there .", "triples": [{"uid": "59-0", "sentiment": "positive", "target_tags": "I\\O found\\O the\\O food\\B ,\\O service\\O and\\O value\\O exceptional\\O everytime\\O I\\O have\\O been\\O there\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O ,\\O service\\O and\\O value\\O exceptional\\B everytime\\O I\\O have\\O been\\O there\\O .\\O"}, {"uid": "59-1", "sentiment": "positive", "target_tags": "I\\O found\\O the\\O food\\O ,\\O service\\B and\\O value\\O exceptional\\O everytime\\O I\\O have\\O been\\O there\\O .\\O", "opinion_tags": "I\\O found\\O the\\O food\\O ,\\O service\\O and\\O value\\O exceptional\\B everytime\\O I\\O have\\O been\\O there\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "NN", ",", "NN", "CC", "VBP", "JJ", "RB", "PRP", "VBP", "VBN", "RB", "."], "head": [2, 0, 4, 2, 6, 2, 8, 2, 8, 8, 14, 14, 14, 8, 2], "deprel": ["nsubj", "root", "det", "obj", "punct", "obj", "cc", "conj", "obj", "advmod", "nsubj", "aux", "cop", "ccomp", "punct"]}, {"id": "60", "sentence": "The service was excellent - friendly and attentive .", "triples": [{"uid": "60-0", "sentiment": "positive", "target_tags": "The\\O service\\B was\\O excellent\\O -\\O friendly\\O and\\O attentive\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\B -\\O friendly\\O and\\O attentive\\O .\\O"}, {"uid": "60-1", "sentiment": "positive", "target_tags": "The\\O service\\B was\\O excellent\\O -\\O friendly\\O and\\O attentive\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O -\\O friendly\\B and\\O attentive\\O .\\O"}, {"uid": "60-2", "sentiment": "positive", "target_tags": "The\\O service\\B was\\O excellent\\O -\\O friendly\\O and\\O attentive\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O -\\O friendly\\O and\\O attentive\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "JJ", "CC", "JJ", "."], "head": [2, 4, 4, 0, 6, 4, 8, 6, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "parataxis", "cc", "conj", "punct"]}, {"id": "61", "sentence": "Very good wine choices .", "triples": [{"uid": "61-0", "sentiment": "positive", "target_tags": "Very\\O good\\O wine\\B choices\\I .\\O", "opinion_tags": "Very\\O good\\B wine\\O choices\\O .\\O"}], "postag": ["RB", "JJ", "NN", "NNS", "."], "head": [2, 4, 4, 0, 4], "deprel": ["advmod", "amod", "compound", "root", "punct"]}, {"id": "62", "sentence": "Great staff .", "triples": [{"uid": "62-0", "sentiment": "positive", "target_tags": "Great\\O staff\\B .\\O", "opinion_tags": "Great\\B staff\\O .\\O"}], "postag": ["JJ", "NN", "."], "head": [2, 0, 2], "deprel": ["amod", "root", "punct"]}, {"id": "63", "sentence": "But the staff was so horrible to us .", "triples": [{"uid": "63-0", "sentiment": "negative", "target_tags": "But\\O the\\O staff\\B was\\O so\\O horrible\\O to\\O us\\O .\\O", "opinion_tags": "But\\O the\\O staff\\O was\\O so\\O horrible\\B to\\O us\\O .\\O"}], "postag": ["CC", "DT", "NN", "VBD", "RB", "JJ", "IN", "PRP", "."], "head": [6, 3, 6, 6, 6, 0, 8, 6, 6], "deprel": ["cc", "det", "nsubj", "cop", "advmod", "root", "case", "obl", "punct"]}, {"id": "64", "sentence": "This place is pricey , and yes , the food is worth it ; but the service makes you feel like you should be paying a quater of the price .", "triples": [{"uid": "64-0", "sentiment": "negative", "target_tags": "This\\O place\\B is\\O pricey\\O ,\\O and\\O yes\\O ,\\O the\\O food\\O is\\O worth\\O it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O", "opinion_tags": "This\\O place\\O is\\O pricey\\B ,\\O and\\O yes\\O ,\\O the\\O food\\O is\\O worth\\O it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O"}, {"uid": "64-1", "sentiment": "positive", "target_tags": "This\\O place\\O is\\O pricey\\O ,\\O and\\O yes\\O ,\\O the\\O food\\B is\\O worth\\O it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O", "opinion_tags": "This\\O place\\O is\\O pricey\\O ,\\O and\\O yes\\O ,\\O the\\O food\\O is\\O worth\\B it\\O ;\\O but\\O the\\O service\\O makes\\O you\\O feel\\O like\\O you\\O should\\O be\\O paying\\O a\\O quater\\O of\\O the\\O price\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "CC", "UH", ",", "DT", "NN", "VBZ", "JJ", "PRP", ",", "CC", "DT", "NN", "VBZ", "PRP", "VB", "IN", "PRP", "MD", "VB", "VBG", "DT", "NN", "IN", "DT", "NN", "."], "head": [2, 4, 4, 0, 12, 12, 12, 12, 10, 12, 12, 4, 12, 4, 18, 17, 18, 4, 18, 18, 25, 25, 25, 25, 20, 27, 25, 30, 30, 27, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "discourse", "punct", "det", "nsubj", "cop", "conj", "obj", "punct", "cc", "det", "nsubj", "conj", "obj", "xcomp", "mark", "nsubj", "aux", "aux", "advcl", "det", "obj", "case", "det", "nmod", "punct"]}, {"id": "65", "sentence": "Do n't dine at Tamarind for the vegetarian dishes , they are simply not up to par with the non-veg selections .", "triples": [{"uid": "65-0", "sentiment": "negative", "target_tags": "Do\\O n't\\O dine\\O at\\O Tamarind\\O for\\O the\\O vegetarian\\B dishes\\I ,\\O they\\O are\\O simply\\O not\\O up\\O to\\O par\\O with\\O the\\O non-veg\\O selections\\O .\\O", "opinion_tags": "Do\\O n't\\O dine\\O at\\O Tamarind\\O for\\O the\\O vegetarian\\O dishes\\O ,\\O they\\O are\\O simply\\O not\\B up\\I to\\I par\\I with\\O the\\O non-veg\\O selections\\O .\\O"}], "postag": ["VB", "RB", "VB", "IN", "NNP", "IN", "DT", "JJ", "NNS", ",", "PRP", "VBP", "RB", "RB", "IN", "IN", "NN", "IN", "DT", "JJ", "NNS", "."], "head": [3, 3, 0, 5, 3, 9, 9, 9, 3, 3, 17, 17, 17, 17, 17, 15, 3, 21, 21, 21, 17, 3], "deprel": ["aux", "advmod", "root", "case", "obl", "case", "det", "amod", "obl", "punct", "nsubj", "cop", "advmod", "advmod", "case", "fixed", "parataxis", "case", "det", "amod", "nmod", "punct"]}, {"id": "66", "sentence": "Decor is nice though service can be spotty .", "triples": [{"uid": "66-0", "sentiment": "positive", "target_tags": "Decor\\B is\\O nice\\O though\\O service\\O can\\O be\\O spotty\\O .\\O", "opinion_tags": "Decor\\O is\\O nice\\B though\\O service\\O can\\O be\\O spotty\\O .\\O"}, {"uid": "66-1", "sentiment": "negative", "target_tags": "Decor\\O is\\O nice\\O though\\O service\\B can\\O be\\O spotty\\O .\\O", "opinion_tags": "Decor\\O is\\O nice\\O though\\O service\\O can\\O be\\O spotty\\B .\\O"}], "postag": ["NN", "VBZ", "JJ", "IN", "NN", "MD", "VB", "JJ", "."], "head": [3, 3, 0, 8, 8, 8, 8, 3, 3], "deprel": ["nsubj", "cop", "root", "mark", "nsubj", "aux", "cop", "advcl", "punct"]}, {"id": "67", "sentence": "I am happy i did the food was awsome .", "triples": [{"uid": "67-0", "sentiment": "positive", "target_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\B was\\O awsome\\O .\\O", "opinion_tags": "I\\O am\\O happy\\O i\\O did\\O the\\O food\\O was\\O awsome\\B .\\O"}], "postag": ["PRP", "VBP", "JJ", "PRP", "VBD", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 0, 9, 9, 7, 9, 9, 3, 3], "deprel": ["nsubj", "cop", "root", "nsubj", "aux", "det", "nsubj", "cop", "ccomp", "punct"]}, {"id": "68", "sentence": "limited menu , no-so-fresh ingredients , thinly-sliced fish , fall-apart rice .", "triples": [{"uid": "68-0", "sentiment": "negative", "target_tags": "limited\\O menu\\B ,\\O no-so-fresh\\O ingredients\\O ,\\O thinly-sliced\\O fish\\O ,\\O fall-apart\\O rice\\O .\\O", "opinion_tags": "limited\\B menu\\O ,\\O no-so-fresh\\O ingredients\\O ,\\O thinly-sliced\\O fish\\O ,\\O fall-apart\\O rice\\O .\\O"}, {"uid": "68-1", "sentiment": "negative", "target_tags": "limited\\O menu\\O ,\\O no-so-fresh\\O ingredients\\B ,\\O thinly-sliced\\O fish\\O ,\\O fall-apart\\O rice\\O .\\O", "opinion_tags": "limited\\O menu\\O ,\\O no-so-fresh\\B ingredients\\O ,\\O thinly-sliced\\O fish\\O ,\\O fall-apart\\O rice\\O .\\O"}, {"uid": "68-2", "sentiment": "negative", "target_tags": "limited\\O menu\\O ,\\O no-so-fresh\\O ingredients\\O ,\\O thinly-sliced\\O fish\\B ,\\O fall-apart\\O rice\\O .\\O", "opinion_tags": "limited\\O menu\\O ,\\O no-so-fresh\\O ingredients\\O ,\\O thinly-sliced\\B fish\\O ,\\O fall-apart\\O rice\\O .\\O"}, {"uid": "68-3", "sentiment": "negative", "target_tags": "limited\\O menu\\O ,\\O no-so-fresh\\O ingredients\\O ,\\O thinly-sliced\\O fish\\O ,\\O fall-apart\\O rice\\B .\\O", "opinion_tags": "limited\\O menu\\O ,\\O no-so-fresh\\O ingredients\\O ,\\O thinly-sliced\\O fish\\O ,\\O fall-apart\\B rice\\O .\\O"}], "postag": ["JJ", "NN", ",", "JJ", "NNS", ",", "JJ", "NN", ",", "JJ", "NN", "."], "head": [2, 0, 5, 5, 2, 8, 8, 2, 2, 11, 2, 2], "deprel": ["amod", "root", "punct", "amod", "conj", "punct", "amod", "conj", "punct", "amod", "conj", "punct"]}, {"id": "69", "sentence": "With the theater 2 blocks away we had a delicious meal in a beautiful room .", "triples": [{"uid": "69-0", "sentiment": "positive", "target_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\O meal\\B in\\O a\\O beautiful\\O room\\O .\\O", "opinion_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\B meal\\O in\\O a\\O beautiful\\O room\\O .\\O"}, {"uid": "69-1", "sentiment": "positive", "target_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\O meal\\O in\\O a\\O beautiful\\O room\\B .\\O", "opinion_tags": "With\\O the\\O theater\\O 2\\O blocks\\O away\\O we\\O had\\O a\\O delicious\\O meal\\O in\\O a\\O beautiful\\B room\\O .\\O"}], "postag": ["IN", "DT", "NN", "CD", "NNS", "RB", "PRP", "VBD", "DT", "JJ", "NN", "IN", "DT", "JJ", "NN", "."], "head": [3, 3, 8, 5, 6, 8, 8, 0, 11, 11, 8, 15, 15, 15, 8, 8], "deprel": ["case", "det", "obl", "nummod", "obl:npmod", "advmod", "nsubj", "root", "det", "amod", "obj", "case", "det", "amod", "obl", "punct"]}, {"id": "70", "sentence": "The seats are uncomfortable if you are sitting against the wall on wooden benches .", "triples": [{"uid": "70-0", "sentiment": "negative", "target_tags": "The\\O seats\\B are\\O uncomfortable\\O if\\O you\\O are\\O sitting\\O against\\O the\\O wall\\O on\\O wooden\\O benches\\O .\\O", "opinion_tags": "The\\O seats\\O are\\O uncomfortable\\B if\\O you\\O are\\O sitting\\O against\\O the\\O wall\\O on\\O wooden\\O benches\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", "IN", "PRP", "VBP", "VBG", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 14, 14, 8, 4], "deprel": ["det", "nsubj", "cop", "root", "mark", "nsubj", "aux", "advcl", "case", "det", "obl", "case", "amod", "obl", "punct"]}, {"id": "71", "sentence": "Waitstaff are very friendly .", "triples": [{"uid": "71-0", "sentiment": "positive", "target_tags": "Waitstaff\\B are\\O very\\O friendly\\O .\\O", "opinion_tags": "Waitstaff\\O are\\O very\\O friendly\\B .\\O"}], "postag": ["NNS", "VBP", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct"]}, {"id": "72", "sentence": "The lobster sandwich is $ 24 and although it was good it was not nearly enough to warrant that price .", "triples": [{"uid": "72-0", "sentiment": "negative", "target_tags": "The\\O lobster\\B sandwich\\I is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\O it\\O was\\O not\\O nearly\\O enough\\O to\\O warrant\\O that\\O price\\O .\\O", "opinion_tags": "The\\O lobster\\O sandwich\\O is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\B it\\O was\\O not\\O nearly\\O enough\\O to\\O warrant\\O that\\O price\\O .\\O"}, {"uid": "72-1", "sentiment": "negative", "target_tags": "The\\O lobster\\B sandwich\\I is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\O it\\O was\\O not\\O nearly\\O enough\\O to\\O warrant\\O that\\O price\\O .\\O", "opinion_tags": "The\\O lobster\\O sandwich\\O is\\O $\\O 24\\O and\\O although\\O it\\O was\\O good\\O it\\O was\\O not\\B nearly\\I enough\\I to\\I warrant\\I that\\I price\\I .\\O"}], "postag": ["DT", "NN", "NN", "VBZ", "$", "CD", "CC", "IN", "PRP", "VBD", "JJ", "PRP", "VBD", "RB", "RB", "JJ", "TO", "VB", "DT", "NN", "."], "head": [3, 3, 5, 5, 0, 5, 16, 11, 11, 11, 16, 16, 16, 16, 16, 5, 18, 16, 20, 18, 5], "deprel": ["det", "compound", "nsubj", "cop", "root", "nummod", "cc", "mark", "expl", "cop", "advcl", "nsubj", "cop", "advmod", "advmod", "conj", "mark", "csubj", "det", "obj", "punct"]}, {"id": "73", "sentence": "The food was delicious ( I had a halibut special , my husband had steak ) , and the service was top-notch .", "triples": [{"uid": "73-0", "sentiment": "positive", "target_tags": "The\\O food\\B was\\O delicious\\O (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\O was\\O top-notch\\O .\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\B (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\O was\\O top-notch\\O .\\O"}, {"uid": "73-1", "sentiment": "positive", "target_tags": "The\\O food\\O was\\O delicious\\O (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\B was\\O top-notch\\O .\\O", "opinion_tags": "The\\O food\\O was\\O delicious\\O (\\O I\\O had\\O a\\O halibut\\O special\\O ,\\O my\\O husband\\O had\\O steak\\O )\\O ,\\O and\\O the\\O service\\O was\\O top-notch\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "-LRB-", "PRP", "VBD", "DT", "NN", "JJ", ",", "PRP$", "NN", "VBD", "NN", "-RRB-", ",", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 7, 7, 4, 9, 7, 7, 14, 13, 14, 7, 14, 7, 22, 22, 20, 22, 22, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "nsubj", "parataxis", "det", "obj", "obj", "punct", "nmod:poss", "nsubj", "parataxis", "obj", "punct", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "74", "sentence": "I almost hesititate to write a review because the atmosphere was so great and I would hate for it too become to crowded .", "triples": [{"uid": "74-0", "sentiment": "positive", "target_tags": "I\\O almost\\O hesititate\\O to\\O write\\O a\\O review\\O because\\O the\\O atmosphere\\B was\\O so\\O great\\O and\\O I\\O would\\O hate\\O for\\O it\\O too\\O become\\O to\\O crowded\\O .\\O", "opinion_tags": "I\\O almost\\O hesititate\\O to\\O write\\O a\\O review\\O because\\O the\\O atmosphere\\O was\\O so\\O great\\B and\\O I\\O would\\O hate\\O for\\O it\\O too\\O become\\O to\\O crowded\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "TO", "VB", "DT", "NN", "IN", "DT", "NN", "VBD", "RB", "JJ", "CC", "PRP", "MD", "VB", "IN", "PRP", "RB", "VB", "IN", "JJ", "."], "head": [3, 3, 0, 5, 3, 7, 5, 13, 10, 13, 13, 13, 5, 17, 17, 17, 13, 19, 17, 21, 17, 23, 21, 3], "deprel": ["nsubj", "advmod", "root", "mark", "xcomp", "det", "obj", "mark", "det", "nsubj", "cop", "advmod", "advcl", "cc", "nsubj", "aux", "conj", "case", "obl", "advmod", "xcomp", "case", "obl", "punct"]}, {"id": "75", "sentence": "Service is average .", "triples": [{"uid": "75-0", "sentiment": "neutral", "target_tags": "Service\\B is\\O average\\O .\\O", "opinion_tags": "Service\\O is\\O average\\B .\\O"}], "postag": ["NN", "VBZ", "JJ", "."], "head": [3, 3, 0, 3], "deprel": ["nsubj", "cop", "root", "punct"]}, {"id": "76", "sentence": "The ambience was so fun , and the prices were great , on top of the fact that the food was really tasty .", "triples": [{"uid": "76-0", "sentiment": "positive", "target_tags": "The\\O ambience\\B was\\O so\\O fun\\O ,\\O and\\O the\\O prices\\O were\\O great\\O ,\\O on\\O top\\O of\\O the\\O fact\\O that\\O the\\O food\\O was\\O really\\O tasty\\O .\\O", "opinion_tags": "The\\O ambience\\O was\\O so\\O fun\\B ,\\O and\\O the\\O prices\\O were\\O great\\O ,\\O on\\O top\\O of\\O the\\O fact\\O that\\O the\\O food\\O was\\O really\\O tasty\\O .\\O"}, {"uid": "76-1", "sentiment": "positive", "target_tags": "The\\O ambience\\O was\\O so\\O fun\\O ,\\O and\\O the\\O prices\\O were\\O great\\O ,\\O on\\O top\\O of\\O the\\O fact\\O that\\O the\\O food\\B was\\O really\\O tasty\\O .\\O", "opinion_tags": "The\\O ambience\\O was\\O so\\O fun\\O ,\\O and\\O the\\O prices\\O were\\O great\\O ,\\O on\\O top\\O of\\O the\\O fact\\O that\\O the\\O food\\O was\\O really\\O tasty\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", ",", "CC", "DT", "NNS", "VBD", "JJ", ",", "IN", "NN", "IN", "DT", "NN", "IN", "DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 11, 11, 9, 11, 11, 5, 11, 14, 11, 17, 17, 14, 23, 20, 23, 23, 23, 17, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct", "cc", "det", "nsubj", "cop", "conj", "punct", "case", "obl", "case", "det", "nmod", "mark", "det", "nsubj", "cop", "advmod", "acl", "punct"]}, {"id": "77", "sentence": "I would highly recommend this place !", "triples": [{"uid": "77-0", "sentiment": "positive", "target_tags": "I\\O would\\O highly\\O recommend\\O this\\O place\\B !\\O", "opinion_tags": "I\\O would\\O highly\\O recommend\\B this\\O place\\O !\\O"}], "postag": ["PRP", "MD", "RB", "VB", "DT", "NN", "."], "head": [4, 4, 4, 0, 6, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "det", "obj", "punct"]}, {"id": "78", "sentence": "Nice Family owned traditional restaurant .", "triples": [{"uid": "78-0", "sentiment": "positive", "target_tags": "Nice\\O Family\\O owned\\O traditional\\O restaurant\\B .\\O", "opinion_tags": "Nice\\O Family\\O owned\\O traditional\\B restaurant\\O .\\O"}], "postag": ["NNP", "NNP", "VBN", "JJ", "NN", "."], "head": [2, 3, 5, 5, 0, 5], "deprel": ["compound", "compound", "amod", "amod", "root", "punct"]}, {"id": "79", "sentence": "Fresh ingredients and everything is made to order .", "triples": [{"uid": "79-0", "sentiment": "positive", "target_tags": "Fresh\\O ingredients\\B and\\O everything\\O is\\O made\\O to\\O order\\O .\\O", "opinion_tags": "Fresh\\B ingredients\\O and\\O everything\\O is\\O made\\O to\\O order\\O .\\O"}], "postag": ["JJ", "NNS", "CC", "NN", "VBZ", "VBN", "TO", "VB", "."], "head": [2, 6, 4, 2, 6, 0, 8, 6, 6], "deprel": ["amod", "nsubj:pass", "cc", "conj", "aux:pass", "root", "mark", "xcomp", "punct"]}, {"id": "80", "sentence": "My friends settled for rice dishes , but we came back the following day to try the dim sum , which was good ... not outstanding , but good .", "triples": [{"uid": "80-0", "sentiment": "neutral", "target_tags": "My\\O friends\\O settled\\O for\\O rice\\O dishes\\O ,\\O but\\O we\\O came\\O back\\O the\\O following\\O day\\O to\\O try\\O the\\O dim\\B sum\\I ,\\O which\\O was\\O good\\O ...\\O not\\O outstanding\\O ,\\O but\\O good\\O .\\O", "opinion_tags": "My\\O friends\\O settled\\O for\\O rice\\O dishes\\O ,\\O but\\O we\\O came\\O back\\O the\\O following\\O day\\O to\\O try\\O the\\O dim\\O sum\\O ,\\O which\\O was\\O good\\B ...\\O not\\O outstanding\\O ,\\O but\\O good\\O .\\O"}, {"uid": "80-1", "sentiment": "neutral", "target_tags": "My\\O friends\\O settled\\O for\\O rice\\O dishes\\O ,\\O but\\O we\\O came\\O back\\O the\\O following\\O day\\O to\\O try\\O the\\O dim\\B sum\\I ,\\O which\\O was\\O good\\O ...\\O not\\O outstanding\\O ,\\O but\\O good\\O .\\O", "opinion_tags": "My\\O friends\\O settled\\O for\\O rice\\O dishes\\O ,\\O but\\O we\\O came\\O back\\O the\\O following\\O day\\O to\\O try\\O the\\O dim\\O sum\\O ,\\O which\\O was\\O good\\O ...\\O not\\B outstanding\\I ,\\O but\\O good\\O .\\O"}], "postag": ["PRP$", "NNS", "VBD", "IN", "NN", "NNS", ",", "CC", "PRP", "VBD", "RB", "DT", "VBG", "NN", "TO", "VB", "DT", "JJ", "NN", ",", "WDT", "VBD", "JJ", ",", "RB", "JJ", ",", "CC", "JJ", "."], "head": [2, 3, 0, 6, 6, 3, 10, 10, 10, 3, 10, 14, 14, 10, 16, 10, 19, 19, 16, 19, 23, 23, 19, 26, 26, 23, 29, 29, 26, 3], "deprel": ["nmod:poss", "nsubj", "root", "case", "compound", "obl", "punct", "cc", "nsubj", "conj", "advmod", "det", "amod", "obl:tmod", "mark", "advcl", "det", "amod", "obj", "punct", "nsubj", "cop", "acl:relcl", "punct", "advmod", "conj", "punct", "cc", "conj", "punct"]}, {"id": "81", "sentence": "I would recommend Roxy 's for that , but not for their food .", "triples": [{"uid": "81-0", "sentiment": "negative", "target_tags": "I\\O would\\O recommend\\O Roxy\\O 's\\O for\\O that\\O ,\\O but\\O not\\O for\\O their\\O food\\B .\\O", "opinion_tags": "I\\O would\\O recommend\\B Roxy\\O 's\\O for\\O that\\O ,\\O but\\O not\\O for\\O their\\O food\\O .\\O"}], "postag": ["PRP", "MD", "VB", "NNP", "POS", "IN", "DT", ",", "CC", "RB", "IN", "PRP$", "NN", "."], "head": [3, 3, 0, 3, 4, 7, 3, 13, 13, 13, 13, 13, 3, 3], "deprel": ["nsubj", "aux", "root", "obj", "case", "case", "obl", "punct", "cc", "advmod", "case", "nmod:poss", "conj", "punct"]}, {"id": "82", "sentence": "I had a huge pastrami sandwich on a roll .", "triples": [{"uid": "82-0", "sentiment": "neutral", "target_tags": "I\\O had\\O a\\O huge\\O pastrami\\B sandwich\\I on\\I a\\I roll\\I .\\O", "opinion_tags": "I\\O had\\O a\\O huge\\B pastrami\\O sandwich\\O on\\O a\\O roll\\O .\\O"}], "postag": ["PRP", "VBD", "DT", "JJ", "NN", "NN", "IN", "DT", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 9, 9, 2, 2], "deprel": ["nsubj", "root", "det", "amod", "compound", "obj", "case", "det", "obl", "punct"]}, {"id": "83", "sentence": "We went here for lunch a couple of weeks ago on a Saturday , and I was thoroughly impressed with the food .", "triples": [{"uid": "83-0", "sentiment": "positive", "target_tags": "We\\O went\\O here\\O for\\O lunch\\O a\\O couple\\O of\\O weeks\\O ago\\O on\\O a\\O Saturday\\O ,\\O and\\O I\\O was\\O thoroughly\\O impressed\\O with\\O the\\O food\\B .\\O", "opinion_tags": "We\\O went\\O here\\O for\\O lunch\\O a\\O couple\\O of\\O weeks\\O ago\\O on\\O a\\O Saturday\\O ,\\O and\\O I\\O was\\O thoroughly\\O impressed\\B with\\O the\\O food\\O .\\O"}], "postag": ["PRP", "VBD", "RB", "IN", "NN", "DT", "NN", "IN", "NNS", "RB", "IN", "DT", "NNP", ",", "CC", "PRP", "VBD", "RB", "JJ", "IN", "DT", "NN", "."], "head": [2, 0, 2, 5, 2, 7, 10, 9, 7, 2, 13, 13, 2, 19, 19, 19, 19, 19, 2, 22, 22, 19, 2], "deprel": ["nsubj", "root", "advmod", "case", "obl", "det", "obl:npmod", "case", "nmod", "advmod", "case", "det", "obl", "punct", "cc", "nsubj", "cop", "advmod", "conj", "case", "det", "obl", "punct"]}, {"id": "84", "sentence": "We had the scallops as an appetizer and they were delicious and the sauce was wonderful .", "triples": [{"uid": "84-0", "sentiment": "positive", "target_tags": "We\\O had\\O the\\O scallops\\B as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\O and\\O the\\O sauce\\O was\\O wonderful\\O .\\O", "opinion_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\B and\\O the\\O sauce\\O was\\O wonderful\\O .\\O"}, {"uid": "84-1", "sentiment": "positive", "target_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\O and\\O the\\O sauce\\B was\\O wonderful\\O .\\O", "opinion_tags": "We\\O had\\O the\\O scallops\\O as\\O an\\O appetizer\\O and\\O they\\O were\\O delicious\\O and\\O the\\O sauce\\O was\\O wonderful\\B .\\O"}], "postag": ["PRP", "VBD", "DT", "NNS", "IN", "DT", "NN", "CC", "PRP", "VBD", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 0, 4, 2, 7, 7, 2, 11, 11, 11, 2, 16, 14, 16, 16, 2, 2], "deprel": ["nsubj", "root", "det", "obj", "case", "det", "obl", "cc", "nsubj", "cop", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "85", "sentence": "The dishes offered were unique , very tasty and fresh from the lamb sausages , sardines with biscuits , large whole shrimp to the amazing pistachio ice cream ( the best and freshest I 've ever had ) .", "triples": [{"uid": "85-0", "sentiment": "positive", "target_tags": "The\\O dishes\\B offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\B ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "85-1", "sentiment": "positive", "target_tags": "The\\O dishes\\B offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\B and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "85-2", "sentiment": "positive", "target_tags": "The\\O dishes\\B offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\B from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}, {"uid": "85-3", "sentiment": "positive", "target_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\O pistachio\\B ice\\I cream\\I (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O", "opinion_tags": "The\\O dishes\\O offered\\O were\\O unique\\O ,\\O very\\O tasty\\O and\\O fresh\\O from\\O the\\O lamb\\O sausages\\O ,\\O sardines\\O with\\O biscuits\\O ,\\O large\\O whole\\O shrimp\\O to\\O the\\O amazing\\B pistachio\\O ice\\O cream\\O (\\O the\\O best\\O and\\O freshest\\O I\\O 've\\O ever\\O had\\O )\\O .\\O"}], "postag": ["DT", "NNS", "VBD", "VBD", "JJ", ",", "RB", "JJ", "CC", "JJ", "IN", "DT", "NN", "NNS", ",", "NNS", "IN", "NNS", ",", "JJ", "JJ", "NNS", "IN", "DT", "JJ", "NN", "NN", "NN", "-LRB-", "DT", "JJS", "CC", "JJS", "PRP", "VBP", "RB", "VBN", "-RRB-", "."], "head": [2, 5, 5, 5, 0, 8, 8, 5, 10, 5, 14, 14, 14, 10, 16, 14, 18, 14, 22, 22, 22, 14, 28, 28, 28, 28, 28, 14, 31, 31, 14, 33, 31, 37, 37, 37, 31, 31, 5], "deprel": ["det", "nsubj", "csubj", "cop", "root", "punct", "advmod", "conj", "cc", "conj", "case", "det", "compound", "obl", "punct", "conj", "case", "nmod", "punct", "amod", "amod", "conj", "case", "det", "amod", "compound", "compound", "nmod", "punct", "det", "parataxis", "cc", "conj", "nsubj", "aux", "advmod", "acl:relcl", "punct", "punct"]}, {"id": "86", "sentence": "I 'm glad I was introduced to this place and this is a rare gem in NY .", "triples": [{"uid": "86-0", "sentiment": "positive", "target_tags": "I\\O 'm\\O glad\\O I\\O was\\O introduced\\O to\\O this\\O place\\B and\\O this\\O is\\O a\\O rare\\O gem\\O in\\O NY\\O .\\O", "opinion_tags": "I\\O 'm\\O glad\\B I\\O was\\O introduced\\O to\\O this\\O place\\O and\\O this\\O is\\O a\\O rare\\O gem\\O in\\O NY\\O .\\O"}], "postag": ["PRP", "VBP", "JJ", "PRP", "VBD", "VBN", "IN", "DT", "NN", "CC", "DT", "VBZ", "DT", "JJ", "NN", "IN", "NNP", "."], "head": [3, 3, 0, 6, 6, 3, 9, 9, 6, 15, 15, 15, 15, 15, 3, 17, 15, 3], "deprel": ["nsubj", "cop", "root", "nsubj:pass", "aux:pass", "ccomp", "case", "det", "obl", "cc", "nsubj", "cop", "det", "amod", "conj", "case", "nmod", "punct"]}, {"id": "87", "sentence": "The service was excellent and the food was delicious .", "triples": [{"uid": "87-0", "sentiment": "positive", "target_tags": "The\\O service\\B was\\O excellent\\O and\\O the\\O food\\O was\\O delicious\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\B and\\O the\\O food\\O was\\O delicious\\O .\\O"}, {"uid": "87-1", "sentiment": "positive", "target_tags": "The\\O service\\O was\\O excellent\\O and\\O the\\O food\\B was\\O delicious\\O .\\O", "opinion_tags": "The\\O service\\O was\\O excellent\\O and\\O the\\O food\\O was\\O delicious\\B .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 4, 4, 0, 9, 7, 9, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "88", "sentence": "The food arrived 20 minutes after I called , cold and soggy .", "triples": [{"uid": "88-0", "sentiment": "negative", "target_tags": "The\\O food\\B arrived\\O 20\\O minutes\\O after\\O I\\O called\\O ,\\O cold\\O and\\O soggy\\O .\\O", "opinion_tags": "The\\O food\\O arrived\\O 20\\O minutes\\O after\\O I\\O called\\O ,\\O cold\\B and\\O soggy\\O .\\O"}, {"uid": "88-1", "sentiment": "negative", "target_tags": "The\\O food\\B arrived\\O 20\\O minutes\\O after\\O I\\O called\\O ,\\O cold\\O and\\O soggy\\O .\\O", "opinion_tags": "The\\O food\\O arrived\\O 20\\O minutes\\O after\\O I\\O called\\O ,\\O cold\\O and\\O soggy\\B .\\O"}], "postag": ["DT", "NN", "VBD", "CD", "NNS", "IN", "PRP", "VBD", ",", "JJ", "CC", "JJ", "."], "head": [2, 3, 0, 5, 3, 8, 8, 3, 10, 8, 12, 10, 3], "deprel": ["det", "nsubj", "root", "nummod", "obj", "mark", "nsubj", "advcl", "punct", "xcomp", "cc", "conj", "punct"]}, {"id": "89", "sentence": "The food looked very appetizing and delicious since it came on a variety of fancy plates .", "triples": [{"uid": "89-0", "sentiment": "positive", "target_tags": "The\\O food\\B looked\\O very\\O appetizing\\O and\\O delicious\\O since\\O it\\O came\\O on\\O a\\O variety\\O of\\O fancy\\O plates\\O .\\O", "opinion_tags": "The\\O food\\O looked\\O very\\O appetizing\\B and\\O delicious\\O since\\O it\\O came\\O on\\O a\\O variety\\O of\\O fancy\\O plates\\O .\\O"}, {"uid": "89-1", "sentiment": "positive", "target_tags": "The\\O food\\B looked\\O very\\O appetizing\\O and\\O delicious\\O since\\O it\\O came\\O on\\O a\\O variety\\O of\\O fancy\\O plates\\O .\\O", "opinion_tags": "The\\O food\\O looked\\O very\\O appetizing\\O and\\O delicious\\B since\\O it\\O came\\O on\\O a\\O variety\\O of\\O fancy\\O plates\\O .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "CC", "JJ", "IN", "PRP", "VBD", "IN", "DT", "NN", "IN", "JJ", "NNS", "."], "head": [2, 3, 0, 5, 3, 7, 5, 10, 10, 3, 13, 13, 10, 16, 16, 13, 3], "deprel": ["det", "nsubj", "root", "advmod", "xcomp", "cc", "conj", "mark", "nsubj", "advcl", "case", "det", "obl", "case", "amod", "nmod", "punct"]}, {"id": "90", "sentence": "By far , the best pizza in Manhattan .", "triples": [{"uid": "90-0", "sentiment": "positive", "target_tags": "By\\O far\\O ,\\O the\\O best\\O pizza\\B in\\O Manhattan\\O .\\O", "opinion_tags": "By\\O far\\O ,\\O the\\O best\\B pizza\\O in\\O Manhattan\\O .\\O"}], "postag": ["IN", "RB", ",", "DT", "JJS", "NN", "IN", "NNP", "."], "head": [2, 6, 6, 6, 6, 0, 8, 6, 6], "deprel": ["case", "nmod", "punct", "det", "amod", "root", "case", "nmod", "punct"]}, {"id": "91", "sentence": "The food was mediocre at best but it was the horrible service that made me vow never to go back .", "triples": [{"uid": "91-0", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O mediocre\\O at\\O best\\O but\\O it\\O was\\O the\\O horrible\\O service\\O that\\O made\\O me\\O vow\\O never\\O to\\O go\\O back\\O .\\O", "opinion_tags": "The\\O food\\O was\\O mediocre\\B at\\O best\\O but\\O it\\O was\\O the\\O horrible\\O service\\O that\\O made\\O me\\O vow\\O never\\O to\\O go\\O back\\O .\\O"}, {"uid": "91-1", "sentiment": "negative", "target_tags": "The\\O food\\O was\\O mediocre\\O at\\O best\\O but\\O it\\O was\\O the\\O horrible\\O service\\B that\\O made\\O me\\O vow\\O never\\O to\\O go\\O back\\O .\\O", "opinion_tags": "The\\O food\\O was\\O mediocre\\O at\\O best\\O but\\O it\\O was\\O the\\O horrible\\B service\\O that\\O made\\O me\\O vow\\O never\\O to\\O go\\O back\\O .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "RB", "JJS", "CC", "PRP", "VBD", "DT", "JJ", "NN", "WDT", "VBD", "PRP", "VB", "RB", "TO", "VB", "RB", "."], "head": [2, 4, 4, 0, 6, 4, 12, 12, 12, 12, 12, 4, 14, 12, 14, 14, 16, 19, 16, 19, 4], "deprel": ["det", "nsubj", "cop", "root", "case", "obl", "cc", "nsubj", "cop", "det", "amod", "conj", "nsubj", "acl:relcl", "obj", "xcomp", "advmod", "mark", "advcl", "advmod", "punct"]}, {"id": "92", "sentence": "While their kitchen food is delicious , their Sushi is out of this world .", "triples": [{"uid": "92-0", "sentiment": "positive", "target_tags": "While\\O their\\O kitchen\\B food\\I is\\O delicious\\O ,\\O their\\O Sushi\\O is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "While\\O their\\O kitchen\\O food\\O is\\O delicious\\B ,\\O their\\O Sushi\\O is\\O out\\O of\\O this\\O world\\O .\\O"}, {"uid": "92-1", "sentiment": "positive", "target_tags": "While\\O their\\O kitchen\\O food\\O is\\O delicious\\O ,\\O their\\O Sushi\\B is\\O out\\O of\\O this\\O world\\O .\\O", "opinion_tags": "While\\O their\\O kitchen\\O food\\O is\\O delicious\\O ,\\O their\\O Sushi\\O is\\O out\\B of\\I this\\I world\\I .\\O"}], "postag": ["IN", "PRP$", "NN", "NN", "VBZ", "JJ", ",", "PRP$", "NN", "VBZ", "IN", "IN", "DT", "NN", "."], "head": [6, 4, 4, 6, 6, 14, 14, 9, 14, 14, 14, 14, 14, 0, 14], "deprel": ["mark", "nmod:poss", "compound", "nsubj", "cop", "advcl", "punct", "nmod:poss", "nsubj", "cop", "case", "case", "det", "root", "punct"]}, {"id": "93", "sentence": "Mizu is home to creative and unique rolls not to found anywhere else .", "triples": [{"uid": "93-0", "sentiment": "positive", "target_tags": "Mizu\\O is\\O home\\O to\\O creative\\O and\\O unique\\O rolls\\B not\\O to\\O found\\O anywhere\\O else\\O .\\O", "opinion_tags": "Mizu\\O is\\O home\\O to\\O creative\\O and\\O unique\\B rolls\\O not\\O to\\O found\\O anywhere\\O else\\O .\\O"}], "postag": ["NNP", "VBZ", "NN", "IN", "JJ", "CC", "JJ", "NNS", "RB", "TO", "VB", "RB", "JJ", "."], "head": [3, 3, 0, 8, 8, 7, 5, 3, 11, 11, 3, 11, 12, 3], "deprel": ["nsubj", "cop", "root", "case", "amod", "cc", "conj", "nmod", "advmod", "mark", "advcl", "advmod", "amod", "punct"]}, {"id": "94", "sentence": "Not only is the cuisine the best around , the service has always been attentive and charming .", "triples": [{"uid": "94-0", "sentiment": "positive", "target_tags": "Not\\O only\\O is\\O the\\O cuisine\\B the\\O best\\O around\\O ,\\O the\\O service\\O has\\O always\\O been\\O attentive\\O and\\O charming\\O .\\O", "opinion_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\B around\\O ,\\O the\\O service\\O has\\O always\\O been\\O attentive\\O and\\O charming\\O .\\O"}, {"uid": "94-1", "sentiment": "positive", "target_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\O around\\O ,\\O the\\O service\\B has\\O always\\O been\\O attentive\\O and\\O charming\\O .\\O", "opinion_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\O around\\O ,\\O the\\O service\\O has\\O always\\O been\\O attentive\\B and\\O charming\\O .\\O"}, {"uid": "94-2", "sentiment": "positive", "target_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\O around\\O ,\\O the\\O service\\B has\\O always\\O been\\O attentive\\O and\\O charming\\O .\\O", "opinion_tags": "Not\\O only\\O is\\O the\\O cuisine\\O the\\O best\\O around\\O ,\\O the\\O service\\O has\\O always\\O been\\O attentive\\O and\\O charming\\B .\\O"}], "postag": ["RB", "RB", "VBZ", "DT", "NN", "DT", "JJS", "RB", ",", "DT", "NN", "VBZ", "RB", "VBN", "JJ", "CC", "JJ", "."], "head": [2, 8, 8, 5, 8, 7, 8, 0, 8, 11, 15, 15, 15, 15, 8, 17, 15, 8], "deprel": ["advmod", "advmod", "cop", "det", "nsubj", "det", "amod", "root", "punct", "det", "nsubj", "aux", "advmod", "cop", "parataxis", "cc", "conj", "punct"]}, {"id": "95", "sentence": "The entree was bland and small , dessert was not inspired .", "triples": [{"uid": "95-0", "sentiment": "negative", "target_tags": "The\\O entree\\B was\\O bland\\O and\\O small\\O ,\\O dessert\\O was\\O not\\O inspired\\O .\\O", "opinion_tags": "The\\O entree\\O was\\O bland\\B and\\O small\\O ,\\O dessert\\O was\\O not\\O inspired\\O .\\O"}, {"uid": "95-1", "sentiment": "negative", "target_tags": "The\\O entree\\B was\\O bland\\O and\\O small\\O ,\\O dessert\\O was\\O not\\O inspired\\O .\\O", "opinion_tags": "The\\O entree\\O was\\O bland\\O and\\O small\\B ,\\O dessert\\O was\\O not\\O inspired\\O .\\O"}, {"uid": "95-2", "sentiment": "negative", "target_tags": "The\\O entree\\O was\\O bland\\O and\\O small\\O ,\\O dessert\\B was\\O not\\O inspired\\O .\\O", "opinion_tags": "The\\O entree\\O was\\O bland\\O and\\O small\\O ,\\O dessert\\O was\\O not\\B inspired\\I .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", "CC", "JJ", ",", "NN", "VBD", "RB", "VBN", "."], "head": [2, 4, 4, 0, 6, 4, 4, 11, 11, 11, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "punct", "nsubj:pass", "aux:pass", "advmod", "parataxis", "punct"]}, {"id": "96", "sentence": "Wonderful strawberry daiquiries as well !", "triples": [{"uid": "96-0", "sentiment": "positive", "target_tags": "Wonderful\\O strawberry\\B daiquiries\\I as\\O well\\O !\\O", "opinion_tags": "Wonderful\\B strawberry\\O daiquiries\\O as\\O well\\O !\\O"}], "postag": ["JJ", "NN", "NNS", "RB", "RB", "."], "head": [3, 3, 0, 3, 4, 3], "deprel": ["amod", "compound", "root", "advmod", "fixed", "punct"]}, {"id": "97", "sentence": "Authentic Taiwanese food that 's cheap ... what more could you ask for ?", "triples": [{"uid": "97-0", "sentiment": "positive", "target_tags": "Authentic\\O Taiwanese\\B food\\I that\\O 's\\O cheap\\O ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O", "opinion_tags": "Authentic\\B Taiwanese\\O food\\O that\\O 's\\O cheap\\O ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O"}, {"uid": "97-1", "sentiment": "positive", "target_tags": "Authentic\\O Taiwanese\\B food\\I that\\O 's\\O cheap\\O ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O", "opinion_tags": "Authentic\\O Taiwanese\\O food\\O that\\O 's\\O cheap\\B ...\\O what\\O more\\O could\\O you\\O ask\\O for\\O ?\\O"}], "postag": ["JJ", "JJ", "NN", "WDT", "VBZ", "JJ", ",", "WP", "JJR", "MD", "PRP", "VB", "IN", "."], "head": [3, 3, 0, 6, 6, 3, 3, 9, 12, 12, 12, 3, 12, 3], "deprel": ["amod", "amod", "root", "nsubj", "cop", "acl:relcl", "punct", "det", "advmod", "aux", "nsubj", "parataxis", "obl", "punct"]}, {"id": "98", "sentence": "My friend devoured her chicken and mashed potatos .", "triples": [{"uid": "98-0", "sentiment": "positive", "target_tags": "My\\O friend\\O devoured\\O her\\O chicken\\B and\\I mashed\\I potatos\\I .\\O", "opinion_tags": "My\\O friend\\O devoured\\B her\\O chicken\\O and\\O mashed\\O potatos\\O .\\O"}], "postag": ["PRP$", "NN", "VBD", "PRP$", "NN", "CC", "VBD", "NNS", "."], "head": [2, 3, 0, 5, 3, 7, 3, 7, 3], "deprel": ["nmod:poss", "nsubj", "root", "nmod:poss", "obj", "cc", "conj", "obj", "punct"]}, {"id": "99", "sentence": "Great neighborhood joint .", "triples": [{"uid": "99-0", "sentiment": "positive", "target_tags": "Great\\O neighborhood\\O joint\\B .\\O", "opinion_tags": "Great\\B neighborhood\\O joint\\O .\\O"}], "postag": ["JJ", "NN", "NN", "."], "head": [3, 3, 0, 3], "deprel": ["amod", "compound", "root", "punct"]}, {"id": "100", "sentence": "This is a nice pizza place with good selection of thin crust pizza including the Basil slice .", "triples": [{"uid": "100-0", "sentiment": "positive", "target_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\O selection\\B of\\I thin\\I crust\\I pizza\\I including\\O the\\O Basil\\O slice\\O .\\O", "opinion_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\B selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O"}, {"uid": "100-1", "sentiment": "positive", "target_tags": "This\\O is\\O a\\O nice\\O pizza\\B place\\I with\\O good\\O selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O", "opinion_tags": "This\\O is\\O a\\O nice\\B pizza\\O place\\O with\\O good\\O selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O"}, {"uid": "100-2", "sentiment": "positive", "target_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\O selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\B slice\\I .\\O", "opinion_tags": "This\\O is\\O a\\O nice\\O pizza\\O place\\O with\\O good\\B selection\\O of\\O thin\\O crust\\O pizza\\O including\\O the\\O Basil\\O slice\\O .\\O"}], "postag": ["DT", "VBZ", "DT", "JJ", "NN", "NN", "IN", "JJ", "NN", "IN", "JJ", "NN", "NN", "VBG", "DT", "NN", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 9, 9, 6, 13, 13, 13, 9, 17, 17, 17, 13, 6], "deprel": ["nsubj", "cop", "det", "amod", "compound", "root", "case", "amod", "nmod", "case", "amod", "compound", "nmod", "case", "det", "compound", "nmod", "punct"]}, {"id": "101", "sentence": "The dosas are skimpy , unattractive and drip with grease , and personally I 'd drink popcorn topping before I 'd eat another one of these .", "triples": [{"uid": "101-0", "sentiment": "negative", "target_tags": "The\\O dosas\\B are\\O skimpy\\O ,\\O unattractive\\O and\\O drip\\O with\\O grease\\O ,\\O and\\O personally\\O I\\O 'd\\O drink\\O popcorn\\O topping\\O before\\O I\\O 'd\\O eat\\O another\\O one\\O of\\O these\\O .\\O", "opinion_tags": "The\\O dosas\\O are\\O skimpy\\B ,\\O unattractive\\O and\\O drip\\O with\\O grease\\O ,\\O and\\O personally\\O I\\O 'd\\O drink\\O popcorn\\O topping\\O before\\O I\\O 'd\\O eat\\O another\\O one\\O of\\O these\\O .\\O"}, {"uid": "101-1", "sentiment": "negative", "target_tags": "The\\O dosas\\B are\\O skimpy\\O ,\\O unattractive\\O and\\O drip\\O with\\O grease\\O ,\\O and\\O personally\\O I\\O 'd\\O drink\\O popcorn\\O topping\\O before\\O I\\O 'd\\O eat\\O another\\O one\\O of\\O these\\O .\\O", "opinion_tags": "The\\O dosas\\O are\\O skimpy\\O ,\\O unattractive\\B and\\O drip\\O with\\O grease\\O ,\\O and\\O personally\\O I\\O 'd\\O drink\\O popcorn\\O topping\\O before\\O I\\O 'd\\O eat\\O another\\O one\\O of\\O these\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", ",", "JJ", "CC", "JJ", "IN", "NN", ",", "CC", "RB", "PRP", "MD", "VB", "NN", "VBG", "IN", "PRP", "MD", "VB", "DT", "CD", "IN", "DT", "."], "head": [2, 4, 4, 0, 6, 4, 8, 4, 10, 4, 16, 16, 16, 16, 16, 4, 18, 16, 22, 22, 22, 16, 24, 22, 26, 24, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "conj", "cc", "conj", "case", "obl", "punct", "cc", "advmod", "nsubj", "aux", "conj", "compound", "xcomp", "mark", "nsubj", "aux", "advcl", "det", "obj", "case", "nmod", "punct"]}, {"id": "102", "sentence": "Best Italian food I ever had ( and being Italian , that means alot ) .", "triples": [{"uid": "102-0", "sentiment": "positive", "target_tags": "Best\\O Italian\\B food\\I I\\O ever\\O had\\O (\\O and\\O being\\O Italian\\O ,\\O that\\O means\\O alot\\O )\\O .\\O", "opinion_tags": "Best\\B Italian\\O food\\O I\\O ever\\O had\\O (\\O and\\O being\\O Italian\\O ,\\O that\\O means\\O alot\\O )\\O .\\O"}], "postag": ["JJS", "JJ", "NN", "PRP", "RB", "VBD", "-LRB-", "CC", "VBG", "JJ", ",", "DT", "VBZ", "NN", "-RRB-", "."], "head": [3, 3, 0, 6, 6, 3, 3, 10, 10, 13, 13, 13, 3, 13, 13, 3], "deprel": ["amod", "amod", "root", "nsubj", "advmod", "acl:relcl", "punct", "cc", "cop", "advcl", "punct", "nsubj", "parataxis", "obj", "punct", "punct"]}, {"id": "103", "sentence": "The restaurant looks out over beautiful green lawns to the Hudson River and the Statue of Liberty .", "triples": [{"uid": "103-0", "sentiment": "positive", "target_tags": "The\\O restaurant\\B looks\\O out\\O over\\O beautiful\\O green\\O lawns\\O to\\O the\\O Hudson\\O River\\O and\\O the\\O Statue\\O of\\O Liberty\\O .\\O", "opinion_tags": "The\\O restaurant\\O looks\\O out\\O over\\O beautiful\\B green\\O lawns\\O to\\O the\\O Hudson\\O River\\O and\\O the\\O Statue\\O of\\O Liberty\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "RP", "IN", "JJ", "JJ", "NNS", "IN", "DT", "NNP", "NNP", "CC", "DT", "NNP", "IN", "NNP", "."], "head": [2, 3, 0, 3, 8, 8, 8, 3, 12, 12, 12, 8, 15, 15, 12, 17, 15, 3], "deprel": ["det", "nsubj", "root", "compound:prt", "case", "amod", "amod", "obl", "case", "det", "compound", "nmod", "cc", "det", "conj", "case", "nmod", "punct"]}, {"id": "104", "sentence": "The food is good , especially their more basic dishes , and the drinks are delicious .", "triples": [{"uid": "104-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\B ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O"}, {"uid": "104-1", "sentiment": "positive", "target_tags": "The\\O food\\O is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\B dishes\\I ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\B ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\O .\\O"}, {"uid": "104-2", "sentiment": "positive", "target_tags": "The\\O food\\O is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\B are\\O delicious\\O .\\O", "opinion_tags": "The\\O food\\O is\\O good\\O ,\\O especially\\O their\\O more\\O basic\\O dishes\\O ,\\O and\\O the\\O drinks\\O are\\O delicious\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", ",", "RB", "PRP$", "RBR", "JJ", "NNS", ",", "CC", "DT", "NNS", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 4, 10, 10, 9, 10, 4, 16, 16, 14, 16, 16, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "advmod", "nmod:poss", "advmod", "amod", "parataxis", "punct", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "105", "sentence": "Good spreads , great beverage selections and bagels really tasty .", "triples": [{"uid": "105-0", "sentiment": "positive", "target_tags": "Good\\O spreads\\B ,\\O great\\O beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\O .\\O", "opinion_tags": "Good\\B spreads\\O ,\\O great\\O beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\O .\\O"}, {"uid": "105-1", "sentiment": "positive", "target_tags": "Good\\O spreads\\O ,\\O great\\O beverage\\B selections\\I and\\O bagels\\O really\\O tasty\\O .\\O", "opinion_tags": "Good\\O spreads\\O ,\\O great\\B beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\O .\\O"}, {"uid": "105-2", "sentiment": "positive", "target_tags": "Good\\O spreads\\O ,\\O great\\O beverage\\O selections\\O and\\O bagels\\B really\\O tasty\\O .\\O", "opinion_tags": "Good\\O spreads\\O ,\\O great\\O beverage\\O selections\\O and\\O bagels\\O really\\O tasty\\B .\\O"}], "postag": ["JJ", "NNS", ",", "JJ", "NN", "NNS", "CC", "NNS", "RB", "JJ", "."], "head": [2, 0, 6, 6, 6, 2, 8, 2, 10, 2, 2], "deprel": ["amod", "root", "punct", "amod", "compound", "conj", "cc", "conj", "advmod", "conj", "punct"]}, {"id": "106", "sentence": "The cream cheeses are out of this world and I love that coffee ! !", "triples": [{"uid": "106-0", "sentiment": "positive", "target_tags": "The\\O cream\\B cheeses\\I are\\O out\\O of\\O this\\O world\\O and\\O I\\O love\\O that\\O coffee\\O !\\O !\\O", "opinion_tags": "The\\O cream\\O cheeses\\O are\\O out\\B of\\I this\\I world\\I and\\O I\\O love\\O that\\O coffee\\O !\\O !\\O"}, {"uid": "106-1", "sentiment": "positive", "target_tags": "The\\O cream\\O cheeses\\O are\\O out\\O of\\O this\\O world\\O and\\O I\\O love\\O that\\O coffee\\B !\\O !\\O", "opinion_tags": "The\\O cream\\O cheeses\\O are\\O out\\O of\\O this\\O world\\O and\\O I\\O love\\B that\\O coffee\\O !\\O !\\O"}], "postag": ["DT", "NN", "NNS", "VBP", "IN", "IN", "DT", "NN", "CC", "PRP", "VBP", "DT", "NN", ".", "."], "head": [3, 3, 8, 8, 8, 8, 8, 0, 11, 11, 8, 13, 11, 8, 8], "deprel": ["det", "compound", "nsubj", "cop", "case", "case", "det", "root", "cc", "nsubj", "conj", "det", "obj", "punct", "punct"]}, {"id": "107", "sentence": "The fish was adequate , but inexpertly sliced .", "triples": [{"uid": "107-0", "sentiment": "negative", "target_tags": "The\\O fish\\B was\\O adequate\\O ,\\O but\\O inexpertly\\O sliced\\O .\\O", "opinion_tags": "The\\O fish\\O was\\O adequate\\B ,\\O but\\O inexpertly\\O sliced\\O .\\O"}, {"uid": "107-1", "sentiment": "negative", "target_tags": "The\\O fish\\B was\\O adequate\\O ,\\O but\\O inexpertly\\O sliced\\O .\\O", "opinion_tags": "The\\O fish\\O was\\O adequate\\O ,\\O but\\O inexpertly\\B sliced\\I .\\O"}], "postag": ["DT", "NN", "VBD", "JJ", ",", "CC", "RB", "VBN", "."], "head": [2, 4, 4, 0, 8, 8, 8, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "punct", "cc", "advmod", "conj", "punct"]}, {"id": "108", "sentence": "Well , this place is so Ghetto its not even funny .", "triples": [{"uid": "108-0", "sentiment": "negative", "target_tags": "Well\\O ,\\O this\\O place\\B is\\O so\\O Ghetto\\O its\\O not\\O even\\O funny\\O .\\O", "opinion_tags": "Well\\O ,\\O this\\O place\\O is\\O so\\O Ghetto\\B its\\O not\\O even\\O funny\\O .\\O"}, {"uid": "108-1", "sentiment": "negative", "target_tags": "Well\\O ,\\O this\\O place\\B is\\O so\\O Ghetto\\O its\\O not\\O even\\O funny\\O .\\O", "opinion_tags": "Well\\O ,\\O this\\O place\\O is\\O so\\O Ghetto\\O its\\O not\\B even\\I funny\\I .\\O"}], "postag": ["UH", ",", "DT", "NN", "VBZ", "RB", "NNP", "PRP$", "RB", "RB", "JJ", "."], "head": [7, 7, 4, 7, 7, 7, 0, 11, 11, 11, 7, 7], "deprel": ["discourse", "punct", "det", "nsubj", "cop", "advmod", "root", "nmod:poss", "advmod", "advmod", "conj", "punct"]}, {"id": "109", "sentence": "Awsome Pizza especially the Margheritta slice .", "triples": [{"uid": "109-0", "sentiment": "positive", "target_tags": "Awsome\\O Pizza\\B especially\\O the\\O Margheritta\\O slice\\O .\\O", "opinion_tags": "Awsome\\B Pizza\\O especially\\O the\\O Margheritta\\O slice\\O .\\O"}, {"uid": "109-1", "sentiment": "positive", "target_tags": "Awsome\\O Pizza\\O especially\\O the\\O Margheritta\\B slice\\I .\\O", "opinion_tags": "Awsome\\B Pizza\\O especially\\O the\\O Margheritta\\O slice\\O .\\O"}], "postag": ["JJ", "NN", "RB", "DT", "NNP", "NN", "."], "head": [2, 0, 6, 6, 6, 2, 2], "deprel": ["amod", "root", "advmod", "det", "compound", "appos", "punct"]}, {"id": "110", "sentence": "Everything about this restaurant was special .", "triples": [{"uid": "110-0", "sentiment": "positive", "target_tags": "Everything\\O about\\O this\\O restaurant\\B was\\O special\\O .\\O", "opinion_tags": "Everything\\O about\\O this\\O restaurant\\O was\\O special\\B .\\O"}], "postag": ["NN", "IN", "DT", "NN", "VBD", "JJ", "."], "head": [6, 4, 4, 1, 6, 0, 6], "deprel": ["nsubj", "case", "det", "nmod", "cop", "root", "punct"]}, {"id": "111", "sentence": "You ca n't go wrong with this place .", "triples": [{"uid": "111-0", "sentiment": "positive", "target_tags": "You\\O ca\\O n't\\O go\\O wrong\\O with\\O this\\O place\\B .\\O", "opinion_tags": "You\\O ca\\B n't\\I go\\I wrong\\I with\\O this\\O place\\O .\\O"}], "postag": ["PRP", "MD", "RB", "VB", "JJ", "IN", "DT", "NN", "."], "head": [4, 4, 4, 0, 4, 8, 8, 4, 4], "deprel": ["nsubj", "aux", "advmod", "root", "advmod", "case", "det", "obl", "punct"]}, {"id": "112", "sentence": "The food is excellent !", "triples": [{"uid": "112-0", "sentiment": "positive", "target_tags": "The\\O food\\B is\\O excellent\\O !\\O", "opinion_tags": "The\\O food\\O is\\O excellent\\B !\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "."], "head": [2, 4, 4, 0, 4], "deprel": ["det", "nsubj", "cop", "root", "punct"]}, {"id": "113", "sentence": "The restaurant is a bit noisy but that is something that can be overlooked once you sit down and enjoy a great meal", "triples": [{"uid": "113-0", "sentiment": "positive", "target_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\B", "opinion_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\B a\\O great\\O meal\\O"}, {"uid": "113-1", "sentiment": "positive", "target_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\B", "opinion_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\B meal\\O"}, {"uid": "113-2", "sentiment": "negative", "target_tags": "The\\O restaurant\\B is\\O a\\O bit\\O noisy\\O but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\O", "opinion_tags": "The\\O restaurant\\O is\\O a\\O bit\\O noisy\\B but\\O that\\O is\\O something\\O that\\O can\\O be\\O overlooked\\O once\\O you\\O sit\\O down\\O and\\O enjoy\\O a\\O great\\O meal\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "NN", "JJ", "CC", "DT", "VBZ", "NN", "WDT", "MD", "VB", "VBN", "IN", "PRP", "VBP", "RP", "CC", "VB", "DT", "JJ", "NN"], "head": [2, 6, 6, 5, 6, 0, 10, 10, 10, 6, 14, 14, 14, 10, 17, 17, 14, 17, 20, 17, 23, 23, 20], "deprel": ["det", "nsubj", "cop", "det", "obl:npmod", "root", "cc", "nsubj", "cop", "conj", "nsubj:pass", "aux", "aux:pass", "acl:relcl", "mark", "nsubj", "advcl", "compound:prt", "cc", "conj", "det", "amod", "obj"]}, {"id": "114", "sentence": "this little place has a cute interior decor and affordable city prices .", "triples": [{"uid": "114-0", "sentiment": "positive", "target_tags": "this\\O little\\O place\\O has\\O a\\O cute\\O interior\\B decor\\I and\\O affordable\\O city\\O prices\\O .\\O", "opinion_tags": "this\\O little\\O place\\O has\\O a\\O cute\\B interior\\O decor\\O and\\O affordable\\O city\\O prices\\O .\\O"}, {"uid": "114-1", "sentiment": "positive", "target_tags": "this\\O little\\O place\\B has\\O a\\O cute\\O interior\\O decor\\O and\\O affordable\\O city\\O prices\\O .\\O", "opinion_tags": "this\\O little\\B place\\O has\\O a\\O cute\\O interior\\O decor\\O and\\O affordable\\O city\\O prices\\O .\\O"}], "postag": ["DT", "JJ", "NN", "VBZ", "DT", "JJ", "JJ", "NN", "CC", "JJ", "NN", "NNS", "."], "head": [3, 3, 4, 0, 8, 8, 8, 4, 12, 12, 12, 8, 4], "deprel": ["det", "amod", "nsubj", "root", "det", "amod", "amod", "obj", "cc", "amod", "compound", "conj", "punct"]}, {"id": "115", "sentence": "delicious bagels , especially when right out of the oven .", "triples": [{"uid": "115-0", "sentiment": "positive", "target_tags": "delicious\\O bagels\\B ,\\O especially\\O when\\O right\\O out\\O of\\O the\\O oven\\O .\\O", "opinion_tags": "delicious\\B bagels\\O ,\\O especially\\O when\\O right\\O out\\O of\\O the\\O oven\\O .\\O"}], "postag": ["JJ", "NNS", ",", "RB", "WRB", "RB", "IN", "IN", "DT", "NN", "."], "head": [2, 0, 2, 10, 10, 10, 10, 10, 10, 2, 2], "deprel": ["amod", "root", "punct", "advmod", "mark", "advmod", "case", "case", "det", "advcl", "punct"]}, {"id": "116", "sentence": "Service is fast and friendly .", "triples": [{"uid": "116-0", "sentiment": "positive", "target_tags": "Service\\B is\\O fast\\O and\\O friendly\\O .\\O", "opinion_tags": "Service\\O is\\O fast\\B and\\O friendly\\O .\\O"}, {"uid": "116-1", "sentiment": "positive", "target_tags": "Service\\B is\\O fast\\O and\\O friendly\\O .\\O", "opinion_tags": "Service\\O is\\O fast\\O and\\O friendly\\B .\\O"}], "postag": ["NN", "VBZ", "JJ", "CC", "JJ", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["nsubj", "cop", "root", "cc", "conj", "punct"]}, {"id": "117", "sentence": "<PERSON> is a good restaurant , but it 's nothing special .", "triples": [{"uid": "117-0", "sentiment": "neutral", "target_tags": "Rao\\B is\\O a\\O good\\O restaurant\\O ,\\O but\\O it\\O 's\\O nothing\\O special\\O .\\O", "opinion_tags": "Rao\\O is\\O a\\O good\\B restaurant\\O ,\\O but\\O it\\O 's\\O nothing\\O special\\O .\\O"}], "postag": ["NNP", "VBZ", "DT", "JJ", "NN", ",", "CC", "PRP", "VBZ", "NN", "JJ", "."], "head": [5, 5, 5, 5, 0, 10, 10, 10, 10, 5, 10, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "punct", "cc", "nsubj", "cop", "conj", "amod", "punct"]}, {"id": "118", "sentence": "I really loved the different and inovated touch that 's the cheff gives to the food .", "triples": [{"uid": "118-0", "sentiment": "positive", "target_tags": "I\\O really\\O loved\\O the\\O different\\O and\\O inovated\\O touch\\O that\\O 's\\O the\\O cheff\\B gives\\O to\\O the\\O food\\O .\\O", "opinion_tags": "I\\O really\\O loved\\B the\\O different\\O and\\O inovated\\O touch\\O that\\O 's\\O the\\O cheff\\O gives\\O to\\O the\\O food\\O .\\O"}, {"uid": "118-1", "sentiment": "positive", "target_tags": "I\\O really\\O loved\\O the\\O different\\O and\\O inovated\\O touch\\O that\\O 's\\O the\\O cheff\\B gives\\O to\\O the\\O food\\O .\\O", "opinion_tags": "I\\O really\\O loved\\O the\\O different\\O and\\O inovated\\B touch\\O that\\O 's\\O the\\O cheff\\O gives\\O to\\O the\\O food\\O .\\O"}], "postag": ["PRP", "RB", "VBD", "DT", "JJ", "CC", "JJ", "NN", "WDT", "VBZ", "DT", "NN", "VBZ", "IN", "DT", "NN", "."], "head": [3, 3, 0, 8, 8, 7, 5, 3, 13, 13, 12, 13, 8, 16, 16, 13, 3], "deprel": ["nsubj", "advmod", "root", "det", "amod", "cc", "conj", "obj", "obj", "aux", "det", "nsubj", "acl:relcl", "case", "det", "obl", "punct"]}, {"id": "119", "sentence": "Drawbacks : service is slow and they do n't toast !", "triples": [{"uid": "119-0", "sentiment": "negative", "target_tags": "Drawbacks\\O :\\O service\\B is\\O slow\\O and\\O they\\O do\\O n't\\O toast\\O !\\O", "opinion_tags": "Drawbacks\\O :\\O service\\O is\\O slow\\B and\\O they\\O do\\O n't\\O toast\\O !\\O"}], "postag": ["NNS", ":", "NN", "VBZ", "JJ", "CC", "PRP", "VBP", "RB", "VB", "."], "head": [0, 1, 5, 5, 1, 10, 10, 10, 10, 5, 1], "deprel": ["root", "punct", "nsubj", "cop", "appos", "cc", "nsubj", "aux", "advmod", "conj", "punct"]}, {"id": "120", "sentence": "Volare virgins or weekly regulars , everyone gets treated the same and you ca n't ask for more than that when the service is this friendly .", "triples": [{"uid": "120-0", "sentiment": "positive", "target_tags": "Volare\\O virgins\\O or\\O weekly\\O regulars\\O ,\\O everyone\\O gets\\O treated\\O the\\O same\\O and\\O you\\O ca\\O n't\\O ask\\O for\\O more\\O than\\O that\\O when\\O the\\O service\\B is\\O this\\O friendly\\O .\\O", "opinion_tags": "Volare\\O virgins\\O or\\O weekly\\O regulars\\O ,\\O everyone\\O gets\\O treated\\O the\\O same\\O and\\O you\\O ca\\O n't\\O ask\\O for\\O more\\O than\\O that\\O when\\O the\\O service\\O is\\O this\\O friendly\\B .\\O"}], "postag": ["NN", "NNS", "CC", "JJ", "NNS", ",", "NN", "VBZ", "VBN", "DT", "JJ", "CC", "PRP", "MD", "RB", "VB", "IN", "JJR", "IN", "DT", "WRB", "DT", "NN", "VBZ", "DT", "JJ", "."], "head": [2, 0, 5, 5, 2, 2, 9, 9, 2, 11, 9, 16, 16, 16, 16, 9, 20, 16, 20, 16, 26, 23, 26, 26, 26, 16, 9], "deprel": ["compound", "root", "cc", "amod", "conj", "punct", "nsubj:pass", "aux:pass", "parataxis", "det", "obj", "cc", "nsubj", "aux", "advmod", "conj", "case", "obl", "case", "obl", "mark", "det", "nsubj", "cop", "obl:npmod", "advcl", "punct"]}, {"id": "121", "sentence": "Lucky Strike is a great casual place to just grab a bite to eat .", "triples": [{"uid": "121-0", "sentiment": "positive", "target_tags": "Lucky\\B Strike\\I is\\O a\\O great\\O casual\\O place\\O to\\O just\\O grab\\O a\\O bite\\O to\\O eat\\O .\\O", "opinion_tags": "Lucky\\O Strike\\O is\\O a\\O great\\B casual\\I place\\O to\\O just\\O grab\\O a\\O bite\\O to\\O eat\\O .\\O"}], "postag": ["NNP", "NNP", "VBZ", "DT", "JJ", "JJ", "NN", "TO", "RB", "VB", "DT", "NN", "TO", "VB", "."], "head": [2, 7, 7, 7, 7, 7, 0, 10, 10, 7, 12, 10, 14, 10, 7], "deprel": ["compound", "nsubj", "cop", "det", "amod", "amod", "root", "mark", "advmod", "acl", "det", "obj", "mark", "advcl", "punct"]}, {"id": "122", "sentence": "What a great place !", "triples": [{"uid": "122-0", "sentiment": "positive", "target_tags": "What\\O a\\O great\\O place\\B !\\O", "opinion_tags": "What\\O a\\O great\\B place\\O !\\O"}], "postag": ["WDT", "DT", "JJ", "NN", "."], "head": [4, 4, 4, 0, 4], "deprel": ["det:predet", "det", "amod", "root", "punct"]}, {"id": "123", "sentence": "This place has the best Chinese style BBQ ribs in the city .", "triples": [{"uid": "123-0", "sentiment": "positive", "target_tags": "This\\O place\\O has\\O the\\O best\\O Chinese\\O style\\O BBQ\\B ribs\\I in\\O the\\O city\\O .\\O", "opinion_tags": "This\\O place\\O has\\O the\\O best\\B Chinese\\O style\\O BBQ\\O ribs\\O in\\O the\\O city\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "DT", "JJS", "JJ", "NN", "NN", "NNS", "IN", "DT", "NN", "."], "head": [2, 3, 0, 9, 9, 9, 9, 9, 3, 12, 12, 9, 3], "deprel": ["det", "nsubj", "root", "det", "amod", "amod", "compound", "compound", "obj", "case", "det", "nmod", "punct"]}, {"id": "124", "sentence": "Quick and friendly service .", "triples": [{"uid": "124-0", "sentiment": "positive", "target_tags": "Quick\\O and\\O friendly\\O service\\B .\\O", "opinion_tags": "Quick\\B and\\O friendly\\O service\\O .\\O"}, {"uid": "124-1", "sentiment": "positive", "target_tags": "Quick\\O and\\O friendly\\O service\\B .\\O", "opinion_tags": "Quick\\O and\\O friendly\\B service\\O .\\O"}], "postag": ["JJ", "CC", "JJ", "NN", "."], "head": [4, 3, 1, 0, 4], "deprel": ["amod", "cc", "conj", "root", "punct"]}, {"id": "125", "sentence": "This little place definitely exceeded my expectations and you sure get a lot of food for your money .", "triples": [{"uid": "125-0", "sentiment": "positive", "target_tags": "This\\O little\\O place\\O definitely\\O exceeded\\O my\\O expectations\\O and\\O you\\O sure\\O get\\O a\\O lot\\O of\\O food\\B for\\O your\\O money\\O .\\O", "opinion_tags": "This\\O little\\O place\\O definitely\\O exceeded\\O my\\O expectations\\O and\\O you\\O sure\\O get\\O a\\O lot\\B of\\O food\\O for\\O your\\O money\\O .\\O"}, {"uid": "125-1", "sentiment": "positive", "target_tags": "This\\O little\\O place\\B definitely\\O exceeded\\O my\\O expectations\\O and\\O you\\O sure\\O get\\O a\\O lot\\O of\\O food\\O for\\O your\\O money\\O .\\O", "opinion_tags": "This\\O little\\O place\\O definitely\\O exceeded\\B my\\I expectations\\I and\\O you\\O sure\\O get\\O a\\O lot\\O of\\O food\\O for\\O your\\O money\\O .\\O"}], "postag": ["DT", "JJ", "NN", "RB", "VBD", "PRP$", "NNS", "CC", "PRP", "RB", "VBP", "DT", "NN", "IN", "NN", "IN", "PRP$", "NN", "."], "head": [3, 3, 5, 5, 0, 7, 5, 11, 11, 11, 5, 13, 11, 15, 13, 18, 18, 11, 5], "deprel": ["det", "amod", "nsubj", "advmod", "root", "nmod:poss", "obj", "cc", "nsubj", "advmod", "conj", "det", "obj", "case", "nmod", "case", "nmod:poss", "obl", "punct"]}, {"id": "126", "sentence": "Service , however , was excellent ... and I liked the setting/atmosphere a lot .", "triples": [{"uid": "126-0", "sentiment": "positive", "target_tags": "Service\\B ,\\O however\\O ,\\O was\\O excellent\\O ...\\O and\\O I\\O liked\\O the\\O setting/atmosphere\\O a\\O lot\\O .\\O", "opinion_tags": "Service\\O ,\\O however\\O ,\\O was\\O excellent\\B ...\\O and\\O I\\O liked\\O the\\O setting/atmosphere\\O a\\O lot\\O .\\O"}, {"uid": "126-1", "sentiment": "positive", "target_tags": "Service\\O ,\\O however\\O ,\\O was\\O excellent\\O ...\\O and\\O I\\O liked\\O the\\O setting/atmosphere\\B a\\O lot\\O .\\O", "opinion_tags": "Service\\O ,\\O however\\O ,\\O was\\O excellent\\O ...\\O and\\O I\\O liked\\B the\\O setting/atmosphere\\O a\\O lot\\O .\\O"}], "postag": ["NN", ",", "RB", ",", "VBD", "JJ", ",", "CC", "PRP", "VBD", "DT", "NNP", "DT", "NN", "."], "head": [6, 6, 6, 6, 6, 0, 6, 10, 10, 6, 12, 10, 14, 10, 6], "deprel": ["nsubj", "punct", "advmod", "punct", "cop", "root", "punct", "cc", "nsubj", "conj", "det", "obj", "det", "obl:npmod", "punct"]}, {"id": "127", "sentence": "People are always friendly .", "triples": [{"uid": "127-0", "sentiment": "positive", "target_tags": "People\\B are\\O always\\O friendly\\O .\\O", "opinion_tags": "People\\O are\\O always\\O friendly\\B .\\O"}], "postag": ["NNS", "VBP", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct"]}, {"id": "128", "sentence": "bottles of wine are cheap and good .", "triples": [{"uid": "128-0", "sentiment": "positive", "target_tags": "bottles\\B of\\I wine\\I are\\O cheap\\O and\\O good\\O .\\O", "opinion_tags": "bottles\\O of\\O wine\\O are\\O cheap\\B and\\O good\\O .\\O"}, {"uid": "128-1", "sentiment": "positive", "target_tags": "bottles\\B of\\I wine\\I are\\O cheap\\O and\\O good\\O .\\O", "opinion_tags": "bottles\\O of\\O wine\\O are\\O cheap\\O and\\O good\\B .\\O"}], "postag": ["NNS", "IN", "NN", "VBP", "JJ", "CC", "JJ", "."], "head": [5, 3, 1, 5, 0, 7, 5, 5], "deprel": ["nsubj", "case", "nmod", "cop", "root", "cc", "conj", "punct"]}, {"id": "129", "sentence": "The food was actually aweful .", "triples": [{"uid": "129-0", "sentiment": "negative", "target_tags": "The\\O food\\B was\\O actually\\O aweful\\O .\\O", "opinion_tags": "The\\O food\\O was\\O actually\\O aweful\\B .\\O"}], "postag": ["DT", "NN", "VBD", "RB", "JJ", "."], "head": [2, 5, 5, 5, 0, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "punct"]}, {"id": "130", "sentence": "the drinks are amazing and half off till 8pm .", "triples": [{"uid": "130-0", "sentiment": "positive", "target_tags": "the\\O drinks\\B are\\O amazing\\O and\\O half\\O off\\O till\\O 8pm\\O .\\O", "opinion_tags": "the\\O drinks\\O are\\O amazing\\B and\\O half\\O off\\O till\\O 8pm\\O .\\O"}], "postag": ["DT", "NNS", "VBP", "JJ", "CC", "JJ", "RP", "IN", "NN", "."], "head": [2, 4, 4, 0, 6, 4, 4, 9, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "conj", "conj", "case", "obl", "punct"]}, {"id": "131", "sentence": "This is an amazing place to try some roti rolls .", "triples": [{"uid": "131-0", "sentiment": "positive", "target_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\O some\\O roti\\B rolls\\I .\\O", "opinion_tags": "This\\O is\\O an\\O amazing\\O place\\O to\\O try\\B some\\O roti\\O rolls\\O .\\O"}], "postag": ["DT", "VBZ", "DT", "JJ", "NN", "TO", "VB", "DT", "NN", "NNS", "."], "head": [5, 5, 5, 5, 0, 7, 5, 10, 10, 7, 5], "deprel": ["nsubj", "cop", "det", "amod", "root", "mark", "acl", "det", "compound", "obj", "punct"]}, {"id": "132", "sentence": "I really recommend the very simple Unda ( Egg ) rolls .", "triples": [{"uid": "132-0", "sentiment": "positive", "target_tags": "I\\O really\\O recommend\\O the\\O very\\O simple\\O Unda\\B (\\I Egg\\I )\\I rolls\\I .\\O", "opinion_tags": "I\\O really\\O recommend\\B the\\O very\\O simple\\O Unda\\O (\\O Egg\\O )\\O rolls\\O .\\O"}, {"uid": "132-1", "sentiment": "positive", "target_tags": "I\\O really\\O recommend\\O the\\O very\\O simple\\O Unda\\B (\\I Egg\\I )\\I rolls\\I .\\O", "opinion_tags": "I\\O really\\O recommend\\O the\\O very\\O simple\\B Unda\\O (\\O Egg\\O )\\O rolls\\O .\\O"}], "postag": ["PRP", "RB", "VBP", "DT", "RB", "JJ", "NN", "-LRB-", "NN", "-RRB-", "NNS", "."], "head": [3, 3, 0, 11, 6, 11, 11, 9, 11, 9, 3, 3], "deprel": ["nsubj", "advmod", "root", "det", "advmod", "amod", "compound", "punct", "compound", "punct", "obj", "punct"]}, {"id": "133", "sentence": "Delicate spices , onions , eggs and a kick-ass roti .", "triples": [{"uid": "133-0", "sentiment": "positive", "target_tags": "Delicate\\O spices\\B ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O"}, {"uid": "133-1", "sentiment": "positive", "target_tags": "Delicate\\O spices\\O ,\\O onions\\B ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O"}, {"uid": "133-2", "sentiment": "positive", "target_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\B and\\O a\\O kick-ass\\O roti\\O .\\O", "opinion_tags": "Delicate\\B spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\O .\\O"}, {"uid": "133-3", "sentiment": "positive", "target_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\O roti\\B .\\O", "opinion_tags": "Delicate\\O spices\\O ,\\O onions\\O ,\\O eggs\\O and\\O a\\O kick-ass\\B roti\\O .\\O"}], "postag": ["JJ", "NNS", ",", "NNS", ",", "NNS", "CC", "DT", "JJ", "NN", "."], "head": [2, 0, 4, 2, 6, 2, 10, 10, 10, 2, 2], "deprel": ["amod", "root", "punct", "conj", "punct", "conj", "cc", "det", "amod", "conj", "punct"]}, {"id": "134", "sentence": "Best drumsticks over rice and sour spicy soup in town !", "triples": [{"uid": "134-0", "sentiment": "positive", "target_tags": "Best\\O drumsticks\\B over\\I rice\\I and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O", "opinion_tags": "Best\\B drumsticks\\O over\\O rice\\O and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O"}, {"uid": "134-1", "sentiment": "positive", "target_tags": "Best\\O drumsticks\\O over\\O rice\\O and\\O sour\\B spicy\\I soup\\I in\\O town\\O !\\O", "opinion_tags": "Best\\B drumsticks\\O over\\O rice\\O and\\O sour\\O spicy\\O soup\\O in\\O town\\O !\\O"}], "postag": ["JJS", "NNS", "IN", "NN", "CC", "JJ", "JJ", "NN", "IN", "NN", "."], "head": [2, 0, 4, 2, 8, 8, 8, 4, 10, 2, 2], "deprel": ["amod", "root", "case", "nmod", "cc", "amod", "amod", "conj", "case", "nmod", "punct"]}, {"id": "135", "sentence": "Beef noodle soup is good as well .", "triples": [{"uid": "135-0", "sentiment": "positive", "target_tags": "Beef\\B noodle\\I soup\\I is\\O good\\O as\\O well\\O .\\O", "opinion_tags": "Beef\\O noodle\\O soup\\O is\\O good\\B as\\O well\\O .\\O"}], "postag": ["NN", "NN", "NN", "VBZ", "JJ", "RB", "RB", "."], "head": [2, 3, 5, 5, 0, 5, 6, 5], "deprel": ["compound", "compound", "nsubj", "cop", "root", "advmod", "fixed", "punct"]}, {"id": "136", "sentence": "Best Taiwanese food in NY !", "triples": [{"uid": "136-0", "sentiment": "positive", "target_tags": "Best\\O Taiwanese\\B food\\I in\\O NY\\O !\\O", "opinion_tags": "Best\\B Taiwanese\\O food\\O in\\O NY\\O !\\O"}], "postag": ["JJS", "JJ", "NN", "IN", "NNP", "."], "head": [3, 3, 0, 5, 3, 3], "deprel": ["amod", "amod", "root", "case", "nmod", "punct"]}, {"id": "137", "sentence": "I have been to Rao 's probably 15 times the past 3 years and it keeps getting better .", "triples": [{"uid": "137-0", "sentiment": "positive", "target_tags": "I\\O have\\O been\\O to\\O Rao\\B 's\\I probably\\O 15\\O times\\O the\\O past\\O 3\\O years\\O and\\O it\\O keeps\\O getting\\O better\\O .\\O", "opinion_tags": "I\\O have\\O been\\O to\\O Rao\\O 's\\O probably\\O 15\\O times\\O the\\O past\\O 3\\O years\\O and\\O it\\O keeps\\O getting\\O better\\B .\\O"}], "postag": ["PRP", "VBP", "VBN", "IN", "NNP", "POS", "RB", "CD", "NNS", "DT", "JJ", "CD", "NNS", "CC", "PRP", "VBZ", "VBG", "JJR", "."], "head": [5, 5, 5, 5, 0, 5, 8, 9, 5, 13, 13, 13, 5, 16, 16, 5, 16, 17, 5], "deprel": ["nsubj", "aux", "cop", "case", "root", "case", "advmod", "nummod", "obl", "det", "amod", "nummod", "obl:tmod", "cc", "nsubj", "conj", "xcomp", "xcomp", "punct"]}, {"id": "138", "sentence": "Staff is very accomodating .", "triples": [{"uid": "138-0", "sentiment": "positive", "target_tags": "Staff\\B is\\O very\\O accomodating\\O .\\O", "opinion_tags": "Staff\\O is\\O very\\O accomodating\\B .\\O"}], "postag": ["NN", "VBZ", "RB", "JJ", "."], "head": [4, 4, 4, 0, 4], "deprel": ["nsubj", "cop", "advmod", "root", "punct"]}, {"id": "139", "sentence": "The decor is very simple but comfortable .", "triples": [{"uid": "139-0", "sentiment": "positive", "target_tags": "The\\O decor\\B is\\O very\\O simple\\O but\\O comfortable\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O very\\O simple\\B but\\O comfortable\\O .\\O"}, {"uid": "139-1", "sentiment": "positive", "target_tags": "The\\O decor\\B is\\O very\\O simple\\O but\\O comfortable\\O .\\O", "opinion_tags": "The\\O decor\\O is\\O very\\O simple\\O but\\O comfortable\\B .\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct"]}, {"id": "140", "sentence": "You must have the crabmeat lasagna which is out of this world and the chocolate bread pudding for dessert .", "triples": [{"uid": "140-0", "sentiment": "positive", "target_tags": "You\\O must\\O have\\O the\\O crabmeat\\B lasagna\\I which\\O is\\O out\\O of\\O this\\O world\\O and\\O the\\O chocolate\\O bread\\O pudding\\O for\\O dessert\\O .\\O", "opinion_tags": "You\\O must\\O have\\O the\\O crabmeat\\O lasagna\\O which\\O is\\O out\\B of\\I this\\I world\\I and\\O the\\O chocolate\\O bread\\O pudding\\O for\\O dessert\\O .\\O"}], "postag": ["PRP", "MD", "VB", "DT", "NN", "NN", "WDT", "VBZ", "IN", "IN", "DT", "NN", "CC", "DT", "NN", "NN", "NN", "IN", "NN", "."], "head": [3, 3, 0, 6, 6, 3, 12, 12, 12, 12, 12, 6, 17, 17, 16, 17, 12, 19, 17, 3], "deprel": ["nsubj", "aux", "root", "det", "compound", "obj", "nsubj", "cop", "case", "case", "det", "acl:relcl", "cc", "det", "compound", "compound", "conj", "case", "nmod", "punct"]}, {"id": "141", "sentence": "whoever the jazz duo was , they were on POINT .", "triples": [{"uid": "141-0", "sentiment": "positive", "target_tags": "whoever\\O the\\O jazz\\B duo\\I was\\O ,\\O they\\O were\\O on\\O POINT\\O .\\O", "opinion_tags": "whoever\\O the\\O jazz\\O duo\\O was\\O ,\\O they\\O were\\O on\\B POINT\\I .\\O"}], "postag": ["WP", "DT", "NN", "NN", "VBD", ",", "PRP", "VBD", "IN", "NN", "."], "head": [0, 4, 4, 1, 1, 1, 10, 10, 10, 1, 10], "deprel": ["root", "det", "compound", "nsubj", "cop", "punct", "nsubj", "cop", "case", "parataxis", "punct"]}, {"id": "142", "sentence": "even the wine by the glass was good .", "triples": [{"uid": "142-0", "sentiment": "positive", "target_tags": "even\\O the\\O wine\\B by\\I the\\I glass\\I was\\O good\\O .\\O", "opinion_tags": "even\\O the\\O wine\\O by\\O the\\O glass\\O was\\O good\\B .\\O"}], "postag": ["RB", "DT", "NN", "IN", "DT", "NN", "VBD", "JJ", "."], "head": [3, 3, 8, 6, 6, 3, 8, 0, 8], "deprel": ["advmod", "det", "nsubj", "case", "det", "nmod", "cop", "root", "punct"]}, {"id": "143", "sentence": "The food is okay and the prices here are mediocre .", "triples": [{"uid": "143-0", "sentiment": "neutral", "target_tags": "The\\O food\\B is\\O okay\\O and\\O the\\O prices\\O here\\O are\\O mediocre\\O .\\O", "opinion_tags": "The\\O food\\O is\\O okay\\B and\\O the\\O prices\\O here\\O are\\O mediocre\\O .\\O"}], "postag": ["DT", "NN", "VBZ", "JJ", "CC", "DT", "NNS", "RB", "VBP", "JJ", "."], "head": [2, 4, 4, 0, 10, 7, 10, 7, 10, 4, 4], "deprel": ["det", "nsubj", "cop", "root", "cc", "det", "nsubj", "advmod", "cop", "conj", "punct"]}, {"id": "144", "sentence": "Baluchi 's has solid food and a nice decor at reasonable prices .", "triples": [{"uid": "144-0", "sentiment": "positive", "target_tags": "Baluchi\\O 's\\O has\\O solid\\O food\\B and\\O a\\O nice\\O decor\\O at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "Baluchi\\O 's\\O has\\O solid\\B food\\O and\\O a\\O nice\\O decor\\O at\\O reasonable\\O prices\\O .\\O"}, {"uid": "144-1", "sentiment": "positive", "target_tags": "Baluchi\\O 's\\O has\\O solid\\O food\\O and\\O a\\O nice\\O decor\\B at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "Baluchi\\O 's\\O has\\O solid\\O food\\O and\\O a\\O nice\\B decor\\O at\\O reasonable\\O prices\\O .\\O"}, {"uid": "144-2", "sentiment": "positive", "target_tags": "Baluchi\\B 's\\I has\\O solid\\O food\\O and\\O a\\O nice\\O decor\\O at\\O reasonable\\O prices\\O .\\O", "opinion_tags": "Baluchi\\O 's\\O has\\O solid\\B food\\O and\\O a\\O nice\\O decor\\O at\\O reasonable\\O prices\\O .\\O"}], "postag": ["NNP", "POS", "VBZ", "JJ", "NN", "CC", "DT", "JJ", "NN", "IN", "JJ", "NNS", "."], "head": [3, 1, 0, 5, 3, 9, 9, 9, 5, 12, 12, 9, 3], "deprel": ["nsubj", "case", "root", "amod", "obj", "cc", "det", "amod", "conj", "case", "amod", "nmod", "punct"]}, {"id": "145", "sentence": "$ 20 for all you can eat sushi can not be beaten .", "triples": [{"uid": "145-0", "sentiment": "positive", "target_tags": "$\\O 20\\O for\\O all\\B you\\I can\\I eat\\I sushi\\I can\\O not\\O be\\O beaten\\O .\\O", "opinion_tags": "$\\O 20\\O for\\O all\\O you\\O can\\O eat\\O sushi\\O can\\O not\\O be\\O beaten\\B .\\O"}], "postag": ["$", "CD", "IN", "DT", "PRP", "MD", "VB", "NN", "MD", "RB", "VB", "VBN", "."], "head": [12, 1, 4, 1, 7, 7, 4, 7, 12, 12, 12, 0, 1], "deprel": ["nsubj:pass", "nummod", "case", "nmod", "nsubj", "aux", "acl:relcl", "obj", "aux", "advmod", "aux:pass", "root", "punct"]}, {"id": "146", "sentence": "The vibe is very relaxed and cozy , service was great and the food was excellent !", "triples": [{"uid": "146-0", "sentiment": "positive", "target_tags": "The\\O vibe\\B is\\O very\\O relaxed\\O and\\O cozy\\O ,\\O service\\O was\\O great\\O and\\O the\\O food\\O was\\O excellent\\O !\\O", "opinion_tags": "The\\O vibe\\O is\\O very\\O relaxed\\B and\\O cozy\\O ,\\O service\\O was\\O great\\O and\\O the\\O food\\O was\\O excellent\\O !\\O"}, {"uid": "146-1", "sentiment": "positive", "target_tags": "The\\O vibe\\B is\\O very\\O relaxed\\O and\\O cozy\\O ,\\O service\\O was\\O great\\O and\\O the\\O food\\O was\\O excellent\\O !\\O", "opinion_tags": "The\\O vibe\\O is\\O very\\O relaxed\\O and\\O cozy\\B ,\\O service\\O was\\O great\\O and\\O the\\O food\\O was\\O excellent\\O !\\O"}, {"uid": "146-2", "sentiment": "positive", "target_tags": "The\\O vibe\\O is\\O very\\O relaxed\\O and\\O cozy\\O ,\\O service\\B was\\O great\\O and\\O the\\O food\\O was\\O excellent\\O !\\O", "opinion_tags": "The\\O vibe\\O is\\O very\\O relaxed\\O and\\O cozy\\O ,\\O service\\O was\\O great\\B and\\O the\\O food\\O was\\O excellent\\O !\\O"}, {"uid": "146-3", "sentiment": "positive", "target_tags": "The\\O vibe\\O is\\O very\\O relaxed\\O and\\O cozy\\O ,\\O service\\O was\\O great\\O and\\O the\\O food\\B was\\O excellent\\O !\\O", "opinion_tags": "The\\O vibe\\O is\\O very\\O relaxed\\O and\\O cozy\\O ,\\O service\\O was\\O great\\O and\\O the\\O food\\O was\\O excellent\\B !\\O"}], "postag": ["DT", "NN", "VBZ", "RB", "JJ", "CC", "JJ", ",", "NN", "VBD", "JJ", "CC", "DT", "NN", "VBD", "JJ", "."], "head": [2, 5, 5, 5, 0, 7, 5, 5, 11, 11, 5, 16, 14, 16, 16, 5, 5], "deprel": ["det", "nsubj", "cop", "advmod", "root", "cc", "conj", "punct", "nsubj", "cop", "conj", "cc", "det", "nsubj", "cop", "conj", "punct"]}, {"id": "147", "sentence": "Never have I had such dramatic delivery guys ( a lot of huffing and panting and muttering under breath b/c I live in a walkup ) who always seem disappointed with their tips .", "triples": [{"uid": "147-0", "sentiment": "negative", "target_tags": "Never\\O have\\O I\\O had\\O such\\O dramatic\\O delivery\\B guys\\I (\\O a\\O lot\\O of\\O huffing\\O and\\O panting\\O and\\O muttering\\O under\\O breath\\O b/c\\O I\\O live\\O in\\O a\\O walkup\\O )\\O who\\O always\\O seem\\O disappointed\\O with\\O their\\O tips\\O .\\O", "opinion_tags": "Never\\O have\\O I\\O had\\O such\\O dramatic\\B delivery\\O guys\\O (\\O a\\O lot\\O of\\O huffing\\O and\\O panting\\O and\\O muttering\\O under\\O breath\\O b/c\\O I\\O live\\O in\\O a\\O walkup\\O )\\O who\\O always\\O seem\\O disappointed\\O with\\O their\\O tips\\O .\\O"}], "postag": ["RB", "VBP", "PRP", "VBN", "JJ", "JJ", "NN", "NNS", "-LRB-", "DT", "NN", "IN", "VBG", "CC", "VBG", "CC", "VBG", "IN", "NN", "IN", "PRP", "VBP", "IN", "DT", "NN", "-RRB-", "WP", "RB", "VBP", "JJ", "IN", "PRP$", "NNS", "."], "head": [4, 4, 4, 0, 8, 8, 8, 4, 11, 11, 8, 13, 11, 15, 13, 17, 13, 19, 17, 22, 22, 17, 25, 25, 22, 11, 29, 29, 8, 29, 33, 33, 30, 4], "deprel": ["advmod", "aux", "nsubj", "root", "amod", "amod", "compound", "obj", "punct", "det", "appos", "case", "nmod", "cc", "conj", "cc", "conj", "case", "obl", "mark", "nsubj", "advcl", "case", "det", "obl", "punct", "nsubj", "advmod", "acl:relcl", "xcomp", "case", "nmod:poss", "obl", "punct"]}]