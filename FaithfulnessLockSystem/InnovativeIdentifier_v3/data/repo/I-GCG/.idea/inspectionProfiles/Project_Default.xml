<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyInterpreterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="103">
            <item index="0" class="java.lang.String" itemvalue="tokenizers" />
            <item index="1" class="java.lang.String" itemvalue="protobuf" />
            <item index="2" class="java.lang.String" itemvalue="transformers" />
            <item index="3" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="4" class="java.lang.String" itemvalue="nltk" />
            <item index="5" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="6" class="java.lang.String" itemvalue="torch" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="datasets" />
            <item index="9" class="java.lang.String" itemvalue="tqdm" />
            <item index="10" class="java.lang.String" itemvalue="pandas" />
            <item index="11" class="java.lang.String" itemvalue="openai" />
            <item index="12" class="java.lang.String" itemvalue="wandb" />
            <item index="13" class="java.lang.String" itemvalue="accelerate" />
            <item index="14" class="java.lang.String" itemvalue="huggingface_hub" />
            <item index="15" class="java.lang.String" itemvalue="torchvision" />
            <item index="16" class="java.lang.String" itemvalue="argparse" />
            <item index="17" class="java.lang.String" itemvalue="ruamel_yaml" />
            <item index="18" class="java.lang.String" itemvalue="timm" />
            <item index="19" class="java.lang.String" itemvalue="httpx" />
            <item index="20" class="java.lang.String" itemvalue="tiktoken" />
            <item index="21" class="java.lang.String" itemvalue="vllm" />
            <item index="22" class="java.lang.String" itemvalue="joblib" />
            <item index="23" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="24" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="25" class="java.lang.String" itemvalue="xformers" />
            <item index="26" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="27" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="28" class="java.lang.String" itemvalue="frozenlist" />
            <item index="29" class="java.lang.String" itemvalue="fsspec" />
            <item index="30" class="java.lang.String" itemvalue="appdirs" />
            <item index="31" class="java.lang.String" itemvalue="filelock" />
            <item index="32" class="java.lang.String" itemvalue="Pygments" />
            <item index="33" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="34" class="java.lang.String" itemvalue="lit" />
            <item index="35" class="java.lang.String" itemvalue="safetensors" />
            <item index="36" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="37" class="java.lang.String" itemvalue="starlette" />
            <item index="38" class="java.lang.String" itemvalue="certifi" />
            <item index="39" class="java.lang.String" itemvalue="anyio" />
            <item index="40" class="java.lang.String" itemvalue="multiprocess" />
            <item index="41" class="java.lang.String" itemvalue="soupsieve" />
            <item index="42" class="java.lang.String" itemvalue="shortuuid" />
            <item index="43" class="java.lang.String" itemvalue="uvicorn" />
            <item index="44" class="java.lang.String" itemvalue="pyparsing" />
            <item index="45" class="java.lang.String" itemvalue="jsonschema" />
            <item index="46" class="java.lang.String" itemvalue="xxhash" />
            <item index="47" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="48" class="java.lang.String" itemvalue="GitPython" />
            <item index="49" class="java.lang.String" itemvalue="pydantic" />
            <item index="50" class="java.lang.String" itemvalue="orjson" />
            <item index="51" class="java.lang.String" itemvalue="wordcloud" />
            <item index="52" class="java.lang.String" itemvalue="ray" />
            <item index="53" class="java.lang.String" itemvalue="altair" />
            <item index="54" class="java.lang.String" itemvalue="attrs" />
            <item index="55" class="java.lang.String" itemvalue="contourpy" />
            <item index="56" class="java.lang.String" itemvalue="psutil" />
            <item index="57" class="java.lang.String" itemvalue="fonttools" />
            <item index="58" class="java.lang.String" itemvalue="regex" />
            <item index="59" class="java.lang.String" itemvalue="PySocks" />
            <item index="60" class="java.lang.String" itemvalue="ninja" />
            <item index="61" class="java.lang.String" itemvalue="matplotlib" />
            <item index="62" class="java.lang.String" itemvalue="peft" />
            <item index="63" class="java.lang.String" itemvalue="nh3" />
            <item index="64" class="java.lang.String" itemvalue="ffmpy" />
            <item index="65" class="java.lang.String" itemvalue="msgpack" />
            <item index="66" class="java.lang.String" itemvalue="httpcore" />
            <item index="67" class="java.lang.String" itemvalue="idna" />
            <item index="68" class="java.lang.String" itemvalue="referencing" />
            <item index="69" class="java.lang.String" itemvalue="networkx" />
            <item index="70" class="java.lang.String" itemvalue="async-timeout" />
            <item index="71" class="java.lang.String" itemvalue="wcwidth" />
            <item index="72" class="java.lang.String" itemvalue="Jinja2" />
            <item index="73" class="java.lang.String" itemvalue="sniffio" />
            <item index="74" class="java.lang.String" itemvalue="jsonschema-specifications" />
            <item index="75" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="76" class="java.lang.String" itemvalue="rpds-py" />
            <item index="77" class="java.lang.String" itemvalue="gdown" />
            <item index="78" class="java.lang.String" itemvalue="seaborn" />
            <item index="79" class="java.lang.String" itemvalue="prompt-toolkit" />
            <item index="80" class="java.lang.String" itemvalue="nlpaug" />
            <item index="81" class="java.lang.String" itemvalue="urllib3" />
            <item index="82" class="java.lang.String" itemvalue="pyarrow" />
            <item index="83" class="java.lang.String" itemvalue="scipy" />
            <item index="84" class="java.lang.String" itemvalue="markdown2" />
            <item index="85" class="java.lang.String" itemvalue="tzdata" />
            <item index="86" class="java.lang.String" itemvalue="rich" />
            <item index="87" class="java.lang.String" itemvalue="dill" />
            <item index="88" class="java.lang.String" itemvalue="packaging" />
            <item index="89" class="java.lang.String" itemvalue="python-multipart" />
            <item index="90" class="java.lang.String" itemvalue="fastapi" />
            <item index="91" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="92" class="java.lang.String" itemvalue="pathtools" />
            <item index="93" class="java.lang.String" itemvalue="toolz" />
            <item index="94" class="java.lang.String" itemvalue="cmake" />
            <item index="95" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="96" class="java.lang.String" itemvalue="aiohttp" />
            <item index="97" class="java.lang.String" itemvalue="multidict" />
            <item index="98" class="java.lang.String" itemvalue="yarl" />
            <item index="99" class="java.lang.String" itemvalue="pytz" />
            <item index="100" class="java.lang.String" itemvalue="einops" />
            <item index="101" class="java.lang.String" itemvalue="aiosignal" />
            <item index="102" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>