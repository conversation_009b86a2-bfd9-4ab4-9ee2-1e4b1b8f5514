"""
生成代码处理结果的详细总结报告
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class SummaryReportGenerator:
    """总结报告生成器"""
    
    def __init__(self, processed_data_dir: str):
        self.processed_data_dir = Path(processed_data_dir)
    
    def load_analysis_data(self, repo_name: str) -> Dict[str, Any]:
        """加载分析数据"""
        repo_dir = self.processed_data_dir / repo_name
        
        data = {}
        
        # 加载各种分析结果
        files_to_load = {
            'statistics': 'statistics.json',
            'knowledge_graph': 'knowledge_graph.json',
            'rag_chunks': 'rag_chunks.json'
        }
        
        for key, filename in files_to_load.items():
            file_path = repo_dir / filename
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    data[key] = json.load(f)
        
        return data
    
    def generate_detailed_report(self, repo_name: str) -> str:
        """生成详细报告"""
        data = self.load_analysis_data(repo_name)
        
        if not data:
            return f"未找到 {repo_name} 的分析数据"
        
        report_lines = []
        
        # 标题
        report_lines.extend([
            "=" * 80,
            f"代码仓库分析详细报告: {repo_name}",
            "=" * 80,
            ""
        ])
        
        # 基本统计信息
        if 'statistics' in data:
            stats = data['statistics']
            repo_info = stats.get('repository_info', {})
            element_stats = stats.get('element_statistics', {})
            rel_stats = stats.get('relationship_statistics', {})
            
            report_lines.extend([
                "📊 基本统计信息",
                "-" * 40,
                f"处理时间: {repo_info.get('processed_at', 'N/A')}",
                f"仓库路径: {repo_info.get('path', 'N/A')}",
                "",
                "代码元素统计:",
                f"  📁 总文件数: {element_stats.get('total_files', 0)}",
                f"  🧩 总代码元素数: {element_stats.get('total_elements', 0)}",
                f"  📦 模块数: {element_stats.get('modules', 0)}",
                f"  🏛️  类数: {element_stats.get('classes', 0)}",
                f"  ⚙️  函数数: {element_stats.get('functions', 0)}",
                f"  📝 变量数: {element_stats.get('variables', 0)}",
                "",
                "关系统计:",
            ])
            
            for rel_type, count in rel_stats.items():
                emoji = self._get_relationship_emoji(rel_type)
                report_lines.append(f"  {emoji} {rel_type}: {count}")
            
            report_lines.append("")
        
        # 知识图谱分析
        if 'knowledge_graph' in data:
            kg = data['knowledge_graph']
            nodes = kg.get('nodes', [])
            edges = kg.get('edges', [])
            
            report_lines.extend([
                "🕸️  知识图谱分析",
                "-" * 40,
                f"节点数: {len(nodes)}",
                f"边数: {len(edges)}",
                ""
            ])
            
            # 节点类型分布
            node_types = {}
            for node in nodes:
                node_type = node.get('type', 'Unknown')
                node_types[node_type] = node_types.get(node_type, 0) + 1
            
            report_lines.append("节点类型分布:")
            for node_type, count in sorted(node_types.items()):
                report_lines.append(f"  {node_type}: {count}")
            
            report_lines.append("")
            
            # 边类型分布
            edge_types = {}
            for edge in edges:
                edge_type = edge.get('type', 'Unknown')
                edge_types[edge_type] = edge_types.get(edge_type, 0) + 1
            
            report_lines.append("边类型分布:")
            for edge_type, count in sorted(edge_types.items()):
                report_lines.append(f"  {edge_type}: {count}")
            
            report_lines.append("")
        
        # RAG数据分析
        if 'rag_chunks' in data:
            rag_data = data['rag_chunks']
            chunks = rag_data.get('chunks', [])
            chunk_types = rag_data.get('chunk_types', {})
            
            report_lines.extend([
                "🤖 RAG数据分析",
                "-" * 40,
                f"总数据块数: {len(chunks)}",
                "",
                "数据块类型分布:",
            ])
            
            for chunk_type, count in chunk_types.items():
                report_lines.append(f"  {chunk_type}: {count}")
            
            report_lines.append("")
            
            # 分析数据块内容长度分布
            content_lengths = [len(chunk.get('content', '')) for chunk in chunks]
            if content_lengths:
                avg_length = sum(content_lengths) / len(content_lengths)
                max_length = max(content_lengths)
                min_length = min(content_lengths)
                
                report_lines.extend([
                    "数据块内容长度统计:",
                    f"  平均长度: {avg_length:.0f} 字符",
                    f"  最大长度: {max_length} 字符",
                    f"  最小长度: {min_length} 字符",
                    ""
                ])
        
        # 主要代码元素
        if 'knowledge_graph' in data:
            kg = data['knowledge_graph']
            nodes = kg.get('nodes', [])
            
            # 提取主要类
            classes = [node for node in nodes if node.get('type') == 'Class']
            functions = [node for node in nodes if node.get('type') == 'Function']
            
            if classes:
                report_lines.extend([
                    "🏛️  主要类",
                    "-" * 40,
                ])
                
                for cls in classes[:10]:  # 显示前10个类
                    cls_name = cls.get('id', 'Unknown')
                    file_name = os.path.basename(cls.get('file_path', ''))
                    report_lines.append(f"  • {cls_name} ({file_name})")
                
                if len(classes) > 10:
                    report_lines.append(f"  ... 还有 {len(classes) - 10} 个类")
                
                report_lines.append("")
            
            if functions:
                report_lines.extend([
                    "⚙️  主要函数",
                    "-" * 40,
                ])
                
                # 过滤掉特殊方法，显示主要函数
                main_functions = [f for f in functions if not f.get('id', '').split('.')[-1].startswith('__')]
                
                for func in main_functions[:10]:  # 显示前10个函数
                    func_name = func.get('id', 'Unknown')
                    file_name = os.path.basename(func.get('file_path', ''))
                    
                    # 检查是否有参数信息
                    metadata = func.get('metadata', {})
                    args = metadata.get('arguments', [])
                    if args and len(args) > 1:  # 排除只有self的方法
                        args_str = f"({', '.join(args[:3])}{'...' if len(args) > 3 else ''})"
                    else:
                        args_str = "()"
                    
                    report_lines.append(f"  • {func_name}{args_str} ({file_name})")
                
                if len(main_functions) > 10:
                    report_lines.append(f"  ... 还有 {len(main_functions) - 10} 个函数")
                
                report_lines.append("")
        
        # 文件分析
        if 'knowledge_graph' in data:
            kg = data['knowledge_graph']
            nodes = kg.get('nodes', [])
            
            # 按文件分组统计
            file_stats = {}
            for node in nodes:
                file_path = node.get('file_path', '')
                if file_path:
                    file_name = os.path.basename(file_path)
                    if file_name not in file_stats:
                        file_stats[file_name] = {'classes': 0, 'functions': 0, 'variables': 0}
                    
                    node_type = node.get('type', '')
                    if node_type == 'Class':
                        file_stats[file_name]['classes'] += 1
                    elif node_type == 'Function':
                        file_stats[file_name]['functions'] += 1
                    elif node_type == 'Variable':
                        file_stats[file_name]['variables'] += 1
            
            if file_stats:
                report_lines.extend([
                    "📁 文件分析",
                    "-" * 40,
                ])
                
                for file_name, stats in sorted(file_stats.items()):
                    total = stats['classes'] + stats['functions'] + stats['variables']
                    report_lines.append(
                        f"  • {file_name}: {total} 元素 "
                        f"(类:{stats['classes']}, 函数:{stats['functions']}, 变量:{stats['variables']})"
                    )
                
                report_lines.append("")
        
        # 复杂度分析
        report_lines.extend([
            "📈 复杂度分析",
            "-" * 40,
        ])
        
        if 'statistics' in data:
            element_stats = data['statistics'].get('element_statistics', {})
            total_files = element_stats.get('total_files', 1)
            total_elements = element_stats.get('total_elements', 0)
            
            avg_elements_per_file = total_elements / total_files if total_files > 0 else 0
            
            report_lines.extend([
                f"平均每个文件的元素数: {avg_elements_per_file:.1f}",
                f"代码密度评估: {self._assess_code_density(avg_elements_per_file)}",
            ])
        
        if 'knowledge_graph' in data:
            kg = data['knowledge_graph']
            edges = kg.get('edges', [])
            nodes = kg.get('nodes', [])
            
            if nodes:
                avg_connections = len(edges) / len(nodes) if nodes else 0
                report_lines.extend([
                    f"平均每个元素的连接数: {avg_connections:.1f}",
                    f"代码耦合度评估: {self._assess_coupling(avg_connections)}",
                ])
        
        report_lines.extend([
            "",
            "=" * 80,
            f"报告生成完成 - {repo_name}",
            "=" * 80
        ])
        
        return "\n".join(report_lines)
    
    def _get_relationship_emoji(self, rel_type: str) -> str:
        """获取关系类型对应的emoji"""
        emoji_map = {
            'imports': '📥',
            'imports_from': '📤',
            'inherits_from': '🧬',
            'has_member': '👥',
            'member_of': '🏠',
            'calls': '📞',
            'uses_variable': '📝',
            'similar_to': '🔄',
            'instantiates': '🏗️'
        }
        return emoji_map.get(rel_type, '🔗')
    
    def _assess_code_density(self, avg_elements: float) -> str:
        """评估代码密度"""
        if avg_elements < 10:
            return "低密度 (简单结构)"
        elif avg_elements < 30:
            return "中等密度 (适中结构)"
        elif avg_elements < 50:
            return "高密度 (复杂结构)"
        else:
            return "极高密度 (非常复杂)"
    
    def _assess_coupling(self, avg_connections: float) -> str:
        """评估代码耦合度"""
        if avg_connections < 2:
            return "低耦合 (松散结构)"
        elif avg_connections < 5:
            return "中等耦合 (适中结构)"
        elif avg_connections < 10:
            return "高耦合 (紧密结构)"
        else:
            return "极高耦合 (强依赖结构)"


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生成代码分析总结报告")
    parser.add_argument("--processed-dir", default="../ProcressedData/code_analysis", 
                       help="处理结果目录")
    parser.add_argument("--repo-name", default="EMCGCN-ASTE", help="仓库名称")
    parser.add_argument("--output-file", help="输出文件路径（可选）")
    
    args = parser.parse_args()
    
    # 生成报告
    generator = SummaryReportGenerator(args.processed_dir)
    report = generator.generate_detailed_report(args.repo_name)
    
    # 输出报告
    if args.output_file:
        with open(args.output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {args.output_file}")
    else:
        print(report)


if __name__ == "__main__":
    main()
