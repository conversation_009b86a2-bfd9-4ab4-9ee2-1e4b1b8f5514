"""
CodeProcessor配置文件
"""

import os
from pathlib import Path

# 基础路径配置
BASE_DIR = Path(__file__).parent.parent
DATA_DIR = BASE_DIR / "data"
PROCESSED_DATA_DIR = BASE_DIR / "ProcressedData" / "code_analysis"
REPO_DIR = DATA_DIR / "repo"

# 支持的文件扩展名
SUPPORTED_EXTENSIONS = {'.py'}

# 忽略的目录和文件
IGNORED_DIRS = {
    '__pycache__',
    '.git',
    '.svn',
    '.hg',
    'node_modules',
    '.venv',
    'venv',
    'env',
    '.env'
}

IGNORED_FILES = {
    '__init__.py',  # 可选：是否忽略__init__.py文件
}

# 代码分析配置
ANALYSIS_CONFIG = {
    # 函数体长度限制（用于RAG）
    'max_function_body_length': 2000,
    
    # 关系分析配置
    'max_relationships_per_element': 10,
    'similarity_threshold': 0.3,
    
    # 上下文提取配置
    'default_context_depth': 2,
    'max_context_elements': 50,
    
    # RAG数据块配置
    'max_chunk_size': 4000,
    'chunk_overlap': 200,
    'max_elements_per_file_chunk': 20,
    'max_elements_per_module_chunk': 20
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_handler': True,
    'console_handler': True
}

# 输出配置
OUTPUT_CONFIG = {
    'save_full_analysis': True,
    'save_rag_data': True,
    'save_knowledge_graph': True,
    'save_statistics': True,
    'save_rag_chunks': True,
    'indent': 2,
    'ensure_ascii': False
}

# 性能配置
PERFORMANCE_CONFIG = {
    'max_workers': 4,  # 并行处理的最大工作线程数
    'batch_size': 100,  # 批处理大小
    'memory_limit_mb': 1024,  # 内存限制（MB）
    'enable_caching': True  # 是否启用缓存
}

# 特定仓库配置
REPO_CONFIGS = {
    'EMCGCN-ASTE': {
        'description': 'EMCGCN-ASTE情感分析项目',
        'main_modules': ['model', 'data', 'utils', 'main'],
        'key_classes': ['EMCGCN', 'DataIterator', 'Metric'],
        'analysis_focus': ['model_architecture', 'data_processing', 'training_loop']
    }
}

def get_repo_config(repo_name: str) -> dict:
    """获取特定仓库的配置"""
    return REPO_CONFIGS.get(repo_name, {})

def get_output_path(repo_name: str, file_type: str = 'base') -> Path:
    """获取输出文件路径"""
    repo_output_dir = PROCESSED_DATA_DIR / repo_name
    
    if file_type == 'base':
        return repo_output_dir
    elif file_type == 'full_analysis':
        return repo_output_dir / 'full_analysis.json'
    elif file_type == 'rag_data':
        return repo_output_dir / 'rag_data.json'
    elif file_type == 'rag_chunks':
        return repo_output_dir / 'rag_chunks.json'
    elif file_type == 'knowledge_graph':
        return repo_output_dir / 'knowledge_graph.json'
    elif file_type == 'statistics':
        return repo_output_dir / 'statistics.json'
    else:
        return repo_output_dir / f'{file_type}.json'

def ensure_output_dirs():
    """确保输出目录存在"""
    PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)

# 初始化
ensure_output_dirs()
