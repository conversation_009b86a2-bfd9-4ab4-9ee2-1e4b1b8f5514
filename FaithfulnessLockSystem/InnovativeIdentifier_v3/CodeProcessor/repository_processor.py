"""
代码仓库处理器主模块
整合代码解析和关系分析，为RAG系统提供结构化的代码数据
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from .code_parser import RepositoryParser
from .relationship_analyzer import RelationshipAnalyzer, CodeContextExtractor

logger = logging.getLogger(__name__)


class RepositoryProcessor:
    """代码仓库处理器"""
    
    def __init__(self, output_dir: str = None):
        self.parser = RepositoryParser()
        self.analyzer = None
        self.context_extractor = None
        self.output_dir = output_dir or "processed_repos"
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def process_repository(self, repo_path: str, repo_name: str = None) -> Dict[str, Any]:
        """处理代码仓库"""
        if not repo_name:
            repo_name = os.path.basename(repo_path)
        
        logger.info(f"开始处理代码仓库: {repo_name}")
        
        # 1. 解析代码结构
        parse_result = self.parser.parse_repository(repo_path)
        
        # 2. 分析代码关系
        self.analyzer = RelationshipAnalyzer(
            parse_result['elements'], 
            parse_result['module_structure']
        )
        relationships = self.analyzer.analyze_all_relationships()
        
        # 3. 构建知识图谱
        knowledge_graph = self.analyzer.build_knowledge_graph()
        
        # 4. 创建上下文提取器
        self.context_extractor = CodeContextExtractor(
            parse_result['elements'], 
            relationships
        )
        
        # 5. 准备RAG数据
        rag_data = self._prepare_rag_data(parse_result['elements'])
        
        # 6. 整合结果
        result = {
            'repository_info': {
                'name': repo_name,
                'path': repo_path,
                'processed_at': datetime.now().isoformat(),
                'statistics': parse_result['statistics']
            },
            'elements': parse_result['elements'],
            'module_structure': parse_result['module_structure'],
            'file_elements': parse_result['file_elements'],
            'relationships': relationships,
            'knowledge_graph': knowledge_graph,
            'rag_data': rag_data
        }
        
        # 7. 保存结果
        self._save_results(repo_name, result)
        
        logger.info(f"代码仓库处理完成: {repo_name}")
        return result
    
    def _prepare_rag_data(self, elements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """为RAG系统准备数据"""
        rag_data = []
        
        for element_name, element_data in elements.items():
            # 为每个代码元素准备RAG条目
            rag_entry = {
                'id': element_name,
                'type': element_data['type'],
                'name': element_data['name'],
                'file_path': element_data['file_path'],
                'content': self._extract_element_content(element_data),
                'embedding_text': self.context_extractor.prepare_for_embedding(element_name) if self.context_extractor else "",
                'metadata': {
                    'start_line': element_data.get('start_line'),
                    'end_line': element_data.get('end_line'),
                    'has_docstring': bool(element_data.get('docstring')),
                    'element_metadata': element_data.get('metadata', {})
                }
            }
            
            rag_data.append(rag_entry)
        
        return rag_data
    
    def _extract_element_content(self, element_data: Dict[str, Any]) -> str:
        """提取代码元素的内容用于RAG"""
        content_parts = []
        
        # 添加定义
        if element_data.get('definition'):
            content_parts.append(f"Definition: {element_data['definition']}")
        
        # 添加文档字符串
        if element_data.get('docstring'):
            content_parts.append(f"Documentation: {element_data['docstring']}")
        
        # 添加函数体（如果不太长）
        if element_data.get('body') and len(element_data['body']) < 1000:
            content_parts.append(f"Implementation: {element_data['body']}")
        
        # 添加关系信息
        if element_data.get('relationships'):
            rel_info = []
            for rel in element_data['relationships']:
                rel_info.append(f"{rel['type']}: {rel['target']}")
            content_parts.append(f"Relationships: {'; '.join(rel_info)}")
        
        return "\n\n".join(content_parts)
    
    def _save_results(self, repo_name: str, result: Dict[str, Any]):
        """保存处理结果"""
        repo_output_dir = os.path.join(self.output_dir, repo_name)
        os.makedirs(repo_output_dir, exist_ok=True)
        
        # 保存完整结果
        with open(os.path.join(repo_output_dir, 'full_analysis.json'), 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        # 保存RAG数据
        with open(os.path.join(repo_output_dir, 'rag_data.json'), 'w', encoding='utf-8') as f:
            json.dump(result['rag_data'], f, indent=2, ensure_ascii=False)
        
        # 保存知识图谱
        with open(os.path.join(repo_output_dir, 'knowledge_graph.json'), 'w', encoding='utf-8') as f:
            json.dump(result['knowledge_graph'], f, indent=2, ensure_ascii=False)
        
        # 保存统计信息
        stats = {
            'repository_info': result['repository_info'],
            'element_statistics': result['repository_info']['statistics'],
            'relationship_statistics': result['knowledge_graph']['statistics']
        }
        with open(os.path.join(repo_output_dir, 'statistics.json'), 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"结果已保存到: {repo_output_dir}")
    
    def get_element_context(self, element_name: str, max_depth: int = 2) -> Dict[str, Any]:
        """获取代码元素的上下文"""
        if not self.context_extractor:
            raise ValueError("Repository not processed yet")
        
        return self.context_extractor.extract_element_context(element_name, max_depth)
    
    def search_elements(self, query: str, element_type: str = None) -> List[Dict[str, Any]]:
        """搜索代码元素"""
        if not hasattr(self.parser, 'elements') or not self.parser.elements:
            raise ValueError("Repository not processed yet")

        results = []
        query_lower = query.lower()

        for element_name, element_obj in self.parser.elements.items():
            element_data = element_obj.to_dict()
            # 类型过滤
            if element_type and element_data['type'] != element_type:
                continue

            # 名称匹配
            if query_lower in element_name.lower():
                results.append({
                    'element_name': element_name,
                    'element_data': element_data,
                    'match_type': 'name'
                })
                continue

            # 文档字符串匹配
            if element_data.get('docstring') and query_lower in element_data['docstring'].lower():
                results.append({
                    'element_name': element_name,
                    'element_data': element_data,
                    'match_type': 'docstring'
                })
                continue

            # 定义匹配
            if element_data.get('definition') and query_lower in element_data['definition'].lower():
                results.append({
                    'element_name': element_name,
                    'element_data': element_data,
                    'match_type': 'definition'
                })
        
        return results
    
    def generate_summary_report(self, repo_name: str) -> str:
        """生成仓库分析摘要报告"""
        if not hasattr(self.parser, 'elements'):
            return "Repository not processed yet"

        # 统计信息
        stats = self.parser.elements
        total_elements = len(stats)

        element_counts = {}
        for element in stats.values():
            element_type = element.element_type
            element_counts[element_type] = element_counts.get(element_type, 0) + 1
        
        # 关系统计
        rel_stats = self.analyzer.get_relationship_statistics() if self.analyzer else {}
        
        # 生成报告
        report_lines = [
            f"# 代码仓库分析报告: {repo_name}",
            f"",
            f"## 基本统计",
            f"- 总代码元素数: {total_elements}",
        ]
        
        for element_type, count in element_counts.items():
            report_lines.append(f"- {element_type}: {count}")
        
        if rel_stats:
            report_lines.extend([
                f"",
                f"## 关系统计",
            ])
            for rel_type, count in rel_stats.items():
                report_lines.append(f"- {rel_type}: {count}")
        
        # 复杂度分析
        report_lines.extend([
            f"",
            f"## 复杂度分析",
            f"- 平均每个文件的元素数: {total_elements / len(self.parser.file_elements) if self.parser.file_elements else 0:.1f}",
        ])
        
        # 主要类和函数
        classes = [name for name, element in stats.items() if element.element_type == "Class"]
        functions = [name for name, element in stats.items() if element.element_type == "Function"]
        
        if classes:
            report_lines.extend([
                f"",
                f"## 主要类 (前5个)",
            ])
            for class_name in classes[:5]:
                report_lines.append(f"- {class_name}")
        
        if functions:
            report_lines.extend([
                f"",
                f"## 主要函数 (前5个)",
            ])
            for func_name in functions[:5]:
                report_lines.append(f"- {func_name}")
        
        return "\n".join(report_lines)


def main():
    """主函数，用于命令行调用"""
    import argparse
    
    parser = argparse.ArgumentParser(description="代码仓库处理器")
    parser.add_argument("repo_path", help="代码仓库路径")
    parser.add_argument("--output-dir", default="processed_repos", help="输出目录")
    parser.add_argument("--repo-name", help="仓库名称")
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 处理仓库
    processor = RepositoryProcessor(args.output_dir)
    result = processor.process_repository(args.repo_path, args.repo_name)
    
    # 生成报告
    repo_name = args.repo_name or os.path.basename(args.repo_path)
    report = processor.generate_summary_report(repo_name)
    print(report)


if __name__ == "__main__":
    main()
