# Enhanced Multi-Channel Graph Convolutional Network for Aspect Sentiment Triplet Extraction

Ha<PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> of Artificial Intelligence, Beijing University of Posts and Telecommunications, China {ccchenhao997， zepeng， fxfeng， rfli，xjwang}@bupt.edu.cn

# Abstract

Aspect Sentiment Triplet Extraction (ASTE) is an emerging sentiment analysis task.Most of the existing studies focus on devising a new tagging scheme that enables the model to extract the sentiment triplets in an end-to-end fashion. However, these methods ignore the relations between words for ASTE task.In this paper,weproposean Enhanced Multi-Channel Graph Convolutional Network model (EMCGCN) to fully utilize the relations between words.Specifically,we first define ten types of relations for ASTE task,and then adopt a biaffine attention module to embed these relations as an adjacent tensor between words in a sentence.After that, our EMC-GCN transforms the sentence into a multi-channel graph by treating words and the relation adjacent tensor as nodes and edges,respectively. Thus,relationaware node representations can be learnt. Furthermore,we consider diverse linguistic features to enhance our EMC-GCN model.Finally, we design an effective refining strategy on EMC-GCN for word-pair representation refinement,which considers the implicit results of aspect and opinion extraction when determining whether word pairs match or not. Extensive experimental results on the benchmark datsets demonstrate that the effectiveness and robustness of our proposed model, which outperforms state-of-the-art methods significantly.l

# 1Introduction

Aspect Sentiment Triplet Extraction (ASTE) is a new variant of Aspect-based Sentiment Analysis (ABSA). The ASTE task aims to extract aspect sentiment triplets from a sentence,and each triplet contains three elements,namely aspect term,opinion term and their associated sentiment. In Figure 1, an example illustrates the definition of ASTE.

![](images/1eec104cbd0e27b2dcfe2a146232f31e7861c93e73c0d35f64df3c97cfed5b24.jpg)

> **Picture description**: The image is an example illustrating the Aspect Sentiment Triplet Extraction (ASTE) task. It includes a sentence with its dependency tree and highlights various components needed for extracting sentiment triplets.

### Sentence Structure:
- **Sentence:** "The gourmet food is delicious but the service is poor."
- **Dependency Tree Structure:**
  - "The" (DT) determines "gourmet" (NN), which forms a compound with "food" (NN).
  - "Food" (NN) is the nominal subject (nsubj) of "delicious" (JJ).
  - "Delicious" (JJ) is connected with "is" (VBZ), acting as a copula.
  - Coordinating conjunction (conj) "but" links the phrases.
  - "Service" (NN) is another nominal subject (nsubj) linked to "poor" (JJ) through "is" (VBZ), another copula.

### Terms and Triplets:
- **Aspect Terms:** {gourmet food, service}
- **Opinion Terms:** {delicious, poor}
- **Triplets:** 
  - (gourmet food, delicious, positive)
  - (service, poor, negative)

The image visually organizes the sentence's syntactical relations using lines and labels to show how the aspect and opinion terms are related, as well as to indicate the sentiment polarity.
  
Figure 1: A sentence with its dependency tree is given to illustrate ASTE task. In the triplet set,aspect terms, opinion terms are highlighted in blue and yellow,respectively. The positive sentiment polarity is highlighted in red, while the negative in green.

To extract the triplets, previous studies have developed three types of approaches. Pipeline approaches (Peng et al., 2O2O) independently extract elements of the triplet. However, such techniques ignore the interaction between them,and potentially lead to error propagation and extra costs.To utilize the associations among the multiple subtasks, Mao et al. (2021) and Chen et al. (2021a) formulate the ASTE task as a multi-turn machine reading comprehension (MRC) problem and design a model based on BERT to jointly train multiple subtasks.Meanwhile, some efforts devote to extracting the triplets in an end-to-end framework (Xu et al., 2020; Wu et al., 2020a; Zhang et al., 2020; Chen et al., 2021b; Yan et al., 2021), which is constructed mainly by designing new tagging scheme. Although previous works have achieved significant fruits, there exists still several challenges.

Here, two questions arise naturally for ASTE task by our observations. 1) How to utilize various relations between words to help ASTE task? Take Figure 1 as an example; for word pair ("gourmet"，“food"),“gourmet” and“food” belong to the same aspect term “gourmet food". Likewise,for word pair("food",“delicious"), “food" is an opinion target of “delicious”and is endowed with a positive sentiment polarity. Therefore, to effectively extract the aspect term “gourmet food", we expect that “gourmet” can obtain the information of “food” and vice versa. To judge the sentiment polarity of the aspect term, information of the opinion term“delicious”should be delivered to “gourmet food". In short, we need to learn task-dependent word representations based on the relations between words. 2) How to utilize the linguistic features to help ASTE task? First, we observe that aspect terms “gourmet food" and “service”are nouns,while opinion terms“delicious” and “poor”are adjectives. Thus, the word pair composed of a noun and an adjective tend to form aspect-opinion pair. Second, from the syntactic dependency tree in Figure 1, different dependency types exist in word pairs. For instance,“gourmet” and “food” comprise a compound noun because the dependency type between them is “compound", while“food”is the nominal subject of “delicious” due to the type “nsubj". Thus, these dependency types can help not only the extraction of aspect and opinion terms but also their matching ². In addition, we consider the tree-based and relative position distances which describe the relevance of two words.

In this paper, we propose a novel architecture, Enhanced Multi-Channel Graph Convolutional Network model (EMC-GCN), to answer the aforementioned questions.Firstly,we utilize a biaffine attention module to model the relation probability distribution between words in a sentence and use a vector to represent it. Each dimension in the vector corresponds to a certain relation type. To this end, we can derive a relation adjacency tensor from a sentence.Furthermore,our EMC-GCN transforms the sentence to a multi-channel graph by treating words and the relation adjacency tensor as nodes and edges,respectively. In order to learn precise relation between words,we impose relation constraint on the relation adjacency tensor. Secondly, to exploit linguistic features, including lexical and syntactic information,we obtain the part-of-speech combination, syntactic dependency type, tree-based distance and relative position distance of each word pair in the sentence. Similarly, we respectively transform these features into the edges for the multi-channel graphs to further enhance our model. Although part of linguistic features has been applied in other tasks (Kouloumpis et al., 2011; Sun et al., 2019; Phan and Ogunbona, 2020; Li et al., 2021), to the best of our knowledge, theyare rarely used in ASTE task.It is non-trivial to explore various linguistic features,adapt and apply them to ASTE in a novel way. Thirdly, inspired by the classifier chains method (Read et al., 2011) in multi-label classification task, we devise an effective refining strategy. Our strategy considers the implicit results of aspect and opinion extraction for word-pair representation refinement when judging whether word pairs match.

Our contributions are highlighted as follows:

1) We propose a novel EMC-GCN model for ASTE task.EMC-GCN exploits the multi-channel graph to encode relations between words. Convolution function over the multi-channel graph is applied to learn relation-aware node representations.

2) We propose a novel way to fully develop linguistic features to enhance our GCN-based model, including the part-of-speech combination, syntactic dependency type, tree-based distance and relative position distance of each word pair in a sentence.

3) We propose an effective refining strategy for refined word-pair representation. It considers the implicit results of aspect and opinion extraction when detecting if word pairs match.

4) We conduct extensive experiments on benchmark datasets. The experimental results show the effectiveness of our EMC-GCN model.

# 2Related Work

Traditional sentiment analysis tasks are sentencelevel (Yang and Cardie, 2O14; Severyn and Moschitti, 2015) or document-level (Dou, 2017; Lyu et al., 2020) oriented. In contrast,Aspect-based Sentiment Analysis (ABSA) is an aspect or entity oriented fine-grained sentiment analysis task. The most three basic subtasks are Aspect Term Extraction (ATE) (Hu and Liu, 2OO4; Yin et al., 2016; Xu et al., 2018; Ma et al., 2019; Chen and Qian, 2020; Wei et al., 202O), Aspect Sentiment Classification (ASC) (Tang et al., 2016; Ma et al., 2017; Li et al., 2018; Zhang et al., 2019; Wang et al.. 2020; Li et al., 2021) and Opinion Term Extraction(OTE)(Yang and Cardie,2012,2013;Fan et al., 2019; Wu et al., 2020b). The studies solve these tasks separately and ignore the dependency between these subtasks.Therefore,some efforts devoted to couple the two subtasks and proposed effective models to jointly extract aspect-based pairs.

![](images/e372e1f2ac152731f346dc8d6ad64a589ae12ca30623b4e0ea8d39c2f767b8d7.jpg)

> **Picture description**: The figure illustrates the architecture of the Enhanced Multi-Channel Graph Convolutional Network (EMC-GCN) model for Aspect Sentiment Triplet Extraction (ASTE).

1. **Input and Encoding Layer**:
   - **BERT Encoder**: 
     - Takes input sentence tokens (e.g., $w_1$: "The", $w_2$: "gourmet", $w_3$: "food", $w_4$: "is", $w_5$: "delicious") and outputs contextual representations ($h_1, h_2, ..., h_5$).

2. **Biaffine Attention and Linguistic Features**:
   - **Biaffine Attention Module**: Captures relation probability distributions between word pairs.
   - **Linguistic Features**: 
     - Includes Part-of-Speech Combination ($R^{psc}$), Syntactic Dependency Type ($R^{dep}$), Tree-based Distance ($R^{tbd}$), and Relative Position Distance ($R^{rpd}$) tensors.
   - Relations are encoded as adjacency tensors with dimension $m$.

3. **Multi-Channel GCN Layer**:
   - Utilizes adjacency tensors to capture various relations between words.
   - Outputs node representations that combine information across different channels.

4. **Refining Strategy**:
   - Further processes node representations to refine word-pair representations.

5. **Prediction Layer**:
   - Uses decoded word-pair representations in a table ($s_{11}, s_{12}, ..., s_{55}$) to predict labels.

6. **Triplet Decoding**:
   - Utilizes predicted relations to decode aspect sentiment triplets.
   - Example output: (“gourmet food”, “delicious”, positive).

7. **Relation Constraints**:
   - Imposes constraints to ensure adjacency tensor precision, calculated via cross-entropy loss.

8. **Output**:
   - Defines a triplet set (e.g., {gourmet food, delicious, positive}) illustrating the relation between aspect terms, opinion terms, and their sentiment.

By integrating these components, the EMC-GCN model effectively extracts sentiment triplets by leveraging word relations and linguistic features.
  
Figure 2: The overall architecture of our end-to-end model EMC-GCN.

This kind of work mainly has two tasks: Aspect and Opinion Term Co-Extraction (AOTE) (Wang et al., 2016, 2017; Dai and Song, 2019; Wang and Pan, 2019; Chen et al., 2020b; Wu et al., 2020a) and Aspect-Sentiment Pair Extraction (ASPE) (Ma et al., 2018; Li et al., 2019a,b; He et al., 2019).

Most recently, Peng et al. (2020) first proposed the ASTE task and developed a two-stage pipeline framework to couple together aspect extraction, aspect sentiment classification and opinion extraction. To further explore this task,(Mao et al., 2021; Chen et al., 2021a) transformed ASTE to a machine reading comprehension problem and utilized the shared BERT encoder to obatin the triplets after multiple stages decoding. Another line of research focuses on designing a new tagging scheme that makes the model can extract the triplets in an endto-end fashion (Xu et al., 202O; Wu et al., 2020a; Zhang et al., 2020; Xu et al., 2021; Yan et al., 2021). For instance, Xu et al. (202O) proposed a positionaware tagging scheme,which solves the limitations related to existing works by enriching the expressiveness of labels. Wu et al. (2020a) proposed a grid tagging scheme, similar to table filling (Miwa and Sasaki, 2014; Gupta et al., 2016), to solve this task in an end-to-end manner. Yan et al. (2021) converted ASTE task into a generative formulation. However, these approaches generally ignore the relations between words and linguistic features which effectively promote the triplet extraction.

# 3Proposed Framework

In this section,we elaborate on the details of EMCGCN.The overview of the EMC-GCN framework

<table><tr><td>#</td><td>Relation</td><td>Meaning</td></tr><tr><td>1</td><td>B-A</td><td>beginning of aspect term.</td></tr><tr><td>2</td><td>I-A</td><td>inside of aspect term.</td></tr><tr><td>3</td><td>A</td><td>word pair (wi, wj) belongs to the same aspect term.</td></tr><tr><td>4</td><td>B-0</td><td>beginning of opinion term.</td></tr><tr><td>5</td><td>I-0</td><td>inside of opinion term.</td></tr><tr><td>6</td><td>0</td><td>word pair (wi,wj) belongs to the same opinion term.</td></tr><tr><td>7</td><td>POS</td><td>Wi and wj of the word pair (wi,wj) respectively belong to</td></tr><tr><td>8</td><td>NEU</td><td>an aspect term and an opinion term,and they form aspect-</td></tr><tr><td>9</td><td>NEG</td><td>opinion pair with positive/neutral/negative sentiment.</td></tr><tr><td>10</td><td></td><td>no above relations between word pair (w, wj).</td></tr></table>

Table 1: The meanings of our defined ten relations.Note that these relations can also be seen as labels.

is shown in Figure 2.

# 3.1Problem Formulation

Given an input sentence $X = \{ w _ { 1 } , w _ { 2 } , \cdot \cdot \cdot , w _ { n } \}$ with $n$ words, the goal of our model is to output a set of triplets $\mathcal { T } = \{ ( a , o , s ) _ { m } \} _ { m = 1 } ^ { | \mathcal { T } | }$ from the sentence $X$ ,where $a$ and $o$ denote aspect term and opinion term, respectively. The sentiment polarity $s$ of the given aspect belongs to a sentiment label set $\mathcal { S } = \{ P O S , N E U , N E G \}$ . That is, the sentiment label set comprises of three sentiment polarities: positive, neutral and negative. The sentence $X$ has a total number of $| \tau |$ triplets.

# 3.2Relation Definition and Table Filling

We define ten types of relations between words in a sentence for ASTE.These relations are shown in Table 1． Specifically, four relations or labels, $\{ B { - } A , I { - } A , B { - } O , I { - } O \}$ aim to extract aspect terms and opinion terms. Compared with GTS (Wu et al., 2020a), the relations we defined introduce more accurately boundary information into our model. The

Figure 3: Table filling for triplet extraction in a sentence is illustrated. Each cell denotes a word pair with a relation or label.Refer Table 1 for definitions of relations.   

<table><tr><td colspan="6">The</td><td colspan="2">the</td><td colspan="2">i service</td><td>poor</td></tr><tr><td>The</td><td></td><td></td><td>」 gourmet food</td><td>」</td><td>↓ delicious</td><td>but</td><td></td><td></td><td></td><td>」</td></tr><tr><td>gourmet</td><td></td><td>B-A</td><td>A</td><td></td><td>⊥POS</td><td>上</td><td></td><td></td><td>↓</td><td></td></tr><tr><td>food</td><td>↓</td><td>A</td><td>I-A</td><td>↓</td><td>POS</td><td>」</td><td>上</td><td></td><td></td><td></td></tr><tr><td>is</td><td></td><td></td><td>↓</td><td></td><td></td><td>↓</td><td>上</td><td>1</td><td>1</td><td>1</td></tr><tr><td>delicious</td><td></td><td>⊥POS POS⊥B-O</td><td></td><td></td><td></td><td></td><td>人</td><td></td><td>Y</td><td></td></tr><tr><td>but</td><td></td><td>↓</td><td>↓</td><td>1</td><td>上</td><td></td><td>上</td><td>↓</td><td></td><td>↓</td></tr><tr><td>the</td><td></td><td>↓</td><td></td><td>1</td><td>↓</td><td>↓</td><td>上</td><td></td><td></td><td>↓</td></tr><tr><td>service</td><td></td><td></td><td>1</td><td>1</td><td>1</td><td></td><td>↓</td><td>B-A</td><td>↓</td><td>NEG</td></tr><tr><td>is</td><td></td><td></td><td></td><td>1</td><td>↓</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>poor</td><td></td><td></td><td>上</td><td></td><td>↓</td><td></td><td></td><td>⊥NEG</td><td>」</td><td>B-O</td></tr></table>

$B$ and $I$ denote the beginning of and inside of the term respectively, while $- A$ and - $o$ subtags aim to determine the role of the term, i.e.,an aspect or an opinion. The $A$ and $o$ relations in Table1 are used to detect whether the word pair formed by two different words belongs to the same aspect or opinion term, respectively. The goal of the three sentiment relations $\{ P O S , N E U , N E G \}$ is not only to detect whether a word-pair matches or not, but also judge the sentiment polarity of the aspect-opinion pair. Thus,we can construct a relation table for each labelled sentence with table filling method (Miwa and Sasaki, 2014; Gupta et al., 2016). In Figure 3, we show the word pairs and their relations in an example sentence.Here, each cell corresponds to a word pair with a relation.

# 3.3Triplet Decoding

The decoding details of the ASTE task are shown in Algorithm 1. For simplicity, we use the upper triangular table to decode triplets.Firstly,we use the predicted relations of all word pairs $( w _ { i } , w _ { i } )$ only based on the main diagonal, to extract aspect terms and opinion terms. Secondly, we need to judge whether the extracted aspect terms and opinion terms match. Particularly, for an aspect term $a$ and an opinion term $o$ , we count predicted relations of all word pairs $( w _ { i } , w _ { j } )$ ,where $w _ { i } \in a$ and $w _ { j } \in o$ If there exists any sentiment relation in predicted relations, the aspect term and the opinion term are considered to be paired, otherwise these two are not paired. Finally, for judging the sentiment polarity of the aspect-opinion pair, the most predicted

# Algorithm 1 Triplet Decoding for ASTE

Input: The predicted results $\mathbf { P }$ of a sentence $X$ with length $n$ ， $\mathbf P ( w _ { i } , w _ { j } )$ denotes the predicted label of the word pair $( w _ { i } , w _ { j } )$ ：   
Output: Triplets $\tau$ of the given sentence.   
1: Initialize $\mathcal { D } = [ ] , \mathcal { A } = \{ \} , \mathcal { O } = \{ \} , \mathcal { T } = \{ \}$   
2:while $i \leq n$ do 3: $\mathcal { D }$ .appenc $| ( \mathbf { P } ( w _ { i } , w _ { i } ) ) , i  i + 1$ 4: end while 5: $\mathcal { A }  \mathrm { G e t A s p e c t } ( \mathcal { D } )$ $\mathcal { O }  \mathrm { G e t O p i n i o n } ( \mathcal { D } )$ 6: while $a \in { \mathcal { A } }$ and $o \in \mathcal { O }$ do   
7: ${ \mathcal { S } } = \{ \}$ 8: while $w _ { i } \in a$ and $w _ { j } \in o$ do   
9: $i < j$ then $l a b e l = \mathbf { P } ( w _ { i } , w _ { j } )$ else $l a b e l = \mathbf { P } ( w _ { j } , w _ { i } )$   
10: if $l a b e l \in \{ P O S , N E U , N E G \}$ then $S \gets S \cup \{ l a b e l \}$   
11: end while   
12: ${ \mathcal { S } } \neq \{ \}$ （204 then The most counted sentiment label denoted as $s$ ， ${ \mathcal { T } } \gets { \mathcal { T } } \cup \{ a , o , s \}$   
13:end while

sentiment relation $s \in { \mathcal { S } }$ is regarded as sentiment polarity. Thus, we collect a triplet $( a , o , s )$ ：

# 3.4EMC-GCN Model

# 3.4.1 Input and Encoding Layer.

BERT (Devlin et al., 2O19) has demonstrated its effectiveness in various tasks. We utilize BERT as the sentence encoder to extract hidden contextual representations. Given an input sentence $X =$ $\{ w _ { 1 } , w _ { 2 } , . . . , w _ { n } \}$ with $n$ tokens, the encoding layer outputs the hidden representation sequence $H =$ $\{ h _ { 1 } , h _ { 2 } , . . . , h _ { n } \}$ at the last Transformer block.

# 3.4.2Biaffine Attention Module

We utilize a biaffine attention module to capture the relation probability distribution of each word pair in a sentence, since the biaffine attention has been proven effective in syntactic dependency parsing (Dozat and Manning, 2O17). The biaffine attention process is formulated as,

$$
\begin{array} { r l } & { h _ { i } ^ { a } = \mathrm { M L P } _ { a } ( h _ { i } ) } \\ & { h _ { j } ^ { o } = \mathrm { M L P } _ { o } ( h _ { j } ) } \\ & { g _ { i , j } = h _ { i } ^ { a \mathrm { T } } U _ { 1 } h _ { j } ^ { o } + U _ { 2 } \left( h _ { i } ^ { a } \oplus h _ { j } ^ { o } \right) + b } \\ & { r _ { i , j , k } = \frac { \displaystyle \exp { \left( g _ { i , j , k } \right) } } { \displaystyle \sum _ { l = 1 } ^ { m } \exp { \left( g _ { i , j , l } \right) } } } \\ & { R = \mathrm { B i a f f n e } \left( \mathrm { M L P } _ { a } ( H ) , \mathrm { M L P } _ { o } ( H ) \right) } \end{array}
$$

where multi-layer perceptron is used. The score vector $r _ { i , j } \in \mathbb { R } ^ { 1 \times m }$ models relations between $w _ { i }$ and $w _ { j }$ ， $m$ is the number of relation types and $r _ { i , j , k }$ denotes the score of the $k$ -th relation type for word pair (wi, wj). The adjacency tensor R ∈ Rn xn×m models relations between words,and each channel corresponds to a relation type. $U _ { 1 }$ ， $U _ { 2 }$ and $b$ are trainable weights and bias. $\oplus$ denotes concatenation. Eq. (5) collects process of Eqs. (1) to (4).

# 3.4.3Multi-Channel GCN

Motivated by CNN,GCN is an efficient CNN variant that operates directly on graphs (Kipf and Welling, 2O17). A graph contains nodes and edges and GCN applies the convolution operation on those nodes connected directly by edges to aggregate relevant information. Given a sentence with $n$ words,the general approach is to use the syntactic dependency tree to construct an adjacency matrix $A \in \mathbb { R } ^ { n \times n }$ representing a graph for the sentence (Zhang et al., 2019; Sun et al., 2019). The element $A _ { i j }$ denotes the edge of node pair $( w _ { i } , w _ { j } )$ Specifically, $A _ { i j } = 1$ if the $i$ -th node is directly connected to the $j$ -th node, and $A _ { i j } = 0$ otherwise. A few studies (Guo et al., 2019; Chen et al., 2020a; Li et al., 2021) construct soft edges by attention mechanism for graph. The edge of any node pair $( w _ { i } , w _ { j } )$ is a probability that indicates the correlation degree between nodes $w _ { i }$ and $w _ { j }$ ：

To model various relations between words, our EMC-GCN extend the vanilla GCN with a multichannel adjacency tensor $R ^ { b a } \in \mathbb { R } ^ { n \times n \times m }$ which is constructed by the aforementioned biaffine attention module. Each channel of the adjacency tensor represents the modeling of a relation between words defined in Table 1. Then, we utilize a GCN to aggregate information along each channel for each node. We formulate the process as follows,

$$
\begin{array} { r l r } & { } & { \widetilde { H } _ { k } ^ { b a } = \sigma \left( R _ { : , : , k } ^ { b a } H W _ { k } + b _ { k } \right) } \\ & { } & { \hat { H } ^ { b a } = f ( \widetilde { H } _ { 1 } ^ { b a } , \widetilde { H } _ { 2 } ^ { b a } , . . . , \widetilde { H } _ { m } ^ { b a } ) } \end{array}
$$

where $R _ { : , : , k } ^ { b a } \in \mathbb { R } ^ { n \times n }$ denotes the $k$ -th chanel sice of $R ^ { b a }$ ： $W _ { k }$ and $b _ { k }$ are the learnable weight and bias. $\sigma$ is an activation function (e.g., ReLU).An average pooling function $f ( \cdot )$ is applied over the node hidden representations of all channels.

# 3.4.4 Linguistic Features

To enhance our EMC-GCN model, we introduce four types of linguistic features for each word pair,shown in Figure 4, including the part-ofspeech combination, syntactic dependency type, tree-based distance,and relative position distance. For syntactic dependency type, we add a self dependency type for each word pair $( w _ { i } , w _ { i } )$ . In particular,we randomly initialize four adjacency tensors based on these features,namely $R ^ { p s c }$ ， $R ^ { d e p }$ ， $R ^ { t b d }$ and $R ^ { r p d }$ . Take syntactic dependency type feature as an example. If a dependency arc exists between $w _ { i }$ and $w _ { j }$ and the dependency type is nsubj, then $R _ { i , j , : } ^ { d e p }$ isitalbedi looking up a trainable embedding table; otherwise we initialize $R _ { i , j , : } ^ { d e p }$ with an $m$ -dimensional zero vector. Subsequently, the graph convolution operation is repeated using these adjacency tensors to obtain node representations $\hat { H } ^ { p s c }$ ， $\hat { H } ^ { d \dot { e } p }$ ， $\hat { H } ^ { t b d }$ and $\hat { H } ^ { r p d }$ Finally, we respectively apply the average pooling function and concatenation operation to all node representations and all edges formally as,

![](images/a6da0c42d698a4872864fe1b83d6b4f36643a8f4429a259db3d8df3a469112ff.jpg)

> **Picture description**: The image presents a detailed view of how linguistic features are represented for the sentence "The food is delicious" using adjacency matrices for four distinct features. The focus is on Part-of-Speech (POS) combinations, syntactic dependency types, tree-based distances, and relative position distances within the sentence. 

### Part-of-Speech Combination
The table displays pairs of words with their combined POS tags:
- "The" (DT) is paired with "food" (NN), "is" (VBZ), and "delicious" (JJ) in respective rows and columns. 
- Similarly, combinations are shown for other word pairs, like "food" with "is" (NN-VBZ), and so on.

### Syntactic Dependency Type
This section provides syntactic relationships as matrix entries, such as:
- "The" has a 'det' relationship with "food" and itself is denoted as 'self'.
- "food" is a 'nsubj' to "delicious" and 'self' to itself.
- The relationships consider dependencies like 'cop' present between "is" and "delicious". 

### Tree-based Distance
This matrix shows the distance in the syntactic tree:
- "The" and "food" have a distance of 1.
- "The" and "delicious" are separated by a distance of 2.
- Other distances reflect the number of syntactic nodes traversed.

### Relative Position Distance
Displays the linear position difference:
- "The" and "food" have a distance of 1.
- "The" and "delicious" have a distance of 3.
- Relative positions are consistent with their textual order.

These matrices collectively illustrate how linguistic features enhance the relational understanding of words in aspect sentiment triplet extraction, which is crucial for the EMC-GCN model.
  
Figure 4: Four types of features for a sentence.

$$
\begin{array} { l } { { { \bf H } = f \left( \hat { H } ^ { b a } , \hat { H } ^ { p s c } , \hat { H } ^ { d e p } , \hat { H } ^ { t b d } , \hat { H } ^ { r p d } \right) } } \\ { { { \bf R } = R ^ { b a } \oplus R ^ { p s c } \oplus R ^ { d e p } \oplus R ^ { t b d } \oplus R ^ { r p d } } } \end{array}
$$

where $\begin{array} { r l r } { { \bf H } } & { { } = } & { \left\{ { \bf h } _ { 1 } , { \bf h } _ { 2 } , . . . , { \bf h } _ { n } \right\} } \end{array}$ and $\begin{array} { r l } { \mathbf { R } } & { { } = } \end{array}$ $\{ \mathbf { r } _ { 1 , 1 } , \mathbf { r } _ { 1 , 2 } , . . . , \mathbf { r } _ { n , n } \}$ denote node representations and edge representations of word pairs.

# 3.4.5Relation Constraint

In order to precisely capture the relations between words, we impose a constraint on the adjacent tensor obtained from biaffine module, i.e.,

$$
\mathcal { L } _ { b a } = - \sum _ { i } ^ { n } \sum _ { j } ^ { n } \sum _ { c \in \mathcal { C } } \mathbb { I } ( y _ { i j } = c ) \log ( r _ { i , j \mid c } )
$$

where $\mathbb { I } ( \cdot )$ denotes the indicator function, $y _ { i j }$ is the ground truth of word-pair $( w _ { i } , w _ { j } )$ ,and $\mathcal { C }$ denotes the relation set. Likewise, we impose the relation constraint on four adjacent tensors produced by linguistic features. The constraint costs denote as $\mathcal { L } _ { p s c }$ $\mathcal { L } _ { d e p }$ ， $\mathcal { L } _ { t b d }$ and $\mathcal { L } _ { { r p d } }$ ：

# 3.4.6Refining Strategy and Prediction Layer

To obtain the representation of word pair $( w _ { i } , w _ { j } )$ for label prediction,we concatenate their node representations $\mathbf { h } _ { i }$ ， $\mathbf { h } _ { j }$ and their edge representation $\mathbf { r } _ { i j }$ .Moreover,motivated by the classifier chains (Read et al., 2011) method in multi-label classification task, we devise an effective refining strategy, which consider the implicit results of aspect and opinion extraction when judging whether word pairs match. Specifically, assuming that $w _ { i }$ is a word in an aspect term and $w _ { j }$ is a word in an opinion term，word pair $( w _ { i } , w _ { j } )$ is more likely to be predicted as an sentiment relation, i.e., POS, NEU or NEG. Otherwise, they are unlikely to match. Thus, we introduce the $\mathbf { r } _ { i i }$ and $\mathbf { r } _ { j j }$ to refine the representation $s _ { i j }$ of word pair $( w _ { i } , w _ { j } )$ ,i.e.,

$$
s _ { i j } = \mathbf { h } _ { i } \oplus \mathbf { h } _ { j } \oplus \mathbf { r } _ { i j } \oplus \mathbf { r } _ { i i } \oplus \mathbf { r } _ { j j }
$$

Finally, we feed the word pair representation $s _ { i j }$ into a linear layer, followed by a softmax function to produce a label probability distribution $p _ { i j }$ ,i.e.,

$$
p _ { i j } = \mathrm { s o f t m a x } ( W _ { p } s _ { i j } + b _ { p } )
$$

where $W _ { p }$ and $b _ { p }$ are the learnable weight and bias.

# 3.5 Loss Function

Our goal is to minimize the objective function as,

$$
\mathcal { L } = \mathcal { L } _ { p } + \alpha \mathcal { L } _ { b a } + \beta \left( \mathcal { L } _ { p s c } + \mathcal { L } _ { d e p } + \mathcal { L } _ { t b d } + \mathcal { L } _ { r p d } \right)
$$

where coefficients $\alpha$ and $\beta$ are for adjusting the influence of corresponding relation constraint loss. The standard cross-entropy loss ${ \mathcal { L } } _ { p }$ is used for the ASTE task, i.e.,

$$
\mathcal { L } _ { p } = - \sum _ { i } ^ { n } \sum _ { j } ^ { n } \sum _ { c \in \mathcal { C } } \mathbb { I } ( y _ { i j } = c ) \log ( p _ { i , j | c } ) .
$$

# 4Experiments

# 4.1 Datasets

We evaluate our method on two ABSA datasets. Both of them are from the SemEval ABSA Challenges (Pontiki et al., 2014, 2015, 2016). The first dataset $\mathcal { D } _ { 1 } { } ^ { 3 }$ comes from Wu et al. (2020a). The second dataset $\mathcal { D } _ { 2 } { } ^ { 4 }$ is annotated by Xu et al. (2020), which is a corrected version of dataset proposed by Peng et al. (202O). Statistics for these two groups of datasets are shown in Table 2.

Table 2: Statistics for two groups of experiment datasets.   

<table><tr><td rowspan="2" colspan="2">Dataset</td><td colspan="2">14res</td><td colspan="2">14lap</td><td colspan="2">15res</td><td colspan="2">16res</td></tr><tr><td>#S</td><td>#T</td><td>#S</td><td>#T</td><td>#S</td><td>#T</td><td>#S</td><td>#T</td></tr><tr><td rowspan="3">D1</td><td>train</td><td>1,259</td><td>2,356</td><td>899</td><td>1,452</td><td>603</td><td>1,038</td><td>863</td><td>1,421</td></tr><tr><td>dev</td><td>315</td><td>580</td><td>225</td><td>383</td><td>151</td><td>239</td><td>216</td><td>348</td></tr><tr><td>test</td><td>493</td><td>1,008</td><td>332</td><td>547</td><td>325</td><td>493</td><td>328</td><td>525</td></tr><tr><td rowspan="3">D2</td><td>train</td><td>1266</td><td>2338</td><td>906</td><td>1460</td><td>605</td><td>1013</td><td>857</td><td>1394</td></tr><tr><td>dev</td><td>310</td><td>577</td><td>219</td><td>346</td><td>148</td><td>249</td><td>210</td><td>339</td></tr><tr><td>test</td><td>492</td><td>994</td><td>328</td><td>543</td><td>322</td><td>485</td><td>326</td><td>514</td></tr></table>

# 4.2Baselines

We compare our EMC-GCN with state-of-the-art baselines. These models are briefly grouped into three categories.1) Pipeline methods: ${ \mathrm { C M L A } } +$ RINANTE $^ +$ ， Li-unified-R,and Peng-two-stage are proposed by Peng et al. (202O). Peng-twostage+IOG and $\mathrm { I M N + I O G }$ are proposed by Wu et al. (2020a). 2) End-to-end methods: GTSCNN,GTS-BiLSTM, GTS-BERT (Wu et al., 2020a)，OTE-MTL (Zhang et al.， 2020)，JETBERT (Xu et al., 2020), $\mathrm { S } ^ { 3 } \mathrm { E } ^ { 2 }$ (Chen et al., 2021b) and BART-ABSA(Yan et al., 2021). 3) MRCbased methods: BMRC(Chen et al., 2O21a) is a multi-turn MRC-based model, which is end-to-end in the training phase, but works in pipeline during the inference phase.

# 4.3Implementation Details

We use the BERT-base-uncased version as our sentence encoder. AdamW optimizer (Loshchilov and Hutter, 2O18) is used with a learning rate of $2 \times 1 0 ^ { - 5 }$ for BERT fine-tuning and a learning rate of $1 0 ^ { - 3 }$ for the other trainable parameters. The dropout rate is set to O.5. The hidden state dimensionality of BERT and GCN are set to 768 and 300, respectively. The EMC-GCN model is trained in 100 epochs with a batch size of 16. To control the influence of relation constraint, we set the hyperparameter $\alpha$ and $\beta$ to 0.1 and O.01, respectively. Note that the number of channels equals to the number of relations we defined, which is immutable due to the relation constraint we proposed. All sentences are parsed by Stanza (Qi et al., 2O2O). We save the model parameters according to the best performance of the model on the development set. The reported results are the average on five runs with different random seeds.

Table 3: Experimental results on $\mathcal { D } _ { 1 }$ (Wu et al.,2O20a). Allbaseline results are from the original papers.   

<table><tr><td rowspan="2">Model</td><td colspan="3">14res</td><td colspan="3">14lap</td><td colspan="3">15res</td><td colspan="3">16res</td></tr><tr><td>P</td><td>R</td><td>F1</td><td>P</td><td>R</td><td>F1</td><td>P</td><td>R</td><td>F1</td><td>P</td><td>R</td><td>F1</td></tr><tr><td>Peng-two-stage+IOG</td><td>58.89</td><td>60.41</td><td>59.64</td><td>48.62</td><td>45.52</td><td>47.02</td><td>51.70</td><td>46.04</td><td>48.71</td><td>59.25</td><td>58.09</td><td>58.67</td></tr><tr><td>IMN+IOG</td><td>59.57</td><td>63.88</td><td>61.65</td><td>49.21</td><td>46.23</td><td>47.68</td><td>55.24</td><td>52.33</td><td>53.75</td><td>1</td><td>-</td><td>1</td></tr><tr><td>GTS-CNN</td><td>70.79</td><td>61.71</td><td>65.94</td><td>55.93</td><td>47.52</td><td>51.38</td><td>60.09</td><td>53.57</td><td>56.64</td><td>62.63</td><td>66.98</td><td>64.73</td></tr><tr><td>GTS-BiLSTM</td><td>67.28</td><td>61.91</td><td>64.49</td><td>59.42</td><td>45.13</td><td>51.30</td><td>63.26</td><td>50.71</td><td>56.29</td><td>66.07</td><td>65.05</td><td>65.56</td></tr><tr><td>SE²</td><td>69.08</td><td>64.55</td><td>66.74</td><td>59.43</td><td>46.23</td><td>52.01</td><td>61.06</td><td>56.44</td><td>58.66</td><td>71.08</td><td>63.13</td><td>66.87</td></tr><tr><td>GTS-BERT</td><td>70.92</td><td>69.49</td><td>70.20</td><td>57.52</td><td>51.92</td><td>54.58</td><td>59.29</td><td>58.07</td><td>58.67</td><td>68.58</td><td>66.60</td><td>67.58</td></tr><tr><td>BMRC</td><td>-</td><td></td><td>70.01</td><td></td><td>-</td><td>57.83</td><td></td><td></td><td>58.74</td><td></td><td></td><td>67.49</td></tr><tr><td>Our EMC-GCN</td><td>71.85</td><td>72.12</td><td>71.98</td><td>61.46</td><td>55.56</td><td>58.32</td><td>59.89</td><td>61.05</td><td>60.38</td><td>65.08</td><td>71.66</td><td>68.18</td></tr></table>

<table><tr><td rowspan="2">Model</td><td colspan="3">14res</td><td colspan="3">14lap</td><td colspan="3">15res</td><td colspan="3">16res</td></tr><tr><td>P</td><td>R</td><td>F1</td><td>P</td><td>R</td><td>F1</td><td>P</td><td>R</td><td>F1</td><td>P</td><td>R</td><td>F1</td></tr><tr><td>CMLA+</td><td>39.18</td><td>47.13</td><td>42.79</td><td>30.09</td><td>36.92</td><td>33.16</td><td>34.56</td><td>39.84</td><td>37.01</td><td>41.34</td><td>42.10</td><td>41.72</td></tr><tr><td>RINANTE+</td><td>31.42</td><td>39.38</td><td>34.95</td><td>21.71</td><td>18.66</td><td>20.07</td><td>29.88</td><td>30.06</td><td>29.97</td><td>25.68</td><td>22.30</td><td>23.87</td></tr><tr><td>Li-unified-Rb</td><td>41.04</td><td>67.35</td><td>51.00</td><td>40.56</td><td>44.28</td><td>42.34</td><td>44.72</td><td>51.39</td><td>47.82</td><td>37.33</td><td>54.51</td><td>44.31</td></tr><tr><td>Peng-two-staged</td><td>43.24</td><td>63.66</td><td>51.46</td><td>37.38</td><td>50.38</td><td>42.87</td><td>48.07</td><td>57.51</td><td>52.32</td><td>46.96</td><td>64.24</td><td>54.21</td></tr><tr><td>OTE-MTL+</td><td>62.00</td><td>55.97</td><td>58.71</td><td>49.53</td><td>39.22</td><td>43.42</td><td>56.37</td><td>40.94</td><td>47.13</td><td>62.88</td><td>52.10</td><td>56.96</td></tr><tr><td>JET-BERT4</td><td>70.56</td><td>55.94</td><td>62.40</td><td>55.39</td><td>47.33</td><td>51.04</td><td>64.45</td><td>51.96</td><td>57.53</td><td>70.42</td><td>58.37</td><td>63.83</td></tr><tr><td>GTS-BERTt</td><td>68.09</td><td>69.54</td><td>68.81</td><td>59.40</td><td>51.94</td><td>55.42</td><td>59.28</td><td>57.93</td><td>58.60</td><td>68.32</td><td>66.86</td><td>67.58</td></tr><tr><td>BMRCt</td><td>75.61</td><td>61.77</td><td>67.99</td><td>70.55</td><td>48.98</td><td>57.82</td><td>68.51</td><td>53.40</td><td>60.02</td><td>71.20</td><td>61.08</td><td>65.75</td></tr><tr><td>BART-ABSAt</td><td>65.52</td><td>64.99</td><td>65.25</td><td>61.41</td><td>56.19</td><td>58.69</td><td>59.14</td><td>59.38</td><td>59.26</td><td>66.60</td><td>68.68</td><td>67.62</td></tr><tr><td>Our EMC-GCN</td><td>71.21</td><td>72.39</td><td>71.78</td><td>61.70</td><td>56.26</td><td>58.81</td><td>61.54</td><td>62.47</td><td>61.93</td><td>65.62</td><td>71.30</td><td>68.33</td></tr></table>

Table 4: Experimental results on $\mathcal { D } _ { 2 }$ (Xu et al., 2O2O). The“b” denotes that results are retrieved from Xu et al. (2020). The“t” means that we reproduce the models using released code with original parameters on the dataset.

# 4.4Main Results

The main experimental results are reported in Tables 3 and 4.Under the F1 metric, our EMC-GCN model outperforms all pipeline,end-to-end and MRC-based methods on the two groups of datasets. We observe that end-to-end and MRC-based methods achieve more significant improvements than pipeline methods do,as they establish the correlations between these subtasks and alleviate the problem of error propagation by jointly training multiple subtasks. Note that the tagging schemes of OTE-MTL and GTS-BERT are similar to table filling. Compared with GTS-BERT,our EMCGCN significantly surpasses its performance by an average of $1 . 9 6 \%$ and $2 . 6 1 \%$ F1-score on $\mathcal { D } _ { 1 }$ and $\mathcal { D } _ { 2 }$ ,respectively. This improvement is attributed to that our EMC-GCN can leverage the relations between words and linguistic knowledge for word representation learning. Another finding is that those methods with BERT encoder, such as JETBERT, GTS-BERT and BMRC, generally achieve better performance than other methods with BiLSTM encoder. We suppose the reason is that BERT has been pre-trained on large-scale data and can provide a strong language understanding ability.

Table 5: F1 scores of ablation study on $\mathcal { D } _ { 2 }$ ：  

<table><tr><td>Model</td><td>14res</td><td>14lap</td><td>15res</td><td>16res</td></tr><tr><td>EMC-GCN</td><td>71.78</td><td>58.81</td><td>61.93</td><td>68.33</td></tr><tr><td>w/o Ten Relations</td><td>70.68</td><td>57.71</td><td>59.85</td><td>66.48</td></tr><tr><td>w/o Linguistic Features</td><td>71.22</td><td>58.38</td><td>60.62</td><td>67.15</td></tr><tr><td>w/o Relation Constraint</td><td>70.59</td><td>57.28</td><td>59.83</td><td>67.89</td></tr><tr><td> w/o Refining Strategy</td><td>70.62</td><td>56.72</td><td>60.23</td><td>67.31</td></tr></table>

# 4.5Model Analysis

# 4.5.1 Ablation Study

To investigate the effectiveness of different modules in EMC-GCN,we conduct ablation study on the second dataset $\mathcal { D } _ { 2 }$ . The experimental results are shown in Table 5.w/o Ten Relations denotes that EMC-GCN uses the same tagging schema as GTS(Wu et al., 2020a) with six labels. Without the four relations $\{ B { \cdot } A , I { - } A , B { \cdot } O , I { - } O \}$ ,EMC-GCN loses boundary information of terms, the performance drops significantly. $w / o$ Linguistic Features means that we remove the four types of features from EMC-GCN.Without the enhancement of linguistic features, the performance of our EMC-GCN is slightly degraded on 14res and 14lap,but decreased by $1 . 3 1 \%$ and $1 . 1 8 \%$ on 15res and 16res, respectively. As 15res and 16res contain less training data, the linguistic features can provide additional information when the training data is insuffcient, which is helpful to the prediction of the model. w/o Relation Constraint indicates that we remove the relation constraint loss between the adjacency tensor $R ^ { b a }$ and the golden label. Thus, each channel in the adjacency tensor cannot precisely describe the relation dependency between words. As a result, the performance of EMC-GCN w/o Relation Constraint on four sub datasets is significantly dropped. w/o Refining Strategy denotes that we remove the implicit results of aspect and opinion extraction $\mathbf { r } _ { i i }$ （202 and $\mathbf { r } _ { j j }$ from word pair representation $s _ { i j }$ . Since the adjacency tensor has a relation constraint with the golden label, we can suppose $\mathbf { r } _ { i i }$ as a predicted label or relation probability distribution of word pair $( w _ { i } , w _ { i } )$ on the main diagonal. Thus, we leverage the aspect and opinion extraction implicit results as prior information to help predict the label of word pair $( w _ { i } , w _ { j } )$ . To sum up, each module of our EMC-GCN contributes to the entire performance on the ASTE task.

Table 6: F1 scores of three sentiment relations on $\mathcal { D } _ { 2 }$ ：   

<table><tr><td rowspan="2">Model</td><td colspan="3">14res</td><td colspan="3">14lap</td></tr><tr><td>POS</td><td>NEU</td><td>NEG</td><td>POS</td><td>NEU</td><td>NEG</td></tr><tr><td>EMC-GCN</td><td>74.69</td><td>19.65</td><td>62.43</td><td>67.74</td><td>19.14</td><td>56.20</td></tr><tr><td>w/o Refining Strategy</td><td>74.98</td><td>17.39</td><td>59.87</td><td>67.31</td><td>16.08</td><td>52.74</td></tr></table>

# 4.5.2Effect of Refining Strategy

The purpose of refining strategy is to facilitate the Word pair matching process based on the aspect and opinion extraction implicit results. To verify the idea, we conduct comparative experiments of three sentiment relations $\{ P O S , N E U , N E G \}$ on 14rest and 14lap of $\mathcal { D } _ { 2 }$ . The results of are shown in Table 6.Note that the function of the three sentiment relations is to detect whether a word-pair matches or not and identify the sentiment polarity of the aspect-opinion pair. The results show that the performance of $w / o$ Refining Strategy has declined markedly and the refinement strategy works as we

![](images/3153d53164037ec35d04a3030a42a9ec0a9e440a413909268e46a5a8672dbada.jpg)

> **Picture description**: The image consists of two side-by-side heatmap visualizations illustrating sentiment relations between word pairs in a sentence.

**Left Heatmap:**

- Sentence: "air has higher resolution but the fonts are small."
- Word Pairs: Each axis represents the words in the sentence.
- Key Highlights:
  - The "higher" and "resolution" combination is prominently marked, indicating a strong relation.
  - These highlighted cells suggest a positive sentiment relationship between "higher" and "resolution."

**Right Heatmap:**

- Sentence: Same as the left heatmap.
- Word Pairs: Same axis representation.
- Key Highlights:
  - The "fonts" and "small" combination is strongly marked, showing a relation.
  - These highlighted cells suggest a negative sentiment relationship between "fonts" and "small."

Both heatmaps visually display the relationship strengths between word pairs, with darker shades indicating stronger sentiment connections, focusing on "POS" for positive and "NEG" for negative between aspect and opinion terms.
  
Figure 5: Visualization of $P O S$ and NEG relation channels of adjacency tensor $R ^ { b a }$ obtained from the biaffine attention.

![](images/5be84124a1b6cc8309b6938f169e40ca3e0958669c721f6ec6b932ca7b247c67.jpg)

> **Picture description**: The image displays a matrix-like visualization with four subfigures, labeled (a) \(R^{psc}\), (b) \(R^{dep}\), (c) \(R^{tbd}\), and (d) \(R^{rpd}\). Each subfigure is associated with a specific type of linguistic feature for a given sentence: "air has higher resolution but the fonts are small." The words from this sentence are arranged both vertically and horizontally along the axes of each subfigure, forming a grid where the intersection of each word pair is color-coded to represent a score or relevance.

1. **Subfigure (a) \(R^{psc}\)**:
   - Represents the part-of-speech combination feature.
   - Higher intensity colors indicate stronger associations between word pairs with respect to part-of-speech.

2. **Subfigure (b) \(R^{dep}\)**:
   - Illustrates the syntactic dependency type.
   - Darker colors show stronger syntactic dependencies between word pairs, consistent with dependency parsing.

3. **Subfigure (c) \(R^{tbd}\)**:
   - Displays the tree-based distance.
   - The stronger color intensity reflects shorter tree-based distances between word pairs, indicating closer syntactic relationships.

4. **Subfigure (d) \(R^{rpd}\)**:
   - Corresponds to the relative position distance.
   - Intensity relates to the physical proximity of word pairs in the sentence, with adjacent words having darker connections.

Each channel visualizes the impact of different linguistic features on the relevance or connection between word pairs in the sentence, supporting the text's discussion on how these features contribute to the model's performance in aspect sentiment triplet extraction.
  
Figure 6: Visualization of adjacency tensors of four linguistic features.

expected.

# 4.5.3Channel Visualization

To investigate the effect of relations between words, we visualize the channel slice of adjacency tensor $R ^ { b a }$ corresponding to a specific relation. Consider the sample sentence，“air has higher resolution but the fonts are small.” from 14lap dataset.This sentence comprises two triplets, $\{ ( r e s o l u t i o n , h i g h e r , P O S )$ ， $( f o n t s , s m a l l , N E G ) \}$ As shown in the left of Figure 5, the visualized adjacency information of“higher”and“resolution” corresponds to the POS relation channel. In the visualization,“higher”and“resolution”are highly related to each other. As a result, they convey their own information to each other. Similarly, in the right of Figure 5,“fonts”can receive the node representation and negative sentiment of“small" in the NEG relation channel.Meanwhile,“small” can also obtain the information of the opinion target it describes.Thus,our EMC-GCN model can readily predict the correct labels of word pairs ("fonts",“small") and("resolution",“higher").

![](images/3eef3497c9c72f206e852b8302ec7d4d4fd31da08d6c022efe85c01281ca2328.jpg)

> **Picture description**: The image provides a comparison of outputs from different models for a given sentence, highlighting aspect and opinion terms related to sentiment analysis. 

### Description:

1. **Text:**
   - The sentence presented is: "It's light and easy to transport."

2. **Highlighted Words:**
   - **"light"** and **"easy"** are highlighted in yellow, indicating they are opinion terms.
   - **"transport"** is highlighted in blue, indicating it is an aspect term.

3. **Models and Outputs:**
   - **Gold Standard (Ground Truth):**
     - Shows that "light" and "easy" are related to "transport" with red lines, showing the correct aspect-opinion pairing.
   - **GTS-BERT Model:**
     - Correctly identifies "easy" as related to "transport" but fails to correctly link "light."
   - **BMRC Model:**
     - Similarly identifies "easy" related to "transport" but misses the relation of "light."
   - **EMC-GCN Model:**
     - Correctly identifies both "light" and "easy" as opinion terms related to "transport," matching the gold standard.

4. **Interactions:**
   - Red lines indicate the relationships or connections between aspects and opinions as identified by the models.

This setup visually compares how well different models can extract and pair opinion terms with the correct aspect in a sentence. The EMC-GCN model achieves results closest to the gold standard, effectively linking both "light" and "easy" to "transport."
  
Figure 7: Different models outputs for a given sentence.

# 4.5.4Linguistic Feature Visualization

To further analyze the role of linguistic features on ASTE task, we visualize adjacency tensors of four linguistic features. We use the $l _ { 2 }$ norm of feature vector in the adjacency tensor to represent the relevance score of the corresponding word pair. In Figure 6, the first one is visualization of adjacency tensor $R ^ { p s c }$ from part-of-speech combination feature and we observe that the score between adjective and noun is higher, because adjective and noun easily form an aspect-opinion pair, while the score between adjectives is lower, since the two adjectives are usually not related and are likely to be bring noise to each other. In visualization of $R ^ { d e p }$ we find that each word only has a score with the words it directly depends on,and computes different relevance scores according to different syntactic dependency types. The visualization of $R ^ { t b d }$ shows that the relevance score calculated for each word with other words at different tree-based distances. The visualization of $R ^ { r p d }$ demonstrates that the relevance of two adjacent words is greater than that of long-distance word pairs. In summary, all linguistic features we devised contribute to ASTE task.

# 4.5.5Case Study

A case study is given in Figure 7. In this example, the aspect terms and opinion terms are highlighted in blue and yellow, respectively. The red line indicates the aspect term and opinion term match, and form a triplet with positive sentiment. The golden opinion term “light” is hard to identify by GTS-BERT and BMRC,while “easy” is predicted correctly by all methods, since“light” is farther from“transport” than “easy”. Thus,they ignore the triplet ("transport”,“light",positive), while our EMC-GCN can precisely extract it. We argue the key factor is that“light”and “transport”can establish significant connections through sentiment relation and linguistic features.

# 5Conclusions and Future Work

In this paper, we propose an EMC-GCN architecture for ASTE task. To exploit relations between words, we first devise a multi-channel graph structure for modeling different relation type of each Word pair. Then, we utilize graph convolution operation over all channels to learn relation-aware node representations.Furthermore,we consider linguistic features to enhance the GCN-based model. Finally, we design an effective refining strategy on EMC-GCN for better extracting triplets. Extensive experiments on benchmark datasets show that our EMC-GCN model consistently outperforms all baselines.In the future, we will analyse roles of linguistic features and effects of their combinations.

# Acknowledgements

This work was supported in part by the National Key R&D Program of China under Grant 2019YFFO30330O and Subject II under Grant 2019YFF0303302,in part by the National Natural Science Foundation of China under Grants 61906018 and 62076032, in part by the 111 Project under Grant BO8OO4,and in part by the Fundamental Research Funds for the Central Universities under Grant 2O21RC36. We appreciate constructive feedback from the anonymous reviewers.

# References

Chenhua Chen, Zhiyang Teng,and Yue Zhang.2020a. Inducing target-specific latent structures for aspect sentiment classification.In Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP), pages 5596-5607, Online.Association for Computational Linguistics.   
Shaowei Chen, Jie Liu, Yu Wang, Wenzheng Zhang, and Ziming Chi. 2O2Ob. Synchronous double-channel recurrent network for aspect-opinion pair extraction. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, pages 6515- 6524,Online.Association for Computational Linguistics.   
Shaowei Chen, Yu Wang, Jie Liu,and Yuelin Wang. 2021a. Bidirectional machine reading comprehension for aspect sentiment triplet extraction. Proceedings of the AAAI Conference on Artificial Intelligence, 35(14):12666-12674.   
Zhexue Chen,Hong Huang,Bang Liu, Xuanhua Shi, and Hai Jin. 2O21b. Semantic and syntactic enhanced aspect sentiment triplet extraction. In Findings of the Association for Computational Linguistics:ACLIJCNLP 2021, pages 1474-1483, Online.Association for Computational Linguistics.

Zhuang Chen and Tieyun Qian. 2O20. Enhancing aspect term extraction with soft prototypes. In Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP), pages 2107-2117, Online. Association for Computational Linguistics.

Hongliang Dai and Yangqiu Song. 2O19. Neural aspect and opinion term extraction with mined rules as weak supervision. In Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics, pages 5268-5277,Florence, Italy. Association for Computational Linguistics.

Jacob Devlin,Ming-Wei Chang，Kenton Lee，and Kristina Toutanova. 2O19. BERT: Pre-training of deep bidirectional transformers for language understanding. In Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers), pages 4171-4186,Minneapolis,Minnesota. Association for Computational Linguistics.

Zi-Yi Dou. 2O17. Capturing user and product information for document level sentiment analysis with deep memory network. In Proceedings of the 2017 Conference on Empirical Methods in Natural Language Processing,pages 521-526, Copenhagen,Denmark. Association for Computational Linguistics.

Timothy Dozat and Christopher D.Manning.2017. Deep biaffine attention for neural dependency parsing. In 5th International Conference on Learning Representations,ICLR 2Ol7,Toulon,France,April 24-26,2017, Conference Track Proceedings. OpenReview.net.

Zhifang Fan, Zhen Wu, Xin-Yu Dai, Shujian Huang,and Jiajun Chen. 2019. Target-oriented opinion words extraction with target-fused neural sequence labeling. In Proceedings of the 2O19 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers), pages 2509-2518, Minneapolis,Minnesota. Association for Computational Linguistics.

Zhijiang Guo, Yan Zhang,and Wei Lu. 2O19. Attention guided graph convolutional networks for relation extraction. In Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics, pages 241-251,Florence,Italy. Association for Computational Linguistics.

Pankaj Gupta, Hinrich Schutze,and Bernt Andrassy. 2016. Table filling multi-task recurrent neural network for joint entity and relation extraction. In Proceedings of COLING 2016, the 26th International Conference on Computational Linguistics: Technical Papers, pages 2537-2547, Osaka, Japan. The COLING 20i6 Organizing Committee.

Ruidan He,Wee Sun Lee, Hwee Tou $\mathrm { N g }$ ，andDaniel Dahlmeier. 2O19. An interactive multi-task learning network for end-to-end aspect-based sentiment analysis. In Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics, pages 504-515, Florence, Italy. Association for Computational Linguistics.

Minqing Hu and Bing Liu. 2OO4. Mining and summarizing customer reviews.In Proceedings of the tenth ACM SIGKDD international conference on Knowledge discovery and data mining, pages l68-177.

Thomas N. Kipf and Max Welling.2017.Semisupervised classification with graph convolutional networks. In 5th International Conference on Learning Representations, ICLR 2O17, Toulon, France, April 24-26,2017.

Efthymios Kouloumpis,Theresa Wilson,and Johanna Moore.2O11. Twitter sentiment analysis: The good the bad and the omg! In Fifth International AAAI conference on weblogs and social media.

Ruifan Li, Hao Chen, Fangxiang Feng, Zhanyu Ma, Xiaojie Wang,and Eduard Hovy. 2O21. Dual graph convolutional networks for aspect-based sentiment analysis. In Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 1lth International Joint Conference on Natural Language Processing (Volume 1: Long Papers), pages 6319-6329, Online. Association for Computational Linguistics.

Xin Li, Lidong Bing,Wai Lam,and Bei Shi. 2018. Transformation networks for target-oriented sentiment classification. In Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 946-956, Melbourne,Australia. Association for Computational Linguistics.

Xin Li, Lidong Bing,Piji Li, and Wai Lam. 2019a. A unified model for opinion target extraction and target sentiment prediction. Proceedings of the AAAI Conference on Artificial Intelligence, 33(O1):6714- 6721.

Xin Li, Lidong Bing, Wenxuan Zhang,and Wai Lam. 2019b. Exploiting BERT for end-to-end aspect-based sentiment analysis.In Proceedings of the 5th Workshop on Noisy User-generated Text (W-NUT 2019), pages 34-41, Hong Kong, China. Association for Computational Linguistics.

Ilya Loshchilov and Frank Hutter. 2O18. Fixing weight decay regularization in adam.

Chenyang Lyu, Jennifer Foster, and Yvette Graham. 2020. Improving document-level sentiment analysis with user and product context. In Proceedings of the 28th International Conference on Computational Linguistics,pages 6724-6729, Barcelona, Spain (Online). International Committee on Computational Linguistics.

Dehong Ma, Sujian Li,and Houfeng Wang. 2O18. Joint learning for targeted sentiment analysis. In Proceedings of the 2018 Conference on Empirical Methods in Natural Language Processing, pages 4737-4742, Brussels, Belgium. Association for Computational Linguistics.

Dehong Ma, Sujian Li, Fangzhao Wu, Xing Xie, and Houfeng Wang. 2O19. Exploring sequence-tosequence learning in aspect term extraction. In Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics, pages 3538- 3547, Florence, Italy. Association for Computational Linguistics.

Dehong Ma, Sujian Li, Xiaodong Zhang,and Houfeng Wang.2O17.Interactive attention networks for aspect-level sentiment classification. In Proceedings of the 26th International Joint Conference on Artificial Intelligence,IJCAI'17, page 4068-4074. AAAI Press.

Yue Mao, Yi Shen, Chao Yu,and Longjun Cai. 2021.A joint training dual-mrc framework for aspect based sentiment analysis. Proceedings of the AAAI Conference on Artificial Intelligence,35(15):13543-13551.

Makoto Miwa and Yutaka Sasaki.2O14. Modeling joint entity and relation extraction with table representation. In Proceedings of the 2O14 Conference on Empirical Methods in Natural Language Processing (EMNLP), pages 1858-1869,Doha, Qatar. Association for Computational Linguistics.

Haiyun Peng,Lu Xu,Lidong Bing,Fei Huang,Wei Lu, and Luo Si. 2020. Knowing what, how and why: A near complete solution for aspect-based sentiment analysis. Proceedings of the AAAl Conference on Artificial Intelligence,34(05):8600-8607.

Minh Hieu Phan and Philip O.Ogunbona.202O.Modelling context and syntactical features for aspectbased sentiment analysis. In Proceedingsof the 58th Annual Meeting of the Association for Computational Linguistics,pages 3211-3220, Online. Association for Computational Linguistics.

Maria Pontiki, Dimitris Galanis,Haris Papageorgiou, Ion Androutsopoulos, Suresh Manandhar, Mohammad AL-Smadi， Mahmoud Al-Ayyoub,Yanyan Zhao，Bing Qin,Orphée De Clercq，Véronique Hoste,Marianna Apidianaki,Xavier Tannier, Natalia Loukachevitch, Evgeniy Kotelnikov, Nuria Bel, Salud Maria Jiménez-Zafra, and Gulsen Eryigit. 2016.SemEval-2016 task 5: Aspect based sentiment analysis. In Proceedings of the 1Oth International Workshop on Semantic Evaluation (SemEval-2016), pages 19-30, San Diego, California.Association for Computational Linguistics.

Maria Pontiki, Dimitris Galanis, Haris Papageorgiou, Suresh Manandhar, and Ion Androutsopoulos.2015. SemEval-2015 task 12: Aspect based sentiment analysis.In Proceedings of the 9th International Workshop on Semantic Evaluation (SemEval 2O15), pages

486-495,Denver, Colorado. Association for Computational Linguistics.

Maria Pontiki, Dimitris Galanis, John Pavlopoulos,Harris Papageorgiou, Ion Androutsopoulos,and Suresh Manandhar.2014. SemEval-2014 task 4: Aspect based sentiment analysis. In Proceedings of the 8th International Workshop on Semantic Evaluation (SemEval 2014), pages 27-35,Dublin,Ireland.Association for Computational Linguistics.

Peng Qi, Yuhao Zhang, Yuhui Zhang,Jason Bolton, and Christopher D.Manning. 2020. Stanza: A python natural language processing toolkit for many human languages. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics: System Demonstrations, pages 101-108, Online.Association for Computational Linguistics.

Jesse Read,Bernhard Pfahringer, Geoff Holmes,and Eibe Frank.2O11.Classifier chains for multi-label classification. Machine learning,85(3):333-359.

Aliaksei Severyn and Alessandro Moschitti. 2015. Twitter sentiment analysis with deep convolutional neural networks. In Proceedings of the 38th International ACM SIGIR Conference on Research and Development in Information Retrieval, SIGIR '15, page 959-962, New York,NY,USA.Association for Computing Machinery.

Kai Sun,Richong Zhang, Samuel Mensah, Yongyi Mao, and Xudong Liu. 2019. Aspect-level sentiment analysis via convolution over dependency tree. In Proceedings of the 2O19 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-iJCNLP), pages 5679-5688,Hong Kong, China. Association for Computational Linguistics.

Duyu Tang, Bing Qin, and Ting Liu. 2O16. Aspect level sentiment classification with deep memory network. In Proceedings of the 2O16 Conference on Empirical Methods in Natural Language Processing,pages 214- 224,Austin, Texas.Association for Computational Linguistics.

Kai Wang, Weizhou Shen, Yunyi Yang, Xiaojun Quan, and Rui Wang. 2O2O. Relational graph attention network for aspect-based sentiment analysis. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, pages 3229- 3238,Online.Association for Computational Linguistics.

Wenya Wang and Sinno Jialin Pan. 2O19. Transferable interactive memory network for domain adaptation in fine-grained opinion extraction. Proceedings of the AAAI Conference on Artificial Intelligence, 33(01):7192-7199.

Wenya Wang, Sinno Jialin Pan, Daniel Dahlmeier, and Xiaokui Xiao. 2O16. Recursive neural conditional random fields for aspect-based sentiment analysis. In

Proceedings of the 2O16 Conference on Empirical Methods in Natural Language Processing,pages 616- 626,Austin, Texas.Association for Computational Linguistics.

Wenya Wang, Sinno Jialin Pan,Daniel Dahlmeier, and Xiaokui Xiao.2O17. Coupled multi-layer attentions for co-extraction of aspect and opinion terms. In Proceedings of the Thirty-First AAAI Conference on Artificial Intelligence, AAAI'17, page 3316-3322. AAAI Press.

Zhenkai Wei, Yu Hong, Bowei Zou, Meng Cheng, and Jianmin Yao.2O20. Don't eclipse your arts due to small discrepancies: Boundary repositioning with a pointer network for aspect extraction. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, pages 3678-3684, Online. Association for Computational Linguistics.

Zhen Wu, Chengcan Ying,Fei Zhao, Zhifang Fan, Xinyu Dai, and Rui Xia. 202Oa. Grid tagging scheme for aspect-oriented fine-grained opinion extraction. In Findings of the Association for Computational Linguistics: EMNLP 2020, pages 2576-2585, Online. Association for Computational Linguistics.

Zhen Wu, Fei Zhao, Xin-Yu Dai, Shujian Huang, and Jiajun Chen. 202Ob. Latent opinions transfer network for target-oriented opinion words extraction. Proceedings of the AAAl Conference on Artificial Intelligence,34(05):9298-9305.

Hu Xu, Bing Liu, Lei Shu,and Philip S. Yu. 2018. Double embeddings and CNN-based sequence labeling for aspect extraction. In Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers), pages 592-598, Melbourne,Australia.Association for Computational Linguistics.

Lu Xu, Yew Ken Chia,and Lidong Bing.2021.Learning span-level interactions for aspect sentiment triplet extraction. In Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 1lth International Joint Conference on Natural Language Processing (Volume 1: Long Papers), pages 4755-4766, Online. Association for Computational Linguistics.

Lu Xu,Hao Li,Wei Lu,and Lidong Bing.2020. Position-aware tagging for aspect sentiment triplet extraction. In Proceedings of the 2O20 Conference on Empirical Methods in Natural Language Processing (EMNLP),pages 2339-2349, Online.Association for Computational Linguistics.

Hang Yan, Junqi Dai, Tuo Ji, Xipeng Qiu, and Zheng Zhang. 2O21. A unified generative framework for aspect-based sentiment analysis. In Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and thellth International Joint Conference on Natural Language Processing (Volume 1: Long Papers), pages 2416-2429, Online. Association for Computational Linguistics.

Bishan Yang and Claire Cardie.2O12. Extracting opinion expressions with semi-Markov conditional random fields. In Proceedings of the 20l2 Joint Conference on Empirical Methods in Natural Language Processing and Computational Natural Language Learning, pages 1335-1345, Jeju Island, Korea. Association for Computational Linguistics.

Bishan Yang and Claire Cardie. 2O13. Joint inference for fine-grained opinion extraction. In Proceedings of the 5lst Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 1640-1649,Sofia, Bulgaria. Association for Computational Linguistics.

Bishan Yang and Claire Cardie.2O14. Context-aware learning for sentence-level sentiment analysis with posterior regularization. In Proceedings of the 52nd Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 325-335, Baltimore,Maryland.Association for Computational Linguistics.

Yichun Yin, Furu Wei, Li Dong, Kaimeng Xu, Ming Zhang,and Ming Zhou. 2016. Unsupervised word and dependency path embeddings for aspect term extraction.In Proceedings of the Twenty-Fifth International Joint Conference on Artificial Intelligence, IJCAI'16, page 2979-2985. AAAI Press.

Chen Zhang, Qiuchi Li,and Dawei Song. 2019. Aspectbased sentiment classification with aspect-specific graph convolutional networks. In Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP), pages 4568-4578, Hong Kong, China. Association for Computational Linguistics.

Chen Zhang, Qiuchi Li, Dawei Song, and Benyou Wang. 2020.A multi-task learning framework for opinion triplet extraction. In Findings of the Association for Computational Linguistics: EMNLP 2020, pages 819-828, Online.Association for Computational Linguistics.