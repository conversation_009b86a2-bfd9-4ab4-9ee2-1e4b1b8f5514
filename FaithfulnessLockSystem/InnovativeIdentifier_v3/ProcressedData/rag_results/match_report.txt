================================================================================
创新点与代码匹配报告
================================================================================

总创新点数: 4
总匹配代码段数: 66
平均每个创新点匹配代码段数: 16.5

创新点 1: Efficient Jailbreak Method - T-GCG
类型: method
匹配代码段数: 13
平均置信度: 0.638

匹配的代码段:
  1. AttackPrompt.test (Function)
     文件: attack_manager.py
     置信度: 0.693 (向量: 0.614 + 交叉: 0.727)
  2. GCGMultiPromptAttack.step (Function)
     文件: gcg_attack.py
     置信度: 0.682 (向量: 0.623 + 交叉: 0.707)
  3. GCGMultiPromptAttack.__init__ (Function)
     文件: gcg_attack.py
     置信度: 0.663 (向量: 0.616 + 交叉: 0.683)
  4. GCGAttackPrompt (Class)
     文件: gcg_attack.py
     置信度: 0.659 (向量: 0.591 + 交叉: 0.688)
  5. GCGAttackPrompt.grad (Function)
     文件: gcg_attack.py
     置信度: 0.650 (向量: 0.599 + 交叉: 0.672)
  ... 还有 8 个匹配项

------------------------------------------------------------

创新点 2: Automatic Multi-Coordinate Updating Strategy
类型: technique
匹配代码段数: 20
平均置信度: 0.636

匹配的代码段:
  1. MultiPromptAttack.test_all (Function)
     文件: attack_manager.py
     置信度: 0.675 (向量: 0.606 + 交叉: 0.705)
  2. PromptManager.generate (Function)
     文件: attack_manager.py
     置信度: 0.669 (向量: 0.643 + 交叉: 0.680)
  3. ProgressiveMultiPromptAttack.run (Function)
     文件: attack_manager.py
     置信度: 0.663 (向量: 0.621 + 交叉: 0.680)
  4. MultiPromptAttack.run (Function)
     文件: attack_manager.py
     置信度: 0.660 (向量: 0.606 + 交叉: 0.684)
  5. AttackPrompt.test (Function)
     文件: attack_manager.py
     置信度: 0.656 (向量: 0.660 + 交叉: 0.654)
  ... 还有 15 个匹配项

------------------------------------------------------------

创新点 3: Easy-to-Hard Initialization Strategy
类型: technique
匹配代码段数: 17
平均置信度: 0.592

匹配的代码段:
  1. AttackPrompt.prompt (Function)
     文件: attack_manager.py
     置信度: 0.624 (向量: 0.509 + 交叉: 0.674)
  2. PromptManager.generate (Function)
     文件: attack_manager.py
     置信度: 0.613 (向量: 0.535 + 交叉: 0.646)
  3. SuffixManager.get_prompt (Function)
     文件: string_utils.py
     置信度: 0.607 (向量: 0.525 + 交叉: 0.642)
  4. ProgressiveMultiPromptAttack (Class)
     文件: attack_manager.py
     置信度: 0.603 (向量: 0.502 + 交叉: 0.646)
  5. MultiPromptAttack.P (Function)
     文件: attack_manager.py
     置信度: 0.600 (向量: 0.512 + 交叉: 0.638)
  ... 还有 12 个匹配项

------------------------------------------------------------

创新点 4: Introduction of Harmful Guidance for Jailbreak
类型: method
匹配代码段数: 16
平均置信度: 0.660

匹配的代码段:
  1. GCGMultiPromptAttack (Class)
     文件: gcg_attack.py
     置信度: 0.708 (向量: 0.649 + 交叉: 0.733)
  2. AttackPrompt.input_toks (Function)
     文件: attack_manager.py
     置信度: 0.707 (向量: 0.546 + 交叉: 0.775)
  3. AttackPrompt.test (Function)
     文件: attack_manager.py
     置信度: 0.696 (向量: 0.576 + 交叉: 0.747)
  4. GCGAttackPrompt (Class)
     文件: gcg_attack.py
     置信度: 0.686 (向量: 0.589 + 交叉: 0.728)
  5. MultiPromptAttack (Class)
     文件: attack_manager.py
     置信度: 0.686 (向量: 0.566 + 交叉: 0.737)
  ... 还有 11 个匹配项

------------------------------------------------------------
