{"system_config": {"model_type": "api", "model_size": "0.6B", "dimension": 1024, "index_type": "flat", "use_cross_encoder": true, "recall_top_k": 50, "rerank_top_k": 20}, "retrieval_stats": {"database_stats": {"total_documents": 379, "embedding_dim": 1024, "index_type": "flat", "index_size": 379, "metadata_fields": {"element_name": ["str"], "element_type": ["str"], "file_path": ["str"], "start_line": ["int"], "end_line": ["NoneType", "int"], "has_docstring": ["bool"], "has_implementation": ["bool"], "relationship_count": ["int"], "chunk_type": ["str"], "chunk_id": ["str"], "is_constructor": ["bool"], "in_class": ["NoneType", "str"], "arguments": ["list"], "member_count": ["int"], "element_counts": ["dict"], "total_elements": ["int"], "classes": ["list"], "functions": ["list"], "module_name": ["str"], "element_count": ["int"], "elements": ["list"]}, "element_types": {"Module": 11, "Variable": 233, "Function": 96, "Class": 14, "unknown": 25}, "chunk_types": {"element": 354, "file": 14, "module": 11}, "total_files": 14, "files": ["/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/main.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/__init__.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/__init__.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/__init__.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/generate_our_config.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/__init__.py", "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_base.py"]}, "retriever_config": {"recall_top_k": 50, "rerank_top_k": 20, "has_cross_encoder": true, "embedding_dim": 1024}}}