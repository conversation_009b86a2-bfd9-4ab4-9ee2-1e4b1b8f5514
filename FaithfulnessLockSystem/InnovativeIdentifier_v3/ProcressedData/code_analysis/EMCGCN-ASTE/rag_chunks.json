{"repository_name": "EMCGCN-ASTE", "total_chunks": 239, "chunk_types": {"element": 229, "file": 5, "module": 5}, "chunks": [{"id": "49aa1e10786eda3ca85b81a8206ba458", "content": "Element: prepare_vocab\n\nType: Module\n\nFile: prepare_vocab.py\n\nLine: 1\n\nDefinition:\n# Module: prepare_vocab\n\nDocumentation:\n\nPrepare vocabulary and initial word vectors.\n\n\nRelationships:\nimports: json\nimports: tqdm\nimports: pickle\nimports: argparse\nimports: numpy", "metadata": {"element_name": "prepare_vocab", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 1, "end_line": 157, "has_docstring": true, "has_implementation": false, "relationship_count": 7}, "chunk_type": "element"}, {"id": "40af2da36d7d86fb380a95365160ddc4", "content": "Element: VocabHelp\n\nType: Class\n\nFile: prepare_vocab.py\n\nLine: 13\n\nDefinition:\nclass VocabHelp(object):\n\nRelationships:\ninherits_from: object\nhas_member: VocabHelp.__init__\nhas_member: VocabHelp.counter\nhas_member: VocabHelp.words_and_frequencies\nhas_member: VocabHelp.__eq__", "metadata": {"element_name": "VocabHelp", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 13, "end_line": 57, "has_docstring": false, "has_implementation": false, "relationship_count": 10, "member_count": 9}, "chunk_type": "element"}, {"id": "3f35112f13ca1777b8610a8f2ca9f8ee", "content": "Element: VocabHelp.__init__\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 14\n\nDefinition:\n    def __init__(self, counter, specials=['<pad>', '<unk>']):\n\nImplementation:\n    def __init__(self, counter, specials=['<pad>', '<unk>']):\n        self.pad_index = 0\n        self.unk_index = 1\n        counter = counter.copy()\n        self.itos = list(specials)\n        for tok in specials:\n            del counter[tok]\n        \n        # sort by frequency, then alphabetically\n        words_and_frequencies = sorted(counter.items(), key=lambda tup: tup[0])\n        words_and_frequencies.sort(key=lambda tup: tup[1], reverse=True)    # words_and_frequencies is a tuple\n\n        for word, freq in words_and_frequencies:\n            self.itos.append(word)\n\n        # stoi is simply a reverse dict for itos\n        self.stoi = {tok: i for i, tok in enumerate(self.itos)}\n\nRelationships:\nmember_of: VocabHelp\nuses_variable: VocabHelp.counter\nuses_variable: VocabHelp.words_and_frequencies\nuses_variable: tup", "metadata": {"element_name": "VocabHelp.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 14, "end_line": 30, "has_docstring": false, "has_implementation": true, "relationship_count": 4, "is_constructor": true, "in_class": "VocabHelp", "arguments": ["self", "counter", "specials"]}, "chunk_type": "element"}, {"id": "8652ff0096b573a3539de8bca757beea", "content": "Element: VocabHelp.counter\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 17\n\nDefinition:\n        counter = counter.copy()\n\nRelationships:\nmember_of: VocabHelp", "metadata": {"element_name": "VocabHelp.counter", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 17, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "9ebd8f669be033b767634c470641ca5a", "content": "Element: VocabHelp.words_and_frequencies\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 23\n\nDefinition:\n        words_and_frequencies = sorted(counter.items(), key=lambda tup: tup[0])\n\nRelationships:\nmember_of: VocabHelp", "metadata": {"element_name": "VocabHelp.words_and_frequencies", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 23, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b186f329b8798929cf16cc0028079eee", "content": "Element: VocabHelp.__eq__\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 32\n\nDefinition:\n    def __eq__(self, other):\n\nImplementation:\n    def __eq__(self, other):\n        if self.stoi != other.stoi:\n            return False\n        if self.itos != other.itos:\n            return False\n        return True\n\nRelationships:\nmember_of: VocabHelp\nsimilar_to: VocabHelp.__len__", "metadata": {"element_name": "VocabHelp.__eq__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 32, "end_line": 37, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "VocabHelp", "arguments": ["self", "other"]}, "chunk_type": "element"}, {"id": "644fe8eb41c7038977a1b1a9e4fccfdb", "content": "Element: VocabHelp.__len__\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 39\n\nDefinition:\n    def __len__(self):\n\nImplementation:\n    def __len__(self):\n        return len(self.itos)\n\nRelationships:\nmember_of: VocabHelp\nsimilar_to: VocabHelp.__eq__", "metadata": {"element_name": "VocabHelp.__len__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 39, "end_line": 40, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "VocabHelp", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "5ffda0d1eda05da8d12ff2667bde672a", "content": "Element: VocabHelp.extend\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 42\n\nDefinition:\n    def extend(self, v):\n\nImplementation:\n    def extend(self, v):\n        words = v.itos\n        for w in words:\n            if w not in self.stoi:\n                self.itos.append(w)\n                self.stoi[w] = len(self.itos) - 1\n        return self\n\nRelationships:\nmember_of: VocabHelp\nuses_variable: VocabHelp.words", "metadata": {"element_name": "VocabHelp.extend", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 42, "end_line": 48, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "VocabHelp", "arguments": ["self", "v"]}, "chunk_type": "element"}, {"id": "407382f7a092d50946d51147541f881f", "content": "Element: VocabHelp.words\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 43\n\nDefinition:\n        words = v.itos\n\nRelationships:\nmember_of: VocabHelp", "metadata": {"element_name": "VocabHelp.words", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 43, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f0e4b9f6ab29ed537a8681be0e7ccaf1", "content": "Element: VocabHelp.load_vocab\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 51\n\nDefinition:\n    def load_vocab(vocab_path: str):\n\nImplementation:\n    def load_vocab(vocab_path: str):\n        with open(vocab_path, \"rb\") as f:\n            return pickle.load(f)\n\nRelationships:\nmember_of: VocabHelp\nsimilar_to: VocabHelp.save_vocab", "metadata": {"element_name": "VocabHelp.load_vocab", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 51, "end_line": 53, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "VocabHelp", "arguments": ["vocab_path"]}, "chunk_type": "element"}, {"id": "8a39e5ebfda19d82a94599d8c885fb22", "content": "Element: VocabHelp.save_vocab\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 55\n\nDefinition:\n    def save_vocab(self, vocab_path):\n\nImplementation:\n    def save_vocab(self, vocab_path):\n        with open(vocab_path, \"wb\") as f:\n            pickle.dump(self, f)\n\nRelationships:\nmember_of: VocabHelp\nsimilar_to: VocabHelp.load_vocab", "metadata": {"element_name": "VocabHelp.save_vocab", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 55, "end_line": 57, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": false, "in_class": "VocabHelp", "arguments": ["self", "vocab_path"]}, "chunk_type": "element"}, {"id": "1d5cd6cfd75b5064efb08b3d32e418ba", "content": "Element: parse_args\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 59\n\nDefinition:\ndef parse_args():\n\nImplementation:\ndef parse_args():\n    parser = argparse.ArgumentParser(description='Prepare vocab.')\n    parser.add_argument('--data_dir', default='../data/D1/res16', help='data directory.')\n    parser.add_argument('--vocab_dir', default='../data/D1/res16', help='Output vocab directory.')\n    parser.add_argument('--lower', default=False, help='If specified, lowercase all words.')\n    args = parser.parse_args()\n    return args\n\nRelationships:\nuses_variable: VocabHelp.words\nuses_variable: parser\nuses_variable: args", "metadata": {"element_name": "parse_args", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 59, "end_line": 65, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": null, "arguments": []}, "chunk_type": "element"}, {"id": "5d55d2c821f653bd28138ae9820d617c", "content": "Element: parser\n\nType: Variable\n\nFile: main.py\n\nLine: 196\n\nDefinition:\n    parser = argparse.ArgumentParser()", "metadata": {"element_name": "parser", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 196, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "72a237d384fbd8b4f9097605279d6f08", "content": "Element: args\n\nType: Variable\n\nFile: main.py\n\nLine: 239\n\nDefinition:\n    args = parser.parse_args()", "metadata": {"element_name": "args", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 239, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "768aec9d6277df20d73f1edd8889827a", "content": "Element: main\n\nType: Module\n\nFile: main.py\n\nLine: 1\n\nDefinition:\n# Module: main\n\nRelationships:\nimports: json\nimports: os\nimports: random\nimports: argparse\nimports: torch", "metadata": {"element_name": "main", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 1, "end_line": 257, "has_docstring": false, "has_implementation": false, "relationship_count": 15}, "chunk_type": "element"}, {"id": "3976c407167997426cd532e768ae1c61", "content": "Element: train_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 71\n\nDefinition:\n    train_file = args.data_dir + '/train.json'", "metadata": {"element_name": "train_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 71, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "0b83381faf4a256b905bff874b04ae13", "content": "Element: dev_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 72\n\nDefinition:\n    dev_file = args.data_dir + '/dev.json'", "metadata": {"element_name": "dev_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 72, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "658159a83ae29e5086133c736c46a716", "content": "Element: test_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 73\n\nDefinition:\n    test_file = args.data_dir + '/test.json'", "metadata": {"element_name": "test_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 73, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "17781647f6f7da21d6f6b1e158a647fa", "content": "Element: vocab_tok_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 77\n\nDefinition:\n    vocab_tok_file = args.vocab_dir + '/vocab_tok.vocab'", "metadata": {"element_name": "vocab_tok_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 77, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "24b48310108cd2a65d85b0cf3e66982a", "content": "Element: vocab_post_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 79\n\nDefinition:\n    vocab_post_file = args.vocab_dir + '/vocab_post.vocab'", "metadata": {"element_name": "vocab_post_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 79, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "91e4e311cc59c6422a01556ae4b793ad", "content": "Element: vocab_deprel_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 81\n\nDefinition:\n    vocab_deprel_file = args.vocab_dir + '/vocab_deprel.vocab'", "metadata": {"element_name": "vocab_deprel_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 81, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c597f4cb686d86a883bef233d9e9656e", "content": "Element: vocab_postag_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 83\n\nDefinition:\n    vocab_postag_file = args.vocab_dir + '/vocab_postag.vocab'", "metadata": {"element_name": "vocab_postag_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 83, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "b9ec9f92d904fa4603656b71660903af", "content": "Element: vocab_synpost_file\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 85\n\nDefinition:\n    vocab_synpost_file = args.vocab_dir + '/vocab_synpost.vocab'", "metadata": {"element_name": "vocab_synpost_file", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 85, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "e95bc28c5874e077c676436d09737274", "content": "Element: token_counter\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 99\n\nDefinition:\n    token_counter = Counter(train_tokens + dev_tokens + test_tokens)", "metadata": {"element_name": "token_counter", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 99, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "2f5dfad4fb9efa70edb6afcc1bc81781", "content": "Element: deprel_counter\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 100\n\nDefinition:\n    deprel_counter = Counter(train_deprel + dev_deprel + test_deprel)", "metadata": {"element_name": "deprel_counter", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 100, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "bbec66f3ca1159225601f60d70689449", "content": "Element: postag_counter\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 101\n\nDefinition:\n    postag_counter = Counter(train_postag + dev_postag + test_postag)", "metadata": {"element_name": "postag_counter", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 101, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c431f43c018b3f553c86ee280737f214", "content": "Element: postag_ca_counter\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 102\n\nDefinition:\n    postag_ca_counter = Counter(train_postag_ca + dev_postag_ca + test_postag_ca)", "metadata": {"element_name": "postag_ca_counter", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 102, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "2f5e52959d32245d8957af292480ffce", "content": "Element: max_len\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 151\n\nDefinition:\n            max_len = max(len(sentence), max_len)", "metadata": {"element_name": "max_len", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 151, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "212dcfddfd5320f4315850841dcfeb4f", "content": "Element: post_counter\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 108\n\nDefinition:\n    post_counter = Counter(list(range(0, max_len)))", "metadata": {"element_name": "post_counter", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 108, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "5e25744b5bcb2600f20c813f028c571f", "content": "Element: syn_post_counter\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 109\n\nDefinition:\n    syn_post_counter = Counter(list(range(0, 5)))", "metadata": {"element_name": "syn_post_counter", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 109, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "871a1055ce03d0c4554cb0a23deceec4", "content": "Element: token_vocab\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 113\n\nDefinition:\n    token_vocab  = VocabHelp(token_counter, specials=['<pad>', '<unk>'])", "metadata": {"element_name": "token_vocab", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 113, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "5680c1b2bc3399e2c70f6a287c4a79f7", "content": "Element: post_vocab\n\nType: Variable\n\nFile: main.py\n\nLine: 185\n\nDefinition:\n    post_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_post.vocab')", "metadata": {"element_name": "post_vocab", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 185, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "d078e58124a88a36d8f157204aa328d7", "content": "Element: deprel_vocab\n\nType: Variable\n\nFile: main.py\n\nLine: 186\n\nDefinition:\n    deprel_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_deprel.vocab')", "metadata": {"element_name": "deprel_vocab", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 186, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "4647c6ef28b3c9d8d26f289e850919ce", "content": "Element: postag_vocab\n\nType: Variable\n\nFile: main.py\n\nLine: 187\n\nDefinition:\n    postag_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_postag.vocab')", "metadata": {"element_name": "postag_vocab", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 187, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "9ad8dbf53872e2d025c2af320c783c54", "content": "Element: syn_post_vocab\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 117\n\nDefinition:\n    syn_post_vocab = VocabHelp(syn_post_counter, specials=['<pad>', '<unk>'])", "metadata": {"element_name": "syn_post_vocab", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 117, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c51cf974865ea80c6f7c851a72b69124", "content": "Element: load_tokens\n\nType: Function\n\nFile: prepare_vocab.py\n\nLine: 128\n\nDefinition:\ndef load_tokens(filename):\n\nImplementation:\ndef load_tokens(filename):\n    with open(filename) as infile:\n        data = json.load(infile)\n        tokens = []\n        deprel = []\n        postag = []\n        postag_ca = []\n        \n        max_len = 0\n        for d in data:\n            sentence = d['sentence'].split()\n            tokens.extend(sentence)\n            deprel.extend(d['deprel'])\n            postag_ca.extend(d['postag'])\n            # postag.extend(d['postag'])\n            n = len(d['postag'])\n            tmp_pos = []\n            for i in range(n):\n                for j in range(n):\n                    tup = tuple(sorted([d['postag'][i], d['postag'][j]]))\n                    tmp_pos.append(tup)\n            postag.extend(tmp_pos)\n            \n            max_len = max(len(sentence), max_len)\n    print(\"{} tokens from {} examples loaded from {}.\".format(len(tokens), len(data), filename))\n    return tokens, deprel, postag, postag_ca, max_len\n\nRelationships:\ncalls: extend\nuses_variable: max_len\nuses_variable: tokens\nuses_variable: deprel\nuses_variable: postag", "metadata": {"element_name": "load_tokens", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 128, "end_line": 153, "has_docstring": false, "has_implementation": true, "relationship_count": 11, "is_constructor": false, "in_class": null, "arguments": ["filename"]}, "chunk_type": "element"}, {"id": "af6b4325fc6eecff402b864bc4d3df7b", "content": "Element: data\n\nType: Module\n\nFile: data.py\n\nLine: 1\n\nDefinition:\n# Module: data\n\nRelationships:\nimports: math\nimports: torch\nimports: numpy\nimports_from: collections\nimports_from: collections", "metadata": {"element_name": "data", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 1, "end_line": 318, "has_docstring": false, "has_implementation": false, "relationship_count": 6}, "chunk_type": "element"}, {"id": "0a352df1c841814c6af53b1972740888", "content": "Element: tokens\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 131\n\nDefinition:\n        tokens = []", "metadata": {"element_name": "tokens", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 131, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "fb7f5f29f32f75683f25023644829029", "content": "Element: deprel\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 132\n\nDefinition:\n        deprel = []", "metadata": {"element_name": "deprel", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 132, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "7269ebe260ef7fd9833b35ebf127ce84", "content": "Element: postag\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 133\n\nDefinition:\n        postag = []", "metadata": {"element_name": "postag", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 133, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "8248cd05349506392f632106b1822c10", "content": "Element: postag_ca\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 134\n\nDefinition:\n        postag_ca = []", "metadata": {"element_name": "postag_ca", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 134, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a49beae4369d0ec4a8b5ec714643e52c", "content": "Element: sentence\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 138\n\nDefinition:\n            sentence = d['sentence'].split()", "metadata": {"element_name": "sentence", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 138, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "64f271f45894f06a0309c806c191bd04", "content": "Element: n\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 143\n\nDefinition:\n            n = len(d['postag'])", "metadata": {"element_name": "n", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 143, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "2a2a6da3256addd978cf39d88aa510fb", "content": "Element: tmp_pos\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 144\n\nDefinition:\n            tmp_pos = []", "metadata": {"element_name": "tmp_pos", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 144, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "4bb6f8b5b6fd7c776f9edce51cc91a7c", "content": "Element: tup\n\nType: Variable\n\nFile: prepare_vocab.py\n\nLine: 147\n\nDefinition:\n                    tup = tuple(sorted([d['postag'][i], d['postag'][j]]))", "metadata": {"element_name": "tup", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "start_line": 147, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c767b69fd9d7d23a7dd82cc9cc26f2b4", "content": "Element: sentiment2id\n\nType: Variable\n\nFile: data.py\n\nLine: 8\n\nDefinition:\nsentiment2id = {'negative': 3, 'neutral': 4, 'positive': 5}", "metadata": {"element_name": "sentiment2id", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 8, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "3cb369a2b31cf3e94e2286ef670ede87", "content": "Element: label\n\nType: Variable\n\nFile: utils.py\n\nLine: 37\n\nDefinition:\n        label = id2label[tags[l][l]]", "metadata": {"element_name": "label", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 37, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "2e5ce6734041b4d78ee67744b243580c", "content": "Element: get_spans\n\nType: Function\n\nFile: data.py\n\nLine: 19\n\nDefinition:\ndef get_spans(tags):\n\nDocumentation:\nfor BIO tag\n\nImplementation:\ndef get_spans(tags):\n    '''for BIO tag'''\n    tags = tags.strip().split()\n    length = len(tags)\n    spans = []\n    start = -1\n    for i in range(length):\n        if tags[i].endswith('B'):\n            if start != -1:\n                spans.append([start, i - 1])\n            start = i\n        elif tags[i].endswith('O'):\n            if start != -1:\n                spans.append([start, i - 1])\n                start = -1\n    if start != -1:\n        spans.append([start, length - 1])\n    return spans\n\nRelationships:\nuses_variable: DataIterator.tags\nuses_variable: length\nuses_variable: Metric.spans\nuses_variable: Metric.start\nsimilar_to: get_evaluate_spans", "metadata": {"element_name": "get_spans", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 19, "end_line": 36, "has_docstring": true, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": null, "arguments": ["tags"]}, "chunk_type": "element"}, {"id": "ee2ce72c4970c3a61e5adedec633db8a", "content": "Element: tags\n\nType: Variable\n\nFile: data.py\n\nLine: 21\n\nDefinition:\n    tags = tags.strip().split()", "metadata": {"element_name": "tags", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 21, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "82fbc6e9a61d4a1e33f40c021fb8065c", "content": "Element: length\n\nType: Variable\n\nFile: data.py\n\nLine: 22\n\nDefinition:\n    length = len(tags)", "metadata": {"element_name": "length", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 22, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "2684ea4e5943143072edbe192303e26b", "content": "Element: spans\n\nType: Variable\n\nFile: utils.py\n\nLine: 31\n\nDefinition:\n    spans = []", "metadata": {"element_name": "spans", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 31, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "edfcb5680a87d118fce81dfa6c50de7c", "content": "Element: start\n\nType: Variable\n\nFile: data.py\n\nLine: 54\n\nDefinition:\n                start = -1", "metadata": {"element_name": "start", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 54, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "d3cd5e582957e75991e04a3709737037", "content": "Element: get_evaluate_spans\n\nType: Function\n\nFile: data.py\n\nLine: 39\n\nDefinition:\ndef get_evaluate_spans(tags, length, token_range):\n\nDocumentation:\nfor BIO tag\n\nImplementation:\ndef get_evaluate_spans(tags, length, token_range):\n    '''for BIO tag'''\n    spans = []\n    start = -1\n    for i in range(length):\n        l, r = token_range[i]\n        if tags[l] == -1:\n            continue\n        elif tags[l] == 1:\n            if start != -1:\n                spans.append([start, i - 1])\n            start = i\n        elif tags[l] == 0:\n            if start != -1:\n                spans.append([start, i - 1])\n                start = -1\n    if start != -1:\n        spans.append([start, length - 1])\n    return spans\n\nRelationships:\nuses_variable: DataIterator.tags\nuses_variable: length\nuses_variable: Metric.spans\nuses_variable: Metric.start\nsimilar_to: get_spans", "metadata": {"element_name": "get_evaluate_spans", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 39, "end_line": 57, "has_docstring": true, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": null, "arguments": ["tags", "length", "token_range"]}, "chunk_type": "element"}, {"id": "3fcd5f2f1c1a0834f266bf8b659fef80", "content": "Element: Instance\n\nType: Class\n\nFile: data.py\n\nLine: 60\n\nDefinition:\nclass Instance(object):\n\nRelationships:\ninherits_from: object\nhas_member: Instance.__init__\nhas_member: Instance.token_start\nhas_member: Instance.token_end\nhas_member: Instance.aspect", "metadata": {"element_name": "Instance", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 60, "end_line": 250, "has_docstring": false, "has_implementation": false, "relationship_count": 16, "member_count": 15}, "chunk_type": "element"}, {"id": "f6b09ce85e778639e676a1c8403261ae", "content": "Element: Instance.__init__\n\nType: Function\n\nFile: data.py\n\nLine: 61\n\nDefinition:\n    def __init__(self, tokenizer, sentence_pack, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):\n\nRelationships:\ncalls: get_spans\nmember_of: Instance\nuses_variable: VocabHelp.words\nuses_variable: args\nuses_variable: post_vocab", "metadata": {"element_name": "Instance.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 61, "end_line": 250, "has_docstring": false, "has_implementation": true, "relationship_count": 38, "is_constructor": true, "in_class": "Instance", "arguments": ["self", "tokenizer", "sentence_pack", "post_vocab", "deprel_vocab", "postag_vocab", "synpost_vocab", "args"]}, "chunk_type": "element"}, {"id": "2df2eb30de1a0d98ec34acaab66a546d", "content": "Element: Instance.token_start\n\nType: Variable\n\nFile: data.py\n\nLine: 88\n\nDefinition:\n            token_start = token_end\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.token_start", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 88, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4f1fbd7791616781ea5b2044a7acd13e", "content": "Element: Instance.token_end\n\nType: Variable\n\nFile: data.py\n\nLine: 86\n\nDefinition:\n            token_end = token_start + len(tokenizer.encode(w, add_special_tokens=False))\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.token_end", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 86, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c06840a1487904223e9bfa827ff9598c", "content": "Element: Instance.aspect\n\nType: Variable\n\nFile: data.py\n\nLine: 106\n\nDefinition:\n            aspect = triple['target_tags']\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.aspect", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 106, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "02f39ae627a916bbab2f3d8dbbb411ac", "content": "Element: Instance.opinion\n\nType: Variable\n\nFile: data.py\n\nLine: 107\n\nDefinition:\n            opinion = triple['opinion_tags']\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.opinion", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 107, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b96fdce283529e827e5df6b0b1499ad8", "content": "Element: Instance.aspect_span\n\nType: Variable\n\nFile: data.py\n\nLine: 108\n\nDefinition:\n            aspect_span = get_spans(aspect)\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.aspect_span", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 108, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "adbc63b51c36f714b2ac4104e5780d46", "content": "Element: Instance.opinion_span\n\nType: Variable\n\nFile: data.py\n\nLine: 109\n\nDefinition:\n            opinion_span = get_spans(opinion)\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.opinion_span", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 109, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "85de63713ce55ae3a203fffc9b48b1fd", "content": "Element: Instance.start\n\nType: Variable\n\nFile: data.py\n\nLine: 190\n\nDefinition:\n            start = self.token_range[i][0]\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.start", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 190, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "50efaca8f1faafc823c856886f7cce8a", "content": "Element: Instance.end\n\nType: Variable\n\nFile: data.py\n\nLine: 191\n\nDefinition:\n            end = self.token_range[i][1]\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.end", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 191, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d61083ad3c0b2981d984b6f43be9ed97", "content": "Element: Instance.set_tag\n\nType: Variable\n\nFile: data.py\n\nLine: 147\n\nDefinition:\n                    set_tag = 1 if i == l else 2\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.set_tag", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 147, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7e50ef8448810d642e28c5696bfa235e", "content": "Element: Instance.tmp\n\nType: Variable\n\nFile: data.py\n\nLine: 211\n\nDefinition:\n        tmp = [[0]*len(self.tokens) for _ in range(len(self.tokens))]\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.tmp", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 211, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d10d131779ccaecbdf3834b74831eba0", "content": "Element: Instance.j\n\nType: Variable\n\nFile: data.py\n\nLine: 213\n\nDefinition:\n            j = self.head[i]\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.j", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 213, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "68b9841dafcb6354f42ddd0579a6bda2", "content": "Element: Instance.tmp_dict\n\nType: Variable\n\nFile: data.py\n\nLine: 219\n\nDefinition:\n        tmp_dict = defaultdict(list)\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.tmp_dict", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 219, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "586c8d683a10019727b08fe34882c60a", "content": "Element: Instance.word_level_degree\n\nType: Variable\n\nFile: data.py\n\nLine: 225\n\nDefinition:\n        word_level_degree = [[4]*len(self.tokens) for _ in range(len(self.tokens))]\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.word_level_degree", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 225, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "21a62719868fc05934eebd6044ab6804", "content": "Element: Instance.node_set\n\nType: Variable\n\nFile: data.py\n\nLine: 228\n\nDefinition:\n            node_set = set()\n\nRelationships:\nmember_of: Instance", "metadata": {"element_name": "Instance.node_set", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 228, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "ce179a4b5204d387dbc77289fb31c9be", "content": "Element: load_data_instances\n\nType: Function\n\nFile: data.py\n\nLine: 253\n\nDefinition:\ndef load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):\n\nImplementation:\ndef load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args):\n    instances = list()\n    tokenizer = BertTokenizer.from_pretrained(args.bert_model_path)\n    for sentence_pack in sentence_packs:\n        instances.append(Instance(tokenizer, sentence_pack, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args))\n    return instances\n\nRelationships:\nuses_variable: args\nuses_variable: post_vocab\nuses_variable: deprel_vocab\nuses_variable: postag_vocab\nuses_variable: instances", "metadata": {"element_name": "load_data_instances", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 253, "end_line": 258, "has_docstring": false, "has_implementation": true, "relationship_count": 8, "is_constructor": false, "in_class": null, "arguments": ["sentence_packs", "post_vocab", "deprel_vocab", "postag_vocab", "synpost_vocab", "args"]}, "chunk_type": "element"}, {"id": "78990486fa29ff3734c4f553015f939d", "content": "Element: instances\n\nType: Variable\n\nFile: main.py\n\nLine: 189\n\nDefinition:\n    instances = load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)", "metadata": {"element_name": "instances", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 189, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "99625b52a7e4c895eef2c08b5baeaf27", "content": "Element: tokenizer\n\nType: Variable\n\nFile: data.py\n\nLine: 255\n\nDefinition:\n    tokenizer = BertTokenizer.from_pretrained(args.bert_model_path)", "metadata": {"element_name": "tokenizer", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 255, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "135eed84bfb49dde84b0c97688102f69", "content": "Element: DataIterator\n\nType: Class\n\nFile: data.py\n\nLine: 261\n\nDefinition:\nclass DataIterator(object):\n\nRelationships:\ninherits_from: object\nhas_member: DataIterator.__init__\nhas_member: DataIterator.get_batch\nhas_member: DataIterator.sentence_ids\nhas_member: DataIterator.sentences", "metadata": {"element_name": "DataIterator", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 261, "end_line": 317, "has_docstring": false, "has_implementation": false, "relationship_count": 18, "member_count": 17}, "chunk_type": "element"}, {"id": "b40ca05ef41a316b17ba4208dc499dc0", "content": "Element: DataIterator.__init__\n\nType: Function\n\nFile: data.py\n\nLine: 262\n\nDefinition:\n    def __init__(self, instances, args):\n\nImplementation:\n    def __init__(self, instances, args):\n        self.instances = instances\n        self.args = args\n        self.batch_count = math.ceil(len(instances)/args.batch_size)\n\nRelationships:\nmember_of: DataIterator\nuses_variable: args\nuses_variable: instances", "metadata": {"element_name": "DataIterator.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 262, "end_line": 265, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": true, "in_class": "DataIterator", "arguments": ["self", "instances", "args"]}, "chunk_type": "element"}, {"id": "c8ecb9af03fb18256a6dc589a180370a", "content": "Element: DataIterator.get_batch\n\nType: Function\n\nFile: data.py\n\nLine: 267\n\nDefinition:\n    def get_batch(self, index):\n\nRelationships:\nmember_of: DataIterator\nuses_variable: args\nuses_variable: sentence\nuses_variable: DataIterator.tags\nuses_variable: length", "metadata": {"element_name": "DataIterator.get_batch", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 267, "end_line": 317, "has_docstring": false, "has_implementation": true, "relationship_count": 20, "is_constructor": false, "in_class": "DataIterator", "arguments": ["self", "index"]}, "chunk_type": "element"}, {"id": "b908f38219323c20fd3c2e043f687c54", "content": "Element: DataIterator.sentence_ids\n\nType: Variable\n\nFile: data.py\n\nLine: 268\n\nDefinition:\n        sentence_ids = []\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.sentence_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 268, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "40809703e724be414c2634706a953f25", "content": "Element: DataIterator.sentences\n\nType: Variable\n\nFile: data.py\n\nLine: 269\n\nDefinition:\n        sentences = []\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.sentences", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 269, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "024383f1cb30a4bd0c5be3adcba7f95f", "content": "Element: DataIterator.sens_lens\n\nType: Variable\n\nFile: data.py\n\nLine: 270\n\nDefinition:\n        sens_lens = []\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.sens_lens", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 270, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f9c89997b2eb6b7d3bf8988112c8d1da", "content": "Element: DataIterator.token_ranges\n\nType: Variable\n\nFile: data.py\n\nLine: 271\n\nDefinition:\n        token_ranges = []\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.token_ranges", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 271, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f7f1597d2d7d4e05efd5b7e05de0a678", "content": "Element: DataIterator.bert_tokens\n\nType: Variable\n\nFile: data.py\n\nLine: 303\n\nDefinition:\n        bert_tokens = torch.stack(bert_tokens).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.bert_tokens", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 303, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "fd32a493195ad8f57a76844b3b5a1fa7", "content": "Element: DataIterator.lengths\n\nType: Variable\n\nFile: data.py\n\nLine: 304\n\nDefinition:\n        lengths = torch.tensor(lengths).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.lengths", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 304, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "89c12a675c90efd355107d6089afd823", "content": "Element: DataIterator.masks\n\nType: Variable\n\nFile: data.py\n\nLine: 305\n\nDefinition:\n        masks = torch.stack(masks).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.masks", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 305, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e42c5565c9e46b8dca687b2f08c2df44", "content": "Element: DataIterator.aspect_tags\n\nType: Variable\n\nFile: data.py\n\nLine: 306\n\nDefinition:\n        aspect_tags = torch.stack(aspect_tags).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.aspect_tags", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 306, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "efacf5f5cc0352b1ee9bdd206e9f464e", "content": "Element: DataIterator.opinion_tags\n\nType: Variable\n\nFile: data.py\n\nLine: 307\n\nDefinition:\n        opinion_tags = torch.stack(opinion_tags).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.opinion_tags", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 307, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c0dbfd3215be4e84840ade241858ddc0", "content": "Element: DataIterator.tags\n\nType: Variable\n\nFile: data.py\n\nLine: 308\n\nDefinition:\n        tags = torch.stack(tags).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.tags", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 308, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1a38e565158f16d703be4101191160f3", "content": "Element: DataIterator.tags_symmetry\n\nType: Variable\n\nFile: data.py\n\nLine: 309\n\nDefinition:\n        tags_symmetry = torch.stack(tags_symmetry).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.tags_symmetry", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 309, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "fe73718a62be251f5f781732f7c4c534", "content": "Element: DataIterator.word_pair_position\n\nType: Variable\n\nFile: data.py\n\nLine: 311\n\nDefinition:\n        word_pair_position = torch.stack(word_pair_position).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.word_pair_position", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 311, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "a6fed61798a77da0063cf2d3d27d2fc7", "content": "Element: DataIterator.word_pair_deprel\n\nType: Variable\n\nFile: data.py\n\nLine: 312\n\nDefinition:\n        word_pair_deprel = torch.stack(word_pair_deprel).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.word_pair_deprel", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 312, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e9edae9dfe9cb952544a135c0f69cde3", "content": "Element: DataIterator.word_pair_pos\n\nType: Variable\n\nFile: data.py\n\nLine: 313\n\nDefinition:\n        word_pair_pos = torch.stack(word_pair_pos).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.word_pair_pos", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 313, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d644a46eaa7ef0150b18798a2af8fb35", "content": "Element: DataIterator.word_pair_synpost\n\nType: Variable\n\nFile: data.py\n\nLine: 314\n\nDefinition:\n        word_pair_synpost = torch.stack(word_pair_synpost).to(self.args.device)\n\nRelationships:\nmember_of: DataIterator", "metadata": {"element_name": "DataIterator.word_pair_synpost", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "start_line": 314, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "11adbd419271cab9ac02cbacc36f64cc", "content": "Element: get_bert_optimizer\n\nType: Function\n\nFile: main.py\n\nLine: 21\n\nDefinition:\ndef get_bert_optimizer(model, args):\n\nImplementation:\ndef get_bert_optimizer(model, args):\n    # # Prepare optimizer and schedule (linear warmup and decay)\n    no_decay = ['bias', 'LayerNorm.weight']\n    diff_part = [\"bert.embeddings\", \"bert.encoder\"]\n\n    optimizer_grouped_parameters = [\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.learning_rate\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.learning_rate\n        },\n    ]\n    optimizer = AdamW(optimizer_grouped_parameters, eps=args.adam_epsilon)\n\n    return optimizer\n\nRelationships:\nuses_variable: args\nuses_variable: n\nuses_variable: no_decay\nuses_variable: diff_part\nuses_variable: optimizer_grouped_parameters", "metadata": {"element_name": "get_bert_optimizer", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 21, "end_line": 54, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": false, "in_class": null, "arguments": ["model", "args"]}, "chunk_type": "element"}, {"id": "bab83fcad60e8939be3712fa102a0f37", "content": "Element: no_decay\n\nType: Variable\n\nFile: main.py\n\nLine: 23\n\nDefinition:\n    no_decay = ['bias', 'LayerNorm.weight']", "metadata": {"element_name": "no_decay", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 23, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "bb8963ddf31af367c0cd4341bed173f1", "content": "Element: diff_part\n\nType: Variable\n\nFile: main.py\n\nLine: 24\n\nDefinition:\n    diff_part = [\"bert.embeddings\", \"bert.encoder\"]", "metadata": {"element_name": "diff_part", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 24, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "898692a3c8995bc20a869cd5c493a68e", "content": "Element: optimizer_grouped_parameters\n\nType: Variable\n\nFile: main.py\n\nLine: 26\n\nDefinition:\n    optimizer_grouped_parameters = [\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.bert_lr\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    not any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": args.weight_decay,\n            \"lr\": args.learning_rate\n        },\n        {\n            \"params\": [p for n, p in model.named_parameters() if\n                    any(nd in n for nd in no_decay) and not any(nd in n for nd in diff_part)],\n            \"weight_decay\": 0.0,\n            \"lr\": args.learning_rate\n        },\n    ]", "metadata": {"element_name": "optimizer_grouped_parameters", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 26, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "3b60d01371ce946085705ae4607eb250", "content": "Element: optimizer\n\nType: Variable\n\nFile: main.py\n\nLine: 82\n\nDefinition:\n    optimizer = get_bert_optimizer(model, args)", "metadata": {"element_name": "optimizer", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 82, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "d9b6d02c8dfec923d7e8828ad4dd3ea6", "content": "Element: train\n\nType: Function\n\nFile: main.py\n\nLine: 57\n\nDefinition:\ndef train(args):\n\nRelationships:\ncalls: eval\ncalls: get_batch\ncalls: get_bert_optimizer\ncalls: load_vocab\ncalls: load_data_instances", "metadata": {"element_name": "train", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 57, "end_line": 130, "has_docstring": false, "has_implementation": true, "relationship_count": 49, "is_constructor": false, "in_class": null, "arguments": ["args"]}, "chunk_type": "element"}, {"id": "9bc9d75c7c657e9106f883759499ac8a", "content": "Element: train_sentence_packs\n\nType: Variable\n\nFile: main.py\n\nLine: 60\n\nDefinition:\n    train_sentence_packs = json.load(open(args.prefix + args.dataset + '/train.json'))", "metadata": {"element_name": "train_sentence_packs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 60, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "20f943ab716005f6d4269cc8627d645f", "content": "Element: dev_sentence_packs\n\nType: Variable\n\nFile: main.py\n\nLine: 62\n\nDefinition:\n    dev_sentence_packs = json.load(open(args.prefix + args.dataset + '/dev.json'))", "metadata": {"element_name": "dev_sentence_packs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 62, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "57f6e492f06500e4bcbc087a4569a587", "content": "Element: synpost_vocab\n\nType: Variable\n\nFile: main.py\n\nLine: 188\n\nDefinition:\n    synpost_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_synpost.vocab')", "metadata": {"element_name": "synpost_vocab", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 188, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "7799239f92961126a3a205eb91b68150", "content": "Element: instances_train\n\nType: Variable\n\nFile: main.py\n\nLine: 73\n\nDefinition:\n    instances_train = load_data_instances(train_sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)", "metadata": {"element_name": "instances_train", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 73, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "834b9cbec2f15853e46cc030a275247c", "content": "Element: instances_dev\n\nType: Variable\n\nFile: main.py\n\nLine: 74\n\nDefinition:\n    instances_dev = load_data_instances(dev_sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)", "metadata": {"element_name": "instances_dev", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 74, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "d901f97efcfc9496776f714021d1a54f", "content": "Element: trainset\n\nType: Variable\n\nFile: main.py\n\nLine: 76\n\nDefinition:\n    trainset = DataIterator(instances_train, args)", "metadata": {"element_name": "trainset", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 76, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c93fc1f6076a533a1b42c08b11248aed", "content": "Element: devset\n\nType: Variable\n\nFile: main.py\n\nLine: 77\n\nDefinition:\n    devset = DataIterator(instances_dev, args)", "metadata": {"element_name": "devset", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 77, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a2c3582865e8b6a8cd03d8735b9fe3cf", "content": "Element: model\n\nType: Module\n\nFile: model.py\n\nLine: 1\n\nDefinition:\n# Module: model\n\nRelationships:\nimports: torch\nimports: torch.nn\nimports: torch.nn\nimports: torch.nn.functional\nimports_from: transformers", "metadata": {"element_name": "model", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 1, "end_line": 192, "has_docstring": false, "has_implementation": false, "relationship_count": 6}, "chunk_type": "element"}, {"id": "4427833a0b61b76b95f4a7ed41aa6ff4", "content": "Element: weight\n\nType: Variable\n\nFile: main.py\n\nLine: 85\n\nDefinition:\n    weight = torch.tensor([1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 2.0, 2.0]).float().cuda()", "metadata": {"element_name": "weight", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 85, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "59002ef8f084d03c7f0cb2e86d7afdf6", "content": "Element: best_joint_f1\n\nType: Variable\n\nFile: main.py\n\nLine: 128\n\nDefinition:\n            best_joint_f1 = joint_f1", "metadata": {"element_name": "best_joint_f1", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 128, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "9d0d72779c30440b4006826b4f0f223b", "content": "Element: best_joint_epoch\n\nType: Variable\n\nFile: main.py\n\nLine: 129\n\nDefinition:\n            best_joint_epoch = i", "metadata": {"element_name": "best_joint_epoch", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 129, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "151f551d5ea552229cde84c5ba33e59c", "content": "Element: tags_flatten\n\nType: Variable\n\nFile: main.py\n\nLine: 94\n\nDefinition:\n            tags_flatten = tags.reshape([-1])", "metadata": {"element_name": "tags_flatten", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 94, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "15c1237a0ffc42cc174d45c81145af50", "content": "Element: tags_symmetry_flatten\n\nType: Variable\n\nFile: main.py\n\nLine: 95\n\nDefinition:\n            tags_symmetry_flatten = tags_symmetry.reshape([-1])", "metadata": {"element_name": "tags_symmetry_flatten", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 95, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "0a179042bec6ea3335b12de8d3aaaf61", "content": "Element: predictions\n\nType: Variable\n\nFile: main.py\n\nLine: 97\n\nDefinition:\n                predictions = model(tokens, masks, word_pair_position, word_pair_deprel, word_pair_pos, word_pair_synpost)", "metadata": {"element_name": "predictions", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 97, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "8bf433830e58b00f18950b696bd4bbdd", "content": "Element: l_ba\n\nType: Variable\n\nFile: main.py\n\nLine: 99\n\nDefinition:\n                l_ba = 0.10 * F.cross_entropy(biaffine_pred.reshape([-1, biaffine_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "metadata": {"element_name": "l_ba", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 99, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "b43c0b492ccd27020295e4abe5614586", "content": "Element: l_rpd\n\nType: Variable\n\nFile: main.py\n\nLine: 100\n\nDefinition:\n                l_rpd = 0.01 * F.cross_entropy(post_pred.reshape([-1, post_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "metadata": {"element_name": "l_rpd", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 100, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "22c65496373e893c2477e2c60552d1bc", "content": "Element: l_dep\n\nType: Variable\n\nFile: main.py\n\nLine: 101\n\nDefinition:\n                l_dep = 0.01 * F.cross_entropy(deprel_pred.reshape([-1, deprel_pred.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "metadata": {"element_name": "l_dep", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 101, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "fb156ca8cc381b3a2537188b88edffb6", "content": "Element: l_psc\n\nType: Variable\n\nFile: main.py\n\nLine: 102\n\nDefinition:\n                l_psc = 0.01 * F.cross_entropy(postag.reshape([-1, postag.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "metadata": {"element_name": "l_psc", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 102, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "0108f45a65ee6ac633b63c01e6011d28", "content": "Element: l_tbd\n\nType: Variable\n\nFile: main.py\n\nLine: 103\n\nDefinition:\n                l_tbd = 0.01 * F.cross_entropy(synpost.reshape([-1, synpost.shape[3]]), tags_symmetry_flatten, ignore_index=-1)", "metadata": {"element_name": "l_tbd", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 103, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "6bfc7d4ac71600fa1f09c44fa2d17ab6", "content": "Element: l_p\n\nType: Variable\n\nFile: main.py\n\nLine: 108\n\nDefinition:\n                    l_p = F.cross_entropy(final_pred.reshape([-1, final_pred.shape[3]]), tags_flatten, weight=weight, ignore_index=-1)", "metadata": {"element_name": "l_p", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 108, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "5e6d89563e45599ffeac999733956317", "content": "Element: loss\n\nType: Variable\n\nFile: main.py\n\nLine: 117\n\nDefinition:\n                    loss = F.cross_entropy(preds_flatten, tags_flatten, weight=weight, ignore_index=-1)", "metadata": {"element_name": "loss", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 117, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "941efb84a0b90e662ed12bbd4e4c532b", "content": "Element: preds\n\nType: Variable\n\nFile: main.py\n\nLine: 148\n\nDefinition:\n            preds = torch.argmax(preds, dim=3)", "metadata": {"element_name": "preds", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 148, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a2b0bb8f7d79ad36c07bad6daa0baa99", "content": "Element: preds_flatten\n\nType: Variable\n\nFile: main.py\n\nLine: 113\n\nDefinition:\n                preds_flatten = preds.reshape([-1, preds.shape[3]])", "metadata": {"element_name": "preds_flatten", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 113, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "1233cae2c88e778db30566b153cc9ae9", "content": "Element: model_path\n\nType: Variable\n\nFile: main.py\n\nLine: 180\n\nDefinition:\n    model_path = args.model_dir + 'bert' + args.task + '.pt'", "metadata": {"element_name": "model_path", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 180, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "6426e1f1fd5c274274b0629a0f3572d7", "content": "Element: eval\n\nType: Function\n\nFile: main.py\n\nLine: 133\n\nDefinition:\ndef eval(model, dataset, args, FLAG=False):\n\nRelationships:\ncalls: score_uniontags\ncalls: get_batch\ncalls: extend\ncalls: train\ncalls: tagReport", "metadata": {"element_name": "eval", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 133, "end_line": 175, "has_docstring": false, "has_implementation": true, "relationship_count": 37, "is_constructor": false, "in_class": null, "arguments": ["model", "dataset", "args", "FLAG"]}, "chunk_type": "element"}, {"id": "8ccbdd395d368a3635a7962b60d85b1f", "content": "Element: all_ids\n\nType: Variable\n\nFile: main.py\n\nLine: 136\n\nDefinition:\n        all_ids = []", "metadata": {"element_name": "all_ids", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 136, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "81cc63c22aec914773222491a5faaec7", "content": "Element: all_sentences\n\nType: Variable\n\nFile: main.py\n\nLine: 137\n\nDefinition:\n        all_sentences = []", "metadata": {"element_name": "all_sentences", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 137, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c6474d4dc28213c430af66be264272b4", "content": "Element: all_preds\n\nType: Variable\n\nFile: main.py\n\nLine: 157\n\nDefinition:\n        all_preds = torch.cat(all_preds, dim=0).cpu().tolist()", "metadata": {"element_name": "all_preds", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 157, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "75f0c4abd5bef0e61b0415fdd7594967", "content": "Element: all_labels\n\nType: Variable\n\nFile: main.py\n\nLine: 158\n\nDefinition:\n        all_labels = torch.cat(all_labels, dim=0).cpu().tolist()", "metadata": {"element_name": "all_labels", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 158, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "19f9641d14de7d4dad915de278d44c30", "content": "Element: all_lengths\n\nType: Variable\n\nFile: main.py\n\nLine: 159\n\nDefinition:\n        all_lengths = torch.cat(all_lengths, dim=0).cpu().tolist()", "metadata": {"element_name": "all_lengths", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 159, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "817d367bfd8c78d3eeff0d6a92229dc4", "content": "Element: all_sens_lengths\n\nType: Variable\n\nFile: main.py\n\nLine: 141\n\nDefinition:\n        all_sens_lengths = []", "metadata": {"element_name": "all_sens_lengths", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 141, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "f010eb8a15d3fa3bda412f6e6831ce1a", "content": "Element: all_token_ranges\n\nType: Variable\n\nFile: main.py\n\nLine: 142\n\nDefinition:\n        all_token_ranges = []", "metadata": {"element_name": "all_token_ranges", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 142, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "b15416030e4ce3983d7fdeefd954946c", "content": "Element: metric\n\nType: Variable\n\nFile: main.py\n\nLine: 161\n\nDefinition:\n        metric = utils.Metric(args, all_preds, all_labels, all_lengths, all_sens_lengths, all_token_ranges, ignore_index=-1)", "metadata": {"element_name": "metric", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 161, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "c33fb8eedb5963689654f300535641e7", "content": "Element: aspect_results\n\nType: Variable\n\nFile: main.py\n\nLine: 163\n\nDefinition:\n        aspect_results = metric.score_aspect()", "metadata": {"element_name": "aspect_results", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 163, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "32af525accd309fc7880d9f7d441d2cc", "content": "Element: opinion_results\n\nType: Variable\n\nFile: main.py\n\nLine: 164\n\nDefinition:\n        opinion_results = metric.score_opinion()", "metadata": {"element_name": "opinion_results", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 164, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "db896b36c61a8527f9292e61135994f9", "content": "Element: test\n\nType: Function\n\nFile: main.py\n\nLine: 178\n\nDefinition:\ndef test(args):\n\nImplementation:\ndef test(args):\n    print(\"Evaluation on testset:\")\n    model_path = args.model_dir + 'bert' + args.task + '.pt'\n    model = torch.load(model_path).to(args.device)\n    model.eval()\n\n    sentence_packs = json.load(open(args.prefix + args.dataset + '/test.json'))\n    post_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_post.vocab')\n    deprel_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_deprel.vocab')\n    postag_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_postag.vocab')\n    synpost_vocab = VocabHelp.load_vocab(args.prefix + args.dataset + '/vocab_synpost.vocab')\n    instances = load_data_instances(sentence_packs, post_vocab, deprel_vocab, postag_vocab, synpost_vocab, args)\n    testset = DataIterator(instances, args)\n    eval(model, testset, args, False)\n\nRelationships:\ncalls: eval\ncalls: load_vocab\ncalls: load_data_instances\nuses_variable: args\nuses_variable: post_vocab", "metadata": {"element_name": "test", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 178, "end_line": 191, "has_docstring": false, "has_implementation": true, "relationship_count": 12, "is_constructor": false, "in_class": null, "arguments": ["args"]}, "chunk_type": "element"}, {"id": "adee1bfdf0ddb40bcc278fc41861d2ab", "content": "Element: sentence_packs\n\nType: Variable\n\nFile: main.py\n\nLine: 184\n\nDefinition:\n    sentence_packs = json.load(open(args.prefix + args.dataset + '/test.json'))", "metadata": {"element_name": "sentence_packs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 184, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "8ead5665b701f32dbbfa2a8d1f692cda", "content": "Element: testset\n\nType: Variable\n\nFile: main.py\n\nLine: 190\n\nDefinition:\n    testset = DataIterator(instances, args)", "metadata": {"element_name": "testset", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "start_line": 190, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "e624e701be8eb1b67ef3b1590439e565", "content": "Element: utils\n\nType: Module\n\nFile: utils.py\n\nLine: 1\n\nDefinition:\n# Module: utils\n\nRelationships:\nimports: numpy\nimports_from: sklearn\nimports_from: data\nimports_from: data", "metadata": {"element_name": "utils", "element_type": "<PERSON><PERSON><PERSON>", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 1, "end_line": 250, "has_docstring": false, "has_implementation": false, "relationship_count": 4}, "chunk_type": "element"}, {"id": "4accc00657a593b1952a71f21f80694b", "content": "Element: get_aspects\n\nType: Function\n\nFile: utils.py\n\nLine: 6\n\nDefinition:\ndef get_aspects(tags, length, token_range, ignore_index=-1):\n\nImplementation:\ndef get_aspects(tags, length, token_range, ignore_index=-1):\n    spans = []\n    start, end = -1, -1\n    for i in range(length):\n        l, r = token_range[i]\n        if tags[l][l] == ignore_index:\n            continue\n        label = id2label[tags[l][l]]\n        if label == 'B-A':\n            if start != -1:\n                spans.append([start, end])\n            start, end = i, i\n        elif label == 'I-A':\n            end = i\n        else:\n            if start != -1:\n                spans.append([start, end])\n                start, end = -1, -1\n    if start != -1:\n        spans.append([start, length-1])\n    \n    return spans\n\nRelationships:\nuses_variable: label\nuses_variable: DataIterator.tags\nuses_variable: length\nuses_variable: Metric.spans\nuses_variable: Metric.start", "metadata": {"element_name": "get_aspects", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 6, "end_line": 27, "has_docstring": false, "has_implementation": true, "relationship_count": 10, "is_constructor": false, "in_class": null, "arguments": ["tags", "length", "token_range", "ignore_index"]}, "chunk_type": "element"}, {"id": "56a8e2fd2509fc344001487c795e3718", "content": "Element: end\n\nType: Variable\n\nFile: utils.py\n\nLine: 43\n\nDefinition:\n            end = i", "metadata": {"element_name": "end", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 43, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 0}, "chunk_type": "element"}, {"id": "a979bc1c6b25881f9631b623a2e98d55", "content": "Element: get_opinions\n\nType: Function\n\nFile: utils.py\n\nLine: 30\n\nDefinition:\ndef get_opinions(tags, length, token_range, ignore_index=-1):\n\nImplementation:\ndef get_opinions(tags, length, token_range, ignore_index=-1):\n    spans = []\n    start, end = -1, -1\n    for i in range(length):\n        l, r = token_range[i]\n        if tags[l][l] == ignore_index:\n            continue\n        label = id2label[tags[l][l]]\n        if label == 'B-O':\n            if start != -1:\n                spans.append([start, end])\n            start, end = i, i\n        elif label == 'I-O':\n            end = i\n        else:\n            if start != -1:\n                spans.append([start, end])\n                start, end = -1, -1\n    if start != -1:\n        spans.append([start, length-1])\n    \n    return spans\n\nRelationships:\nuses_variable: label\nuses_variable: DataIterator.tags\nuses_variable: length\nuses_variable: Metric.spans\nuses_variable: Metric.start", "metadata": {"element_name": "get_opinions", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 30, "end_line": 51, "has_docstring": false, "has_implementation": true, "relationship_count": 10, "is_constructor": false, "in_class": null, "arguments": ["tags", "length", "token_range", "ignore_index"]}, "chunk_type": "element"}, {"id": "cf349e87438b2a70e98cb6f138682f7f", "content": "Element: Metric\n\nType: Class\n\nFile: utils.py\n\nLine: 54\n\nDefinition:\nclass Metric():\n\nRelationships:\nhas_member: Metric.__init__\nhas_member: Metric.get_spans\nhas_member: Metric.spans\nhas_member: Metric.start\nhas_member: Metric.find_pair", "metadata": {"element_name": "Metric", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 54, "end_line": 250, "has_docstring": false, "has_implementation": false, "relationship_count": 34, "member_count": 34}, "chunk_type": "element"}, {"id": "91aeb84d729c4b99ab0d8f8f80061e37", "content": "Element: Metric.__init__\n\nType: Function\n\nFile: utils.py\n\nLine: 55\n\nDefinition:\n    def __init__(self, args, predictions, goldens, bert_lengths, sen_lengths, tokens_ranges, ignore_index=-1):\n\nImplementation:\n    def __init__(self, args, predictions, goldens, bert_lengths, sen_lengths, tokens_ranges, ignore_index=-1):\n        self.args = args\n        self.predictions = predictions\n        self.goldens = goldens\n        self.bert_lengths = bert_lengths\n        self.sen_lengths = sen_lengths\n        self.tokens_ranges = tokens_ranges\n        self.ignore_index = -1\n        self.data_num = len(self.predictions)\n\nRelationships:\nmember_of: Metric\nuses_variable: args\nuses_variable: predictions", "metadata": {"element_name": "Metric.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 55, "end_line": 63, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": true, "in_class": "Metric", "arguments": ["self", "args", "predictions", "goldens", "bert_lengths", "sen_lengths", "tokens_ranges", "ignore_index"]}, "chunk_type": "element"}, {"id": "7ca2cf461ec3969fcb8e76bf0e911543", "content": "Element: Metric.get_spans\n\nType: Function\n\nFile: utils.py\n\nLine: 65\n\nDefinition:\n    def get_spans(self, tags, length, token_range, type):\n\nImplementation:\n    def get_spans(self, tags, length, token_range, type):\n        spans = []\n        start = -1\n        for i in range(length):\n            l, r = token_range[i]\n            if tags[l][l] == self.ignore_index:\n                continue\n            elif tags[l][l] == type:\n                if start == -1:\n                    start = i\n            elif tags[l][l] != type:\n                if start != -1:\n                    spans.append([start, i - 1])\n                    start = -1\n        if start != -1:\n            spans.append([start, length - 1])\n        return spans\n\nRelationships:\nmember_of: Metric\nuses_variable: DataIterator.tags\nuses_variable: length\nuses_variable: Metric.spans\nuses_variable: Metric.start", "metadata": {"element_name": "Metric.get_spans", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 65, "end_line": 81, "has_docstring": false, "has_implementation": true, "relationship_count": 9, "is_constructor": false, "in_class": "Metric", "arguments": ["self", "tags", "length", "token_range", "type"]}, "chunk_type": "element"}, {"id": "d992395029a2c16e9eeef4b650357d27", "content": "Element: Metric.spans\n\nType: Variable\n\nFile: utils.py\n\nLine: 66\n\nDefinition:\n        spans = []\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.spans", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 66, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "98aa542a8c86a61d9930af8e435238e9", "content": "Element: Metric.start\n\nType: Variable\n\nFile: utils.py\n\nLine: 78\n\nDefinition:\n                    start = -1\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.start", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 78, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4648282dc787e9ef6e2adbea670766bb", "content": "Element: Metric.find_pair\n\nType: Function\n\nFile: utils.py\n\nLine: 83\n\nDefinition:\n    def find_pair(self, tags, aspect_spans, opinion_spans, token_ranges):\n\nImplementation:\n    def find_pair(self, tags, aspect_spans, opinion_spans, token_ranges):\n        pairs = []\n        for al, ar in aspect_spans:\n            for pl, pr in opinion_spans:\n                tag_num = [0] * 4\n                for i in range(al, ar + 1):\n                    for j in range(pl, pr + 1):\n                        a_start = token_ranges[i][0]\n                        o_start = token_ranges[j][0]\n                        if al < pl:\n                            tag_num[int(tags[a_start][o_start])] += 1\n                        else:\n                            tag_num[int(tags[o_start][a_start])] += 1\n                if tag_num[3] == 0: continue\n                sentiment = -1\n                pairs.append([al, ar, pl, pr, sentiment])\n        return pairs\n\nRelationships:\nmember_of: Metric\nuses_variable: DataIterator.tags\nuses_variable: Instance.j\nuses_variable: DataIterator.token_ranges\nuses_variable: Metric.pairs", "metadata": {"element_name": "Metric.find_pair", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 83, "end_line": 99, "has_docstring": false, "has_implementation": true, "relationship_count": 10, "is_constructor": false, "in_class": "Metric", "arguments": ["self", "tags", "aspect_spans", "opinion_spans", "token_ranges"]}, "chunk_type": "element"}, {"id": "fdd51e22afa0edc518b6c17ef104b96b", "content": "Element: Metric.pairs\n\nType: Variable\n\nFile: utils.py\n\nLine: 84\n\nDefinition:\n        pairs = []\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.pairs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 84, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "22037239fcaed2fd2bab63f3bf6e75ee", "content": "Element: Metric.tag_num\n\nType: Variable\n\nFile: utils.py\n\nLine: 106\n\nDefinition:\n                tag_num = [0] * len(label2id)\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.tag_num", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 106, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1c86dc72acb15d572ca3dec3a7738156", "content": "Element: Metric.a_start\n\nType: Variable\n\nFile: utils.py\n\nLine: 109\n\nDefinition:\n                        a_start = token_ranges[i][0]\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.a_start", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 109, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d02ad8bb48d6465e488bf71c66b76437", "content": "Element: Metric.o_start\n\nType: Variable\n\nFile: utils.py\n\nLine: 110\n\nDefinition:\n                        o_start = token_ranges[j][0]\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.o_start", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 110, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1d0cad4cba6035591683975ff17610f2", "content": "Element: Metric.sentiment\n\nType: Variable\n\nFile: utils.py\n\nLine: 123\n\nDefinition:\n                    sentiment = 7\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.sentiment", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 123, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "22cabfcb0100aeae32f04fa2f1004cf5", "content": "Element: Metric.find_triplet\n\nType: Function\n\nFile: utils.py\n\nLine: 101\n\nDefinition:\n    def find_triplet(self, tags, aspect_spans, opinion_spans, token_ranges):\n\nImplementation:\n    def find_triplet(self, tags, aspect_spans, opinion_spans, token_ranges):\n        # label2id = {'N': 0, 'B-A': 1, 'I-A': 2, 'A': 3, 'B-O': 4, 'I-O': 5, 'O': 6, 'negative': 7, 'neutral': 8, 'positive': 9}\n        triplets_utm = []\n        for al, ar in aspect_spans:\n            for pl, pr in opinion_spans:\n                tag_num = [0] * len(label2id)\n                for i in range(al, ar + 1):\n                    for j in range(pl, pr + 1):\n                        a_start = token_ranges[i][0]\n                        o_start = token_ranges[j][0]\n                        if al < pl:\n                            tag_num[int(tags[a_start][o_start])] += 1\n                        else:\n                            tag_num[int(tags[o_start][a_start])] += 1\n\n                if sum(tag_num[7:]) == 0: continue\n                sentiment = -1\n                if tag_num[9] >= tag_num[8] and tag_num[9] >= tag_num[7]:\n                    sentiment = 9\n                elif tag_num[8] >= tag_num[7] and tag_num[8] >= tag_num[9]:\n                    sentiment = 8\n                elif tag_num[7] >= tag_num[9] and tag_num[7] >= tag_num[8]:\n                    sentiment = 7\n                if sentiment == -1:\n                    print('wrong!!!!!!!!!!!!!!!!!!!!')\n                    exit()\n                triplets_utm.append([al, ar, pl, pr, sentiment])\n        \n        return triplets_utm\n\nRelationships:\nmember_of: Metric\nuses_variable: DataIterator.tags\nuses_variable: Instance.j\nuses_variable: DataIterator.token_ranges\nuses_variable: Metric.tag_num", "metadata": {"element_name": "Metric.find_triplet", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 101, "end_line": 129, "has_docstring": false, "has_implementation": true, "relationship_count": 11, "is_constructor": false, "in_class": "Metric", "arguments": ["self", "tags", "aspect_spans", "opinion_spans", "token_ranges"]}, "chunk_type": "element"}, {"id": "afbf314c948f728d3cd1b9f7a43fda87", "content": "Element: Metric.triplets_utm\n\nType: Variable\n\nFile: utils.py\n\nLine: 103\n\nDefinition:\n        triplets_utm = []\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.triplets_utm", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 103, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5e937b869f588913418b8291fe7bc209", "content": "Element: Metric.score_aspect\n\nType: Function\n\nFile: utils.py\n\nLine: 131\n\nDefinition:\n    def score_aspect(self):\n\nImplementation:\n    def score_aspect(self):\n        assert len(self.predictions) == len(self.goldens)\n        golden_set = set()\n        predicted_set = set()\n        for i in range(self.data_num):\n            golden_aspect_spans = get_aspects(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n            for spans in golden_aspect_spans:\n                golden_set.add(str(i) + '-' + '-'.join(map(str, spans)))\n\n            predicted_aspect_spans = get_aspects(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n            for spans in predicted_aspect_spans:\n                predicted_set.add(str(i) + '-' + '-'.join(map(str, spans)))\n\n        correct_num = len(golden_set & predicted_set)\n        precision = correct_num / len(predicted_set) if len(predicted_set) > 0 else 0\n        recall = correct_num / len(golden_set) if len(golden_set) > 0 else 0\n        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n        return precision, recall, f1\n\nRelationships:\ncalls: get_aspects\nmember_of: Metric\nuses_variable: Metric.spans\nuses_variable: predictions\nuses_variable: Metric.golden_set", "metadata": {"element_name": "Metric.score_aspect", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 131, "end_line": 148, "has_docstring": false, "has_implementation": true, "relationship_count": 15, "is_constructor": false, "in_class": "Metric", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "0bf0730ee78e7fdfe8ef4464453c87c3", "content": "Element: Metric.golden_set\n\nType: Variable\n\nFile: utils.py\n\nLine: 201\n\nDefinition:\n        golden_set = set()\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.golden_set", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 201, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d3831c88019dfbf2906623fc4100d6ba", "content": "Element: Metric.predicted_set\n\nType: Variable\n\nFile: utils.py\n\nLine: 202\n\nDefinition:\n        predicted_set = set()\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.predicted_set", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 202, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "0961819e61a09df810e288703aa3a1f5", "content": "Element: Metric.golden_aspect_spans\n\nType: Variable\n\nFile: utils.py\n\nLine: 206\n\nDefinition:\n            golden_aspect_spans = get_aspects(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.golden_aspect_spans", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 206, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "2c27690ff99c553d0eed0768be39566f", "content": "Element: Metric.predicted_aspect_spans\n\nType: Variable\n\nFile: utils.py\n\nLine: 215\n\nDefinition:\n            predicted_aspect_spans = get_aspects(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.predicted_aspect_spans", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 215, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6a950e99f157d5cc4de2223748988a5e", "content": "Element: Metric.correct_num\n\nType: Variable\n\nFile: utils.py\n\nLine: 227\n\nDefinition:\n        correct_num = len(golden_set & predicted_set)\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.correct_num", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 227, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7839d8196f70406d5b990d80c1911af7", "content": "Element: Metric.precision\n\nType: Variable\n\nFile: utils.py\n\nLine: 228\n\nDefinition:\n        precision = correct_num / len(predicted_set) if len(predicted_set) > 0 else 0\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.precision", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 228, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "7f30f6d0e0828917d57140350a0a2c06", "content": "Element: Metric.recall\n\nType: Variable\n\nFile: utils.py\n\nLine: 229\n\nDefinition:\n        recall = correct_num / len(golden_set) if len(golden_set) > 0 else 0\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.recall", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 229, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d2579785c1544a709cbafdc561edd266", "content": "Element: Metric.f1\n\nType: Variable\n\nFile: utils.py\n\nLine: 230\n\nDefinition:\n        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.f1", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 230, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f4db507901fc8a40969a918036e82e02", "content": "Element: Metric.score_opinion\n\nType: Function\n\nFile: utils.py\n\nLine: 150\n\nDefinition:\n    def score_opinion(self):\n\nImplementation:\n    def score_opinion(self):\n        assert len(self.predictions) == len(self.goldens)\n        golden_set = set()\n        predicted_set = set()\n        for i in range(self.data_num):\n            golden_opinion_spans = get_opinions(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n            for spans in golden_opinion_spans:\n                golden_set.add(str(i) + '-' + '-'.join(map(str, spans)))\n\n            predicted_opinion_spans = get_opinions(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n            for spans in predicted_opinion_spans:\n                predicted_set.add(str(i) + '-' + '-'.join(map(str, spans)))\n\n        correct_num = len(golden_set & predicted_set)\n        precision = correct_num / len(predicted_set) if len(predicted_set) > 0 else 0\n        recall = correct_num / len(golden_set) if len(golden_set) > 0 else 0\n        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n        return precision, recall, f1\n\nRelationships:\ncalls: get_opinions\nmember_of: Metric\nuses_variable: Metric.spans\nuses_variable: predictions\nuses_variable: Metric.golden_set", "metadata": {"element_name": "Metric.score_opinion", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 150, "end_line": 167, "has_docstring": false, "has_implementation": true, "relationship_count": 15, "is_constructor": false, "in_class": "Metric", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "490be8da85b35b588cff5cc8700d71d1", "content": "Element: Metric.golden_opinion_spans\n\nType: Variable\n\nFile: utils.py\n\nLine: 207\n\nDefinition:\n            golden_opinion_spans = get_opinions(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.golden_opinion_spans", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 207, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "05b8c52d63ef58221e3a7847cf2673af", "content": "Element: Metric.predicted_opinion_spans\n\nType: Variable\n\nFile: utils.py\n\nLine: 216\n\nDefinition:\n            predicted_opinion_spans = get_opinions(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.predicted_opinion_spans", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 216, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "a547143e489e7ed8a542d9ff51c0f035", "content": "Element: Metric.score_uniontags\n\nType: Function\n\nFile: utils.py\n\nLine: 169\n\nDefinition:\n    def score_uniontags(self):\n\nImplementation:\n    def score_uniontags(self):\n        assert len(self.predictions) == len(self.goldens)\n        golden_set = set()\n        predicted_set = set()\n        for i in range(self.data_num):\n            golden_aspect_spans = get_aspects(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n            golden_opinion_spans = get_opinions(self.goldens[i], self.sen_lengths[i], self.tokens_ranges[i])\n            if self.args.task == 'pair':\n                golden_tuples = self.find_pair(self.goldens[i], golden_aspect_spans, golden_opinion_spans, self.tokens_ranges[i])\n            elif self.args.task == 'triplet':\n                golden_tuples = self.find_triplet(self.goldens[i], golden_aspect_spans, golden_opinion_spans, self.tokens_ranges[i])\n            for pair in golden_tuples:\n                golden_set.add(str(i) + '-' + '-'.join(map(str, pair)))\n\n            predicted_aspect_spans = get_aspects(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n            predicted_opinion_spans = get_opinions(self.predictions[i], self.sen_lengths[i], self.tokens_ranges[i])\n            if self.args.task == 'pair':\n                predicted_tuples = self.find_pair(self.predictions[i], predicted_aspect_spans, predicted_opinion_spans, self.tokens_ranges[i])\n            elif self.args.task == 'triplet':\n                predicted_tuples = self.find_triplet(self.predictions[i], predicted_aspect_spans, predicted_opinion_spans, self.tokens_ranges[i])\n            for pair in predicted_tuples:\n                predicted_set.add(str(i) + '-' + '-'.join(map(str, pair)))\n            \n        correct_num = len(golden_set & predicted_set)\n        precision = correct_num / len(predicted_set) if len(predicted_set) > 0 else 0\n        recall = correct_num / len(golden_set) if len(golden_set) > 0 else 0\n        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n        return precision, recall, f1\n\nRelationships:\ncalls: find_pair\ncalls: get_aspects\ncalls: find_triplet\ncalls: get_opinions\nmember_of: Metric", "metadata": {"element_name": "Metric.score_uniontags", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 169, "end_line": 196, "has_docstring": false, "has_implementation": true, "relationship_count": 22, "is_constructor": false, "in_class": "Metric", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "ff44d184f0ee72429698cef80c348749", "content": "Element: Metric.golden_tuples\n\nType: Variable\n\nFile: utils.py\n\nLine: 211\n\nDefinition:\n                golden_tuples = self.find_triplet(self.goldens[i], golden_aspect_spans, golden_opinion_spans, self.tokens_ranges[i])\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.golden_tuples", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 211, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "58b28df5b77c07f0170e9c3033c61f94", "content": "Element: Metric.predicted_tuples\n\nType: Variable\n\nFile: utils.py\n\nLine: 220\n\nDefinition:\n                predicted_tuples = self.find_triplet(self.predictions[i], predicted_aspect_spans, predicted_opinion_spans, self.tokens_ranges[i])\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.predicted_tuples", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 220, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "50bbf766d916d374acaa9efc5ca7a84b", "content": "Element: Metric.score_uniontags_print\n\nType: Function\n\nFile: utils.py\n\nLine: 199\n\nDefinition:\n    def score_uniontags_print(self):\n\nRelationships:\ncalls: find_pair\ncalls: get_aspects\ncalls: find_triplet\ncalls: get_opinions\nmember_of: Metric", "metadata": {"element_name": "Metric.score_uniontags_print", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 199, "end_line": 231, "has_docstring": false, "has_implementation": true, "relationship_count": 24, "is_constructor": false, "in_class": "Metric", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "3dfa68e41cc7f0681d81384d1e344a67", "content": "Element: Metric.all_golden_triplets\n\nType: Variable\n\nFile: utils.py\n\nLine: 203\n\nDefinition:\n        all_golden_triplets = []\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.all_golden_triplets", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 203, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "cd8c7af1babf89ce0c696da64a26646b", "content": "Element: Metric.all_predicted_triplets\n\nType: Variable\n\nFile: utils.py\n\nLine: 204\n\nDefinition:\n        all_predicted_triplets = []\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.all_predicted_triplets", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 204, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "c7514f743f4e36ff9180b166b245280b", "content": "Element: Metric.tagReport\n\nType: Function\n\nFile: utils.py\n\nLine: 233\n\nDefinition:\n    def tagReport(self):\n\nImplementation:\n    def tagReport(self):\n        print(len(self.predictions))\n        print(len(self.goldens))\n\n        golden_tags = []\n        predict_tags = []\n        for i in range(self.data_num):\n            for r in range(102):\n                for c in range(r, 102):\n                    if self.goldens[i][r][c] == -1:\n                        continue\n                    golden_tags.append(self.goldens[i][r][c])\n                    predict_tags.append(self.predictions[i][r][c])\n        \n        print(len(golden_tags))\n        print(len(predict_tags))\n        target_names = ['N', 'B-A', 'I-A', 'A', 'B-O', 'I-O', 'O', 'negative', 'neutral', 'positive']\n        print(metrics.classification_report(golden_tags, predict_tags, target_names=target_names, digits=4))\n\nRelationships:\nmember_of: Metric\nuses_variable: predictions\nuses_variable: Metric.golden_tags\nuses_variable: Metric.predict_tags\nuses_variable: Metric.target_names", "metadata": {"element_name": "Metric.tagReport", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 233, "end_line": 250, "has_docstring": false, "has_implementation": true, "relationship_count": 6, "is_constructor": false, "in_class": "Metric", "arguments": ["self"]}, "chunk_type": "element"}, {"id": "689e2d1793c1ca530a1431b7d420f64b", "content": "Element: Metric.golden_tags\n\nType: Variable\n\nFile: utils.py\n\nLine: 237\n\nDefinition:\n        golden_tags = []\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.golden_tags", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 237, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "333a4835c9954553e2ec7f19827cb0a0", "content": "Element: Metric.predict_tags\n\nType: Variable\n\nFile: utils.py\n\nLine: 238\n\nDefinition:\n        predict_tags = []\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.predict_tags", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 238, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "759caf124d1a03054ce63ac1880f1b25", "content": "Element: Metric.target_names\n\nType: Variable\n\nFile: utils.py\n\nLine: 249\n\nDefinition:\n        target_names = ['N', 'B-A', 'I-A', 'A', 'B-O', 'I-O', 'O', 'negative', 'neutral', 'positive']\n\nRelationships:\nmember_of: Metric", "metadata": {"element_name": "Metric.target_names", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "start_line": 249, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f4a951f68470e215f97c1b9381049efe", "content": "Element: LayerNorm\n\nType: Class\n\nFile: model.py\n\nLine: 8\n\nDefinition:\nclass LayerNorm(nn.Module):\n\nDocumentation:\nConstruct a layernorm module (See citation for details).\n\nRelationships:\ninherits_from: nn.Module\nhas_member: LayerNorm.__init__\nhas_member: LayerNorm.forward\nhas_member: LayerNorm.mean\nhas_member: LayerNorm.std", "metadata": {"element_name": "LayerNorm", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 8, "end_line": 20, "has_docstring": true, "has_implementation": false, "relationship_count": 5, "member_count": 4}, "chunk_type": "element"}, {"id": "fd3c7ddcad8f7469d4e350f58e27a4e0", "content": "Element: LayerNorm.__init__\n\nType: Function\n\nFile: model.py\n\nLine: 11\n\nDefinition:\n    def __init__(self, features, eps=1e-6):\n\nImplementation:\n    def __init__(self, features, eps=1e-6):\n        super(LayerNorm, self).__init__()\n        self.a_2 = nn.Parameter(torch.ones(features))\n        self.b_2 = nn.Parameter(torch.zeros(features))\n        self.eps = eps\n\nRelationships:\nmember_of: LayerNorm\nuses_variable: Biaffine.ones", "metadata": {"element_name": "LayerNorm.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 11, "end_line": 15, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": true, "in_class": "LayerNorm", "arguments": ["self", "features", "eps"]}, "chunk_type": "element"}, {"id": "bc9f73e827194be99334ee579140f60c", "content": "Element: LayerNorm.forward\n\nType: Function\n\nFile: model.py\n\nLine: 17\n\nDefinition:\n    def forward(self, x):\n\nImplementation:\n    def forward(self, x):\n        mean = x.mean(-1, keepdim=True)\n        std = x.std(-1, keepdim=True)\n        return self.a_2 * (x - mean) / (std + self.eps) + self.b_2\n\nRelationships:\nmember_of: LayerNorm\nuses_variable: LayerNorm.mean\nuses_variable: LayerNorm.std", "metadata": {"element_name": "LayerNorm.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 17, "end_line": 20, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": false, "in_class": "LayerNorm", "arguments": ["self", "x"]}, "chunk_type": "element"}, {"id": "76b622ddccc2ba5d4507e0502c02ce36", "content": "Element: LayerNorm.mean\n\nType: Variable\n\nFile: model.py\n\nLine: 18\n\nDefinition:\n        mean = x.mean(-1, keepdim=True)\n\nRelationships:\nmember_of: LayerNorm", "metadata": {"element_name": "LayerNorm.mean", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 18, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3c73859ca448771b7d842febebed9236", "content": "Element: LayerNorm.std\n\nType: Variable\n\nFile: model.py\n\nLine: 19\n\nDefinition:\n        std = x.std(-1, keepdim=True)\n\nRelationships:\nmember_of: LayerNorm", "metadata": {"element_name": "LayerNorm.std", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 19, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "0d4970a35c8e953dcc3bfbfebe8d05a2", "content": "Element: RefiningStrategy\n\nType: Class\n\nFile: model.py\n\nLine: 23\n\nDefinition:\nclass RefiningStrategy(nn.<PERSON><PERSON>le):\n\nRelationships:\ninherits_from: nn.Module\nhas_member: RefiningStrategy.__init__\nhas_member: RefiningStrategy.forward\nhas_member: RefiningStrategy.node\nhas_member: RefiningStrategy.edge_diag", "metadata": {"element_name": "RefiningStrategy", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 23, "end_line": 44, "has_docstring": false, "has_implementation": false, "relationship_count": 8, "member_count": 7}, "chunk_type": "element"}, {"id": "9e159c4cf96a245bffb670afffb14918", "content": "Element: RefiningStrategy.__init__\n\nType: Function\n\nFile: model.py\n\nLine: 24\n\nDefinition:\n    def __init__(self, hidden_dim, edge_dim, dim_e, dropout_ratio=0.5):\n\nImplementation:\n    def __init__(self, hidden_dim, edge_dim, dim_e, dropout_ratio=0.5):\n        super(RefiningStrategy, self).__init__()\n        self.hidden_dim = hidden_dim\n        self.edge_dim = edge_dim\n        self.dim_e = dim_e\n        self.dropout = dropout_ratio\n        self.W = nn.Linear(self.hidden_dim * 2 + self.edge_dim * 3, self.dim_e)\n\nRelationships:\nmember_of: RefiningStrategy\nsimilar_to: GraphConvLayer.__init__", "metadata": {"element_name": "RefiningStrategy.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 24, "end_line": 30, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": true, "in_class": "RefiningStrategy", "arguments": ["self", "hidden_dim", "edge_dim", "dim_e", "dropout_ratio"]}, "chunk_type": "element"}, {"id": "d9188cc384df5c9dde92d6bba0c978ee", "content": "Element: RefiningStrategy.forward\n\nType: Function\n\nFile: model.py\n\nLine: 33\n\nDefinition:\n    def forward(self, edge, node1, node2):\n\nImplementation:\n    def forward(self, edge, node1, node2):\n        batch, seq, seq, edge_dim = edge.shape\n        node = torch.cat([node1, node2], dim=-1)\n\n        edge_diag = torch.diagonal(edge, offset=0, dim1=1, dim2=2).permute(0, 2, 1).contiguous()\n        edge_i = edge_diag.unsqueeze(1).expand(batch, seq, seq, edge_dim)\n        edge_j = edge_i.permute(0, 2, 1, 3).contiguous()\n        edge = self.W(torch.cat([edge, edge_i, edge_j, node], dim=-1))\n\n        # edge = self.W(torch.cat([edge, node], dim=-1))\n\n        return edge\n\nRelationships:\nmember_of: RefiningStrategy\nuses_variable: RefiningStrategy.node\nuses_variable: RefiningStrategy.edge_diag\nuses_variable: RefiningStrategy.edge_i\nuses_variable: RefiningStrategy.edge_j", "metadata": {"element_name": "RefiningStrategy.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 33, "end_line": 44, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": false, "in_class": "RefiningStrategy", "arguments": ["self", "edge", "node1", "node2"]}, "chunk_type": "element"}, {"id": "f630b4de362547715645d25a1771e811", "content": "Element: RefiningStrategy.node\n\nType: Variable\n\nFile: model.py\n\nLine: 35\n\nDefinition:\n        node = torch.cat([node1, node2], dim=-1)\n\nRelationships:\nmember_of: RefiningStrategy", "metadata": {"element_name": "RefiningStrategy.node", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 35, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "24c3a57bcf5c0f2a376001c808213732", "content": "Element: RefiningStrategy.edge_diag\n\nType: Variable\n\nFile: model.py\n\nLine: 37\n\nDefinition:\n        edge_diag = torch.diagonal(edge, offset=0, dim1=1, dim2=2).permute(0, 2, 1).contiguous()\n\nRelationships:\nmember_of: RefiningStrategy", "metadata": {"element_name": "RefiningStrategy.edge_diag", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 37, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "1b055f38df500ed45b7d12fa1e5c859f", "content": "Element: RefiningStrategy.edge_i\n\nType: Variable\n\nFile: model.py\n\nLine: 38\n\nDefinition:\n        edge_i = edge_diag.unsqueeze(1).expand(batch, seq, seq, edge_dim)\n\nRelationships:\nmember_of: RefiningStrategy", "metadata": {"element_name": "RefiningStrategy.edge_i", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 38, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5d37a60e01c57d2a6a8d587c988607c2", "content": "Element: RefiningStrategy.edge_j\n\nType: Variable\n\nFile: model.py\n\nLine: 39\n\nDefinition:\n        edge_j = edge_i.permute(0, 2, 1, 3).contiguous()\n\nRelationships:\nmember_of: RefiningStrategy", "metadata": {"element_name": "RefiningStrategy.edge_j", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 39, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "94812306e56b109c17f8c952350c987b", "content": "Element: RefiningStrategy.edge\n\nType: Variable\n\nFile: model.py\n\nLine: 40\n\nDefinition:\n        edge = self.W(torch.cat([edge, edge_i, edge_j, node], dim=-1))\n\nRelationships:\nmember_of: RefiningStrategy", "metadata": {"element_name": "RefiningStrategy.edge", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 40, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "b5b6206394671a8934df8f71441bac55", "content": "Element: GraphConvLayer\n\nType: Class\n\nFile: model.py\n\nLine: 47\n\nDefinition:\nclass GraphConvLayer(nn.Module):\n\nDocumentation:\n A GCN module operated on dependency graphs. \n\nRelationships:\ninherits_from: nn.Module\nhas_member: GraphConvLayer.__init__\nhas_member: GraphConvLayer.forward\nhas_member: GraphConvLayer.weight_prob_softmax\nhas_member: GraphConvLayer.gcn_inputs", "metadata": {"element_name": "GraphConvLayer", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 47, "end_line": 86, "has_docstring": true, "has_implementation": false, "relationship_count": 12, "member_count": 11}, "chunk_type": "element"}, {"id": "66251fe882c32ea2ac97c766eee30e1d", "content": "Element: GraphConvLayer.__init__\n\nType: Function\n\nFile: model.py\n\nLine: 50\n\nDefinition:\n    def __init__(self, device, gcn_dim, edge_dim, dep_embed_dim, pooling='avg'):\n\nImplementation:\n    def __init__(self, device, gcn_dim, edge_dim, dep_embed_dim, pooling='avg'):\n        super(GraphConvLayer, self).__init__()\n        self.gcn_dim = gcn_dim\n        self.edge_dim = edge_dim\n        self.dep_embed_dim = dep_embed_dim\n        self.device = device\n        self.pooling = pooling\n        self.layernorm = LayerNorm(self.gcn_dim)\n        self.W = nn.Linear(self.gcn_dim, self.gcn_dim)\n        self.highway = RefiningStrategy(gcn_dim, self.edge_dim, self.dep_embed_dim, dropout_ratio=0.5)\n\nRelationships:\nmember_of: GraphConvLayer\nsimilar_to: RefiningStrategy.__init__", "metadata": {"element_name": "GraphConvLayer.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 50, "end_line": 59, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": true, "in_class": "GraphConvLayer", "arguments": ["self", "device", "gcn_dim", "edge_dim", "dep_embed_dim", "pooling"]}, "chunk_type": "element"}, {"id": "d43f6929c82c31bd5552b434f9f0cc90", "content": "Element: GraphConvLayer.forward\n\nType: Function\n\nFile: model.py\n\nLine: 61\n\nDefinition:\n    def forward(self, weight_prob_softmax, weight_adj, gcn_inputs, self_loop):\n\nImplementation:\n    def forward(self, weight_prob_softmax, weight_adj, gcn_inputs, self_loop):\n        batch, seq, dim = gcn_inputs.shape\n        weight_prob_softmax = weight_prob_softmax.permute(0, 3, 1, 2)\n    \n        gcn_inputs = gcn_inputs.unsqueeze(1).expand(batch, self.edge_dim, seq, dim)\n\n        weight_prob_softmax += self_loop\n        Ax = torch.matmul(weight_prob_softmax, gcn_inputs)\n        if self.pooling == 'avg':\n            Ax = Ax.mean(dim=1)\n        elif self.pooling == 'max':\n            Ax, _ = Ax.max(dim=1)\n        elif self.pooling == 'sum':\n            Ax = Ax.sum(dim=1)\n        # Ax: [batch, seq, dim]\n        gcn_outputs = self.W(Ax)\n        gcn_outputs = self.layernorm(gcn_outputs)\n        weights_gcn_outputs = F.relu(gcn_outputs)\n\n        node_outputs = weights_gcn_outputs\n        weight_prob_softmax = weight_prob_softmax.permute(0, 2, 3, 1).contiguous()\n        node_outputs1 = node_outputs.unsqueeze(1).expand(batch, seq, seq, dim)\n        node_outputs2 = node_outputs1.permute(0, 2, 1, 3).contiguous()\n        edge_outputs = self.highway(weight_adj, node_outputs1, node_outputs2)\n\n        return node_outputs, edge_outputs\n\nRelationships:\nmember_of: GraphConvLayer\nuses_variable: LayerNorm.mean\nuses_variable: EMCGCN.weight_prob_softmax\nuses_variable: GraphConvLayer.gcn_inputs\nuses_variable: GraphConvLayer.Ax", "metadata": {"element_name": "GraphConvLayer.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 61, "end_line": 86, "has_docstring": false, "has_implementation": true, "relationship_count": 13, "is_constructor": false, "in_class": "GraphConvLayer", "arguments": ["self", "weight_prob_softmax", "weight_adj", "gcn_inputs", "self_loop"]}, "chunk_type": "element"}, {"id": "133ab380ddaf651d7bf3b96b536f6173", "content": "Element: GraphConvLayer.weight_prob_softmax\n\nType: Variable\n\nFile: model.py\n\nLine: 81\n\nDefinition:\n        weight_prob_softmax = weight_prob_softmax.permute(0, 2, 3, 1).contiguous()\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.weight_prob_softmax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 81, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "d8ce032cc458ae09640b32f2cc4abc3f", "content": "Element: GraphConvLayer.gcn_inputs\n\nType: Variable\n\nFile: model.py\n\nLine: 65\n\nDefinition:\n        gcn_inputs = gcn_inputs.unsqueeze(1).expand(batch, self.edge_dim, seq, dim)\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.gcn_inputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 65, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "59c2937b04988115ad3d47976c9f8c96", "content": "Element: GraphConvLayer.Ax\n\nType: Variable\n\nFile: model.py\n\nLine: 74\n\nDefinition:\n            Ax = Ax.sum(dim=1)\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.Ax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 74, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "3f952c96dc321db301c4448282010f69", "content": "Element: GraphConvLayer.gcn_outputs\n\nType: Variable\n\nFile: model.py\n\nLine: 77\n\nDefinition:\n        gcn_outputs = self.layernorm(gcn_outputs)\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.gcn_outputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 77, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5f1731e555ed8389d16a5982ce9bb23b", "content": "Element: GraphConvLayer.weights_gcn_outputs\n\nType: Variable\n\nFile: model.py\n\nLine: 78\n\nDefinition:\n        weights_gcn_outputs = F.relu(gcn_outputs)\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.weights_gcn_outputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 78, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8b7eea2116f54a30bb06cd08382189b1", "content": "Element: GraphConvLayer.node_outputs\n\nType: Variable\n\nFile: model.py\n\nLine: 80\n\nDefinition:\n        node_outputs = weights_gcn_outputs\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.node_outputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 80, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "f3dfa065734ba28592902dd74ed1e7dd", "content": "Element: GraphConvLayer.node_outputs1\n\nType: Variable\n\nFile: model.py\n\nLine: 82\n\nDefinition:\n        node_outputs1 = node_outputs.unsqueeze(1).expand(batch, seq, seq, dim)\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.node_outputs1", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 82, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "19609163faa0ae27514a39680674adbb", "content": "Element: GraphConvLayer.node_outputs2\n\nType: Variable\n\nFile: model.py\n\nLine: 83\n\nDefinition:\n        node_outputs2 = node_outputs1.permute(0, 2, 1, 3).contiguous()\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.node_outputs2", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 83, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "532b6b7872cd8b09c4af5b4d0768d98f", "content": "Element: GraphConvLayer.edge_outputs\n\nType: Variable\n\nFile: model.py\n\nLine: 84\n\nDefinition:\n        edge_outputs = self.highway(weight_adj, node_outputs1, node_outputs2)\n\nRelationships:\nmember_of: GraphConvLayer", "metadata": {"element_name": "GraphConvLayer.edge_outputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 84, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "dd4e10b1f0dd5ea6ced3f50bc67f4ada", "content": "Element: Biaffine\n\nType: Class\n\nFile: model.py\n\nLine: 89\n\nDefinition:\nclass Biaffine(nn.Mo<PERSON>le):\n\nRelationships:\ninherits_from: nn.Module\nhas_member: Biaffine.__init__\nhas_member: Biaffine.forward\nhas_member: Biaffine.ones\nhas_member: Biaffine.input1", "metadata": {"element_name": "Biaffine", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 89, "end_line": 120, "has_docstring": false, "has_implementation": false, "relationship_count": 8, "member_count": 7}, "chunk_type": "element"}, {"id": "ba6316b7e9012dc41b0d7e57a1a56c59", "content": "Element: Biaffine.__init__\n\nType: Function\n\nFile: model.py\n\nLine: 90\n\nDefinition:\n    def __init__(self, args, in1_features, in2_features, out_features, bias=(True, True)):\n\nImplementation:\n    def __init__(self, args, in1_features, in2_features, out_features, bias=(True, True)):\n        super(Biaffine, self).__init__()\n        self.args = args\n        self.in1_features = in1_features\n        self.in2_features = in2_features\n        self.out_features = out_features\n        self.bias = bias\n        self.linear_input_size = in1_features + int(bias[0])\n        self.linear_output_size = out_features * (in2_features + int(bias[1]))\n        self.linear = torch.nn.Linear(in_features=self.linear_input_size,\n                                    out_features=self.linear_output_size,\n                                    bias=False)\n\nRelationships:\nmember_of: Biaffine\nuses_variable: args", "metadata": {"element_name": "Biaffine.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 90, "end_line": 101, "has_docstring": false, "has_implementation": true, "relationship_count": 2, "is_constructor": true, "in_class": "Biaffine", "arguments": ["self", "args", "in1_features", "in2_features", "out_features", "bias"]}, "chunk_type": "element"}, {"id": "491140b205da55a7799885bf5968f031", "content": "Element: Biaffine.forward\n\nType: Function\n\nFile: model.py\n\nLine: 103\n\nDefinition:\n    def forward(self, input1, input2):\n\nImplementation:\n    def forward(self, input1, input2):\n        batch_size, len1, dim1 = input1.size()\n        batch_size, len2, dim2 = input2.size()\n        if self.bias[0]:\n            ones = torch.ones(batch_size, len1, 1).to(self.args.device)\n            input1 = torch.cat((input1, ones), dim=2)\n            dim1 += 1\n        if self.bias[1]:\n            ones = torch.ones(batch_size, len2, 1).to(self.args.device)\n            input2 = torch.cat((input2, ones), dim=2)\n            dim2 += 1\n        affine = self.linear(input1)\n        affine = affine.view(batch_size, len1*self.out_features, dim2)\n        input2 = torch.transpose(input2, 1, 2)\n        biaffine = torch.bmm(affine, input2)\n        biaffine = torch.transpose(biaffine, 1, 2)\n        biaffine = biaffine.contiguous().view(batch_size, len2, len1, self.out_features)\n        return biaffine\n\nRelationships:\nmember_of: Biaffine\nuses_variable: args\nuses_variable: Biaffine.ones\nuses_variable: Biaffine.input1\nuses_variable: Biaffine.input2", "metadata": {"element_name": "Biaffine.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 103, "end_line": 120, "has_docstring": false, "has_implementation": true, "relationship_count": 7, "is_constructor": false, "in_class": "Biaffine", "arguments": ["self", "input1", "input2"]}, "chunk_type": "element"}, {"id": "507789d324f324d38ce0e112eeafff46", "content": "Element: Biaffine.ones\n\nType: Variable\n\nFile: model.py\n\nLine: 111\n\nDefinition:\n            ones = torch.ones(batch_size, len2, 1).to(self.args.device)\n\nRelationships:\nmember_of: Biaffine", "metadata": {"element_name": "Biaffine.ones", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 111, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e04369400573dcf42622971573825886", "content": "Element: Biaffine.input1\n\nType: Variable\n\nFile: model.py\n\nLine: 108\n\nDefinition:\n            input1 = torch.cat((input1, ones), dim=2)\n\nRelationships:\nmember_of: Biaffine", "metadata": {"element_name": "Biaffine.input1", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 108, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5b7950c0a224e691364b529d661f3f0b", "content": "Element: Biaffine.input2\n\nType: Variable\n\nFile: model.py\n\nLine: 116\n\nDefinition:\n        input2 = torch.transpose(input2, 1, 2)\n\nRelationships:\nmember_of: Biaffine", "metadata": {"element_name": "Biaffine.input2", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 116, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "5c46d1162d86109a56890a8d3c9b8019", "content": "Element: Biaffine.affine\n\nType: Variable\n\nFile: model.py\n\nLine: 115\n\nDefinition:\n        affine = affine.view(batch_size, len1*self.out_features, dim2)\n\nRelationships:\nmember_of: Biaffine", "metadata": {"element_name": "Biaffine.affine", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 115, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "855470fe50a0726cf02e01e493d7c259", "content": "Element: Biaffine.biaffine\n\nType: Variable\n\nFile: model.py\n\nLine: 119\n\nDefinition:\n        biaffine = biaffine.contiguous().view(batch_size, len2, len1, self.out_features)\n\nRelationships:\nmember_of: Biaffine", "metadata": {"element_name": "Biaffine.biaffine", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 119, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "cdeb94424f6c92520e3881f24d707165", "content": "Element: EMCGCN\n\nType: Class\n\nFile: model.py\n\nLine: 123\n\nDefinition:\nclass EMCGCN(torch.nn.Module):\n\nRelationships:\ninherits_from: torch.nn.Module\nhas_member: EMCGCN.__init__\nhas_member: EMCGCN.forward\nhas_member: EMCGCN.bert_feature\nhas_member: EMCGCN.tensor_masks", "metadata": {"element_name": "EMCGCN", "element_type": "Class", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 123, "end_line": 192, "has_docstring": false, "has_implementation": false, "relationship_count": 23, "member_count": 22}, "chunk_type": "element"}, {"id": "95d0e7da7ce08a1c9c5a306e50e48e0f", "content": "Element: EMCGCN.__init__\n\nType: Function\n\nFile: model.py\n\nLine: 124\n\nDefinition:\n    def __init__(self, args):\n\nImplementation:\n    def __init__(self, args):\n        super(EMCGCN, self).__init__()\n        self.args = args\n        self.bert = BertModel.from_pretrained(args.bert_model_path)\n        self.tokenizer = BertTokenizer.from_pretrained(args.bert_model_path)\n        self.dropout_output = torch.nn.Dropout(args.emb_dropout)\n\n        self.post_emb = torch.nn.Embedding(args.post_size, args.class_num, padding_idx=0)\n        self.deprel_emb = torch.nn.Embedding(args.deprel_size, args.class_num, padding_idx=0)\n        self.postag_emb  = torch.nn.Embedding(args.postag_size, args.class_num, padding_idx=0)\n        self.synpost_emb = torch.nn.Embedding(args.synpost_size, args.class_num, padding_idx=0)\n        \n        self.triplet_biaffine = Biaffine(args, args.gcn_dim, args.gcn_dim, args.class_num, bias=(True, True))\n        self.ap_fc = nn.Linear(args.bert_feature_dim, args.gcn_dim)\n        self.op_fc = nn.Linear(args.bert_feature_dim, args.gcn_dim)\n\n        self.dense = nn.Linear(args.bert_feature_dim, args.gcn_dim)\n        self.num_layers = args.num_layers\n        self.gcn_layers = nn.ModuleList()\n\n        self.layernorm = LayerNorm(args.bert_feature_dim)\n\n        for i in range(self.num_layers):\n            self.gcn_layers.append(\n                GraphConvLayer(args.device, args.gcn_dim, 5*args.class_num, args.class_num, args.pooling))\n\nRelationships:\nmember_of: EMCGCN\nuses_variable: args\nuses_variable: tokenizer", "metadata": {"element_name": "EMCGCN.__init__", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 124, "end_line": 148, "has_docstring": false, "has_implementation": true, "relationship_count": 3, "is_constructor": true, "in_class": "EMCGCN", "arguments": ["self", "args"]}, "chunk_type": "element"}, {"id": "8fcb6767fc64fce9911a0dc0124019ef", "content": "Element: EMCGCN.forward\n\nType: Function\n\nFile: model.py\n\nLine: 150\n\nDefinition:\n    def forward(self, tokens, masks, word_pair_position, word_pair_deprel, word_pair_pos, word_pair_synpost):\n\nRelationships:\nmember_of: EMCGCN\nuses_variable: args\nuses_variable: tokens\nuses_variable: DataIterator.masks\nuses_variable: DataIterator.word_pair_position", "metadata": {"element_name": "EMCGCN.forward", "element_type": "Function", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 150, "end_line": 192, "has_docstring": false, "has_implementation": true, "relationship_count": 28, "is_constructor": false, "in_class": "EMCGCN", "arguments": ["self", "tokens", "masks", "word_pair_position", "word_pair_deprel", "word_pair_pos", "word_pair_synpost"]}, "chunk_type": "element"}, {"id": "79344035565579ca4a91948636cddb3e", "content": "Element: EMCGCN.bert_feature\n\nType: Variable\n\nFile: model.py\n\nLine: 152\n\nDefinition:\n        bert_feature = self.dropout_output(bert_feature) \n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.bert_feature", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 152, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "9503f5888fa52cebf633c290cf59b48a", "content": "Element: EMCGCN.tensor_masks\n\nType: Variable\n\nFile: model.py\n\nLine: 155\n\nDefinition:\n        tensor_masks = masks.unsqueeze(1).expand(batch, seq, seq).unsqueeze(-1)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.tensor_masks", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 155, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "34fb750e576650f53c9c59f3fc70ea65", "content": "Element: EMCGCN.word_pair_post_emb\n\nType: Variable\n\nFile: model.py\n\nLine: 158\n\nDefinition:\n        word_pair_post_emb = self.post_emb(word_pair_position)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_post_emb", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 158, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8d9204d1718ac4ba79276905a8171a0c", "content": "Element: EMCGCN.word_pair_deprel_emb\n\nType: Variable\n\nFile: model.py\n\nLine: 159\n\nDefinition:\n        word_pair_deprel_emb = self.deprel_emb(word_pair_deprel)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_deprel_emb", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 159, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6d38ec85ca9b5f517185504837925a67", "content": "Element: EMCGCN.word_pair_postag_emb\n\nType: Variable\n\nFile: model.py\n\nLine: 160\n\nDefinition:\n        word_pair_postag_emb = self.postag_emb(word_pair_pos)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_postag_emb", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 160, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e08faf760fbaa1b985e44efad4460b6c", "content": "Element: EMCGCN.word_pair_synpost_emb\n\nType: Variable\n\nFile: model.py\n\nLine: 161\n\nDefinition:\n        word_pair_synpost_emb = self.synpost_emb(word_pair_synpost)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_synpost_emb", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 161, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "650527a411e9ce97631586df9ffa5a54", "content": "Element: EMCGCN.ap_node\n\nType: Variable\n\nFile: model.py\n\nLine: 164\n\nDefinition:\n        ap_node = F.relu(self.ap_fc(bert_feature))\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.ap_node", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 164, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "8d7d900a7028417ed834f8c83195d7b4", "content": "Element: EMCGCN.op_node\n\nType: Variable\n\nFile: model.py\n\nLine: 165\n\nDefinition:\n        op_node = <PERSON>.relu(self.op_fc(bert_feature))\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.op_node", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 165, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "6f07cf78866713e1a8f1061eb66a1e4c", "content": "Element: EMCGCN.biaffine_edge\n\nType: Variable\n\nFile: model.py\n\nLine: 166\n\nDefinition:\n        biaffine_edge = self.triplet_biaffine(ap_node, op_node)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.biaffine_edge", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 166, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "cdd8e9866c50a9ea896ad6cd53c36195", "content": "Element: EMCGCN.gcn_input\n\nType: Variable\n\nFile: model.py\n\nLine: 167\n\nDefinition:\n        gcn_input = F.relu(self.dense(bert_feature))\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.gcn_input", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 167, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "4d30535b527fc536ea8914f49d4e6024", "content": "Element: EMCGCN.gcn_outputs\n\nType: Variable\n\nFile: model.py\n\nLine: 168\n\nDefinition:\n        gcn_outputs = gcn_input\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.gcn_outputs", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 168, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "02cceb647dfc637c38cfe389ada6739c", "content": "Element: EMCGCN.weight_prob_list\n\nType: Variable\n\nFile: model.py\n\nLine: 170\n\nDefinition:\n        weight_prob_list = [biaffine_edge, word_pair_post_emb, word_pair_deprel_emb, word_pair_postag_emb, word_pair_synpost_emb]\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.weight_prob_list", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 170, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "dc31651555baf614e7d17518b1300d09", "content": "Element: EMCGCN.biaffine_edge_softmax\n\nType: Variable\n\nFile: model.py\n\nLine: 172\n\nDefinition:\n        biaffine_edge_softmax = F.softmax(biaffine_edge, dim=-1) * tensor_masks\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.biaffine_edge_softmax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 172, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "34b2fd37d8fa6e5a55853430d1d6d0dd", "content": "Element: EMCGCN.word_pair_post_emb_softmax\n\nType: Variable\n\nFile: model.py\n\nLine: 173\n\nDefinition:\n        word_pair_post_emb_softmax = F.softmax(word_pair_post_emb, dim=-1) * tensor_masks\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_post_emb_softmax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 173, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "ae6e184106130f371bd107a8ef512c33", "content": "Element: EMCGCN.word_pair_deprel_emb_softmax\n\nType: Variable\n\nFile: model.py\n\nLine: 174\n\nDefinition:\n        word_pair_deprel_emb_softmax = F.softmax(word_pair_deprel_emb, dim=-1) * tensor_masks\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_deprel_emb_softmax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 174, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e2cad18e82b5d919252b8d62b0945a8b", "content": "Element: EMCGCN.word_pair_postag_emb_softmax\n\nType: Variable\n\nFile: model.py\n\nLine: 175\n\nDefinition:\n        word_pair_postag_emb_softmax = F.softmax(word_pair_postag_emb, dim=-1) * tensor_masks\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_postag_emb_softmax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 175, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e41ea5313a022cec1d30986859cbffe7", "content": "Element: EMCGCN.word_pair_synpost_emb_softmax\n\nType: Variable\n\nFile: model.py\n\nLine: 176\n\nDefinition:\n        word_pair_synpost_emb_softmax = F.softmax(word_pair_synpost_emb, dim=-1) * tensor_masks\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.word_pair_synpost_emb_softmax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 176, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "603b7ca892dfe881057390d2d968356e", "content": "Element: EMCGCN.self_loop\n\nType: Variable\n\nFile: model.py\n\nLine: 181\n\nDefinition:\n        self_loop = torch.stack(self_loop).to(self.args.device).unsqueeze(1).expand(batch, 5*self.args.class_num, seq, seq) * tensor_masks.permute(0, 3, 1, 2).contiguous()\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.self_loop", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 181, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "20cb7096a70c2ba75abbe433df66a777", "content": "Element: EMCGCN.weight_prob\n\nType: Variable\n\nFile: model.py\n\nLine: 183\n\nDefinition:\n        weight_prob = torch.cat([biaffine_edge, word_pair_post_emb, word_pair_deprel_emb, \\\n            word_pair_postag_emb, word_pair_synpost_emb], dim=-1)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.weight_prob", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 183, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "e6b103b0ca304291b1de5ded1d996549", "content": "Element: EMCGCN.weight_prob_softmax\n\nType: Variable\n\nFile: model.py\n\nLine: 185\n\nDefinition:\n        weight_prob_softmax = torch.cat([biaffine_edge_softmax, word_pair_post_emb_softmax, \\\n            word_pair_deprel_emb_softmax, word_pair_postag_emb_softmax, word_pair_synpost_emb_softmax], dim=-1)\n\nRelationships:\nmember_of: EMCGCN", "metadata": {"element_name": "EMCGCN.weight_prob_softmax", "element_type": "Variable", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "start_line": 185, "end_line": null, "has_docstring": false, "has_implementation": false, "relationship_count": 1}, "chunk_type": "element"}, {"id": "9ac1cd6ab5bc537aabaeff128ddda5c3", "content": "File: prepare_vocab.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py\n\nElements in this file:\n\n- Module: 1\n\n- Class: 1\n\n- Function: 9\n\n- Variable: 34\n\nClasses: VocabHelp\n\nFunctions: VocabHelp.__init__, VocabHelp.__eq__, VocabHelp.__len__, VocabHelp.extend, VocabHelp.load_vocab, VocabHelp.save_vocab, parse_args, main, load_tokens\n\nModule Documentation:\n\nPrepare vocabulary and initial word vectors.\n", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "element_counts": {"Module": 1, "Class": 1, "Function": 9, "Variable": 34}, "total_elements": 45, "classes": ["VocabHelp"], "functions": ["VocabHelp.__init__", "VocabHelp.__eq__", "VocabHelp.__len__", "VocabHelp.extend", "VocabHelp.load_vocab", "VocabHelp.save_vocab", "parse_args", "main", "load_tokens"]}, "chunk_type": "file"}, {"id": "ccd1bb841a102fbd1e97ed0775dcf223", "content": "File: data.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py\n\nElements in this file:\n\n- Module: 1\n\n- Variable: 37\n\n- Function: 6\n\n- Class: 2\n\nClasses: Instance, DataIterator\n\nFunctions: get_spans, get_evaluate_spans, Instance.__init__, load_data_instances, DataIterator.__init__, DataIterator.get_batch", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "element_counts": {"Module": 1, "Variable": 37, "Function": 6, "Class": 2}, "total_elements": 46, "classes": ["Instance", "DataIterator"], "functions": ["get_spans", "get_evaluate_spans", "Instance.__init__", "load_data_instances", "DataIterator.__init__", "DataIterator.get_batch"]}, "chunk_type": "file"}, {"id": "c53e687eb7a80ff9c236d6d3605fa117", "content": "File: main.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py\n\nElements in this file:\n\n- Module: 1\n\n- Function: 4\n\n- Variable: 46\n\nFunctions: get_bert_optimizer, train, eval, test", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "element_counts": {"Module": 1, "Function": 4, "Variable": 46}, "total_elements": 51, "classes": [], "functions": ["get_bert_optimizer", "train", "eval", "test"]}, "chunk_type": "file"}, {"id": "78e40952c4e9794e05e483e32ddf1f6f", "content": "File: utils.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py\n\nElements in this file:\n\n- Module: 1\n\n- Function: 11\n\n- Variable: 28\n\n- Class: 1\n\nClasses: Metric\n\nFunctions: get_aspects, get_opinions, Metric.__init__, Metric.get_spans, Metric.find_pair, Metric.find_triplet, Metric.score_aspect, Metric.score_opinion, Metric.score_uniontags, Metric.score_uniontags_print\n\n... and 1 more functions", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "element_counts": {"Module": 1, "Function": 11, "Variable": 28, "Class": 1}, "total_elements": 41, "classes": ["Metric"], "functions": ["get_aspects", "get_opinions", "Metric.__init__", "Metric.get_spans", "Metric.find_pair", "Metric.find_triplet", "Metric.score_aspect", "Metric.score_opinion", "Metric.score_uniontags", "Metric.score_uniontags_print", "Metric.tagReport"]}, "chunk_type": "file"}, {"id": "ac65c71913eecc8a3b0de81638d9b123", "content": "File: model.py\n\nFull Path: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py\n\nElements in this file:\n\n- Module: 1\n\n- Class: 5\n\n- Function: 10\n\n- Variable: 41\n\nClasses: LayerNorm, RefiningStrategy, GraphConvLayer, Biaffine, EMCGCN\n\nFunctions: LayerNorm.__init__, LayerNorm.forward, RefiningStrategy.__init__, RefiningStrategy.forward, GraphConvLayer.__init__, GraphConvLayer.forward, Biaffine.__init__, Biaffine.forward, EMCGCN.__init__, EMCGCN.forward", "metadata": {"file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "element_counts": {"Module": 1, "Class": 5, "Function": 10, "Variable": 41}, "total_elements": 57, "classes": ["LayerNorm", "RefiningStrategy", "GraphConvLayer", "Biaffine", "EMCGCN"], "functions": ["LayerNorm.__init__", "LayerNorm.forward", "RefiningStrategy.__init__", "RefiningStrategy.forward", "GraphConvLayer.__init__", "GraphConvLayer.forward", "Biaffine.__init__", "Biaffine.forward", "EMCGCN.__init__", "EMCGCN.forward"]}, "chunk_type": "file"}, {"id": "d8956dfcea49cbca2d56754e97dd782a", "content": "Module: code.prepare_vocab\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py\n\nContains 37 elements:\n\nVocabHelp, VocabHelp.__init__, VocabHelp.counter, VocabHelp.words_and_frequencies, VocabHelp.__eq__, VocabHelp.__len__, VocabHelp.extend, VocabHelp.words, VocabHelp.load_vocab, VocabHelp.save_vocab, parse_args, train_file, dev_file, test_file, vocab_tok_file, vocab_post_file, vocab_deprel_file, vocab_postag_file, vocab_synpost_file, token_counter\n\n... and 17 more", "metadata": {"module_name": "code.prepare_vocab", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/prepare_vocab.py", "element_count": 37, "elements": ["VocabHelp", "VocabHelp.__init__", "VocabHelp.counter", "VocabHelp.words_and_frequencies", "VocabHelp.__eq__", "VocabHelp.__len__", "VocabHelp.extend", "VocabHelp.words", "VocabHelp.load_vocab", "VocabHelp.save_vocab", "parse_args", "train_file", "dev_file", "test_file", "vocab_tok_file", "vocab_post_file", "vocab_deprel_file", "vocab_postag_file", "vocab_synpost_file", "token_counter", "deprel_counter", "postag_counter", "postag_ca_counter", "max_len", "post_counter", "syn_post_counter", "token_vocab", "syn_post_vocab", "load_tokens", "tokens", "deprel", "postag", "postag_ca", "sentence", "n", "tmp_pos", "tup"]}, "chunk_type": "module"}, {"id": "3638383965c3b42d258bdc5873a528ed", "content": "Module: code.main\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py\n\nContains 49 elements:\n\nparser, args, post_vocab, deprel_vocab, postag_vocab, instances, get_bert_optimizer, no_decay, diff_part, optimizer_grouped_parameters, optimizer, train, train_sentence_packs, dev_sentence_packs, synpost_vocab, instances_train, instances_dev, trainset, devset, weight\n\n... and 29 more", "metadata": {"module_name": "code.main", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/main.py", "element_count": 49, "elements": ["parser", "args", "post_vocab", "deprel_vocab", "postag_vocab", "instances", "get_bert_optimizer", "no_decay", "diff_part", "optimizer_grouped_parameters", "optimizer", "train", "train_sentence_packs", "dev_sentence_packs", "synpost_vocab", "instances_train", "instances_dev", "trainset", "devset", "weight", "best_joint_f1", "best_joint_epoch", "tags_flatten", "tags_symmetry_flatten", "predictions", "l_ba", "l_rpd", "l_dep", "l_psc", "l_tbd", "l_p", "loss", "preds", "preds_flatten", "model_path", "eval", "all_ids", "all_sentences", "all_preds", "all_labels", "all_lengths", "all_sens_lengths", "all_token_ranges", "metric", "aspect_results", "opinion_results", "test", "sentence_packs", "testset"]}, "chunk_type": "module"}, {"id": "1ff2e024faf2e8d0483dcafc710173ea", "content": "Module: code.data\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py\n\nContains 42 elements:\n\nsentiment2id, get_spans, tags, length, start, get_evaluate_spans, Instance, Instance.__init__, Instance.token_start, Instance.token_end, Instance.aspect, Instance.opinion, Instance.aspect_span, Instance.opinion_span, Instance.start, Instance.end, Instance.set_tag, Instance.tmp, Instance.j, Instance.tmp_dict\n\n... and 22 more", "metadata": {"module_name": "code.data", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/data.py", "element_count": 42, "elements": ["sentiment2id", "get_spans", "tags", "length", "start", "get_evaluate_spans", "Instance", "Instance.__init__", "Instance.token_start", "Instance.token_end", "Instance.aspect", "Instance.opinion", "Instance.aspect_span", "Instance.opinion_span", "Instance.start", "Instance.end", "Instance.set_tag", "Instance.tmp", "Instance.j", "Instance.tmp_dict", "Instance.word_level_degree", "Instance.node_set", "load_data_instances", "tokenizer", "DataIterator", "DataIterator.__init__", "DataIterator.get_batch", "DataIterator.sentence_ids", "DataIterator.sentences", "DataIterator.sens_lens", "DataIterator.token_ranges", "DataIterator.bert_tokens", "DataIterator.lengths", "DataIterator.masks", "DataIterator.aspect_tags", "DataIterator.opinion_tags", "DataIterator.tags", "DataIterator.tags_symmetry", "DataIterator.word_pair_position", "DataIterator.word_pair_deprel", "DataIterator.word_pair_pos", "DataIterator.word_pair_synpost"]}, "chunk_type": "module"}, {"id": "b94458ebfadb2ccc7e5c12e2d1104c84", "content": "Module: code.model\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py\n\nContains 56 elements:\n\nLayerNorm, LayerNorm.__init__, LayerNorm.forward, LayerNorm.mean, LayerNorm.std, RefiningStrategy, RefiningStrategy.__init__, RefiningStrategy.forward, RefiningStrategy.node, RefiningStrategy.edge_diag, RefiningStrategy.edge_i, RefiningStrategy.edge_j, RefiningStrategy.edge, GraphConvLayer, GraphConvLayer.__init__, GraphConvLayer.forward, GraphConvLayer.weight_prob_softmax, GraphConvLayer.gcn_inputs, GraphConvLayer.Ax, GraphConvLayer.gcn_outputs\n\n... and 36 more", "metadata": {"module_name": "code.model", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/model.py", "element_count": 56, "elements": ["LayerNorm", "LayerNorm.__init__", "LayerNorm.forward", "LayerNorm.mean", "LayerNorm.std", "RefiningStrategy", "RefiningStrategy.__init__", "RefiningStrategy.forward", "RefiningStrategy.node", "RefiningStrategy.edge_diag", "RefiningStrategy.edge_i", "RefiningStrategy.edge_j", "RefiningStrategy.edge", "GraphConvLayer", "GraphConvLayer.__init__", "GraphConvLayer.forward", "GraphConvLayer.weight_prob_softmax", "GraphConvLayer.gcn_inputs", "GraphConvLayer.Ax", "GraphConvLayer.gcn_outputs", "GraphConvLayer.weights_gcn_outputs", "GraphConvLayer.node_outputs", "GraphConvLayer.node_outputs1", "GraphConvLayer.node_outputs2", "GraphConvLayer.edge_outputs", "Biaffine", "Biaffine.__init__", "Biaffine.forward", "Biaffine.ones", "Biaffine.input1", "Biaffine.input2", "Biaffine.affine", "Biaffine.biaffine", "EMCGCN", "EMCGCN.__init__", "EMCGCN.forward", "EMCGCN.bert_feature", "EMCGCN.tensor_masks", "EMCGCN.word_pair_post_emb", "EMCGCN.word_pair_deprel_emb", "EMCGCN.word_pair_postag_emb", "EMCGCN.word_pair_synpost_emb", "EMCGCN.ap_node", "EMCGCN.op_node", "EMCGCN.biaffine_edge", "EMCGCN.gcn_input", "EMCGCN.gcn_outputs", "EMCGCN.weight_prob_list", "EMCGCN.biaffine_edge_softmax", "EMCGCN.word_pair_post_emb_softmax", "EMCGCN.word_pair_deprel_emb_softmax", "EMCGCN.word_pair_postag_emb_softmax", "EMCGCN.word_pair_synpost_emb_softmax", "EMCGCN.self_loop", "EMCGCN.weight_prob", "EMCGCN.weight_prob_softmax"]}, "chunk_type": "module"}, {"id": "dcece1c7cd665ad2221843cae4ca71cc", "content": "Module: code.utils\n\nFile: /home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py\n\nContains 40 elements:\n\nlabel, spans, get_aspects, end, get_opinions, Metric, Metric.__init__, Metric.get_spans, Metric.spans, Metric.start, Metric.find_pair, Metric.pairs, Metric.tag_num, Metric.a_start, Metric.o_start, Metric.sentiment, Metric.find_triplet, Metric.triplets_utm, Metric.score_aspect, Metric.golden_set\n\n... and 20 more", "metadata": {"module_name": "code.utils", "file_path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE/code/utils.py", "element_count": 40, "elements": ["label", "spans", "get_aspects", "end", "get_opinions", "Metric", "Metric.__init__", "Metric.get_spans", "Metric.spans", "Metric.start", "Metric.find_pair", "Metric.pairs", "Metric.tag_num", "Metric.a_start", "Metric.o_start", "Metric.sentiment", "Metric.find_triplet", "Metric.triplets_utm", "Metric.score_aspect", "Metric.golden_set", "Metric.predicted_set", "Metric.golden_aspect_spans", "Metric.predicted_aspect_spans", "Metric.correct_num", "Metric.precision", "Metric.recall", "Metric.f1", "Metric.score_opinion", "Metric.golden_opinion_spans", "Metric.predicted_opinion_spans", "Metric.score_uniontags", "Metric.golden_tuples", "Metric.predicted_tuples", "Metric.score_uniontags_print", "Metric.all_golden_triplets", "Metric.all_predicted_triplets", "Metric.tagReport", "Metric.golden_tags", "Metric.predict_tags", "Metric.target_names"]}, "chunk_type": "module"}], "repository_metadata": {"name": "EMCGCN-ASTE", "path": "/home/<USER>/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/EMCGCN-ASTE", "processed_at": "2025-08-21T11:52:02.271184", "statistics": {"total_files": 5, "total_elements": 229, "modules": 5, "classes": 9, "functions": 39, "variables": 176}}}