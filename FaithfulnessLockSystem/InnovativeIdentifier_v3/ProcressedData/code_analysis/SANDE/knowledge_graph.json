{"nodes": [{"id": "attack_llm_core_base", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_base.py", "metadata": {}}, {"id": "parser", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "args", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "model_path", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "behavior_config", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "user_prompt", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "num_steps", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "adv_string_init", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "target", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "template_name", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "device", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "batch_size", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "topk", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "allow_non_ascii", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "test_prefixes", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "conv_template", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"in_function": "load_conversation_template"}}, {"id": "suffix_manager", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "generate", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {"arguments": ["model", "tokenizer", "input_ids", "assistant_role_slice", "gen_config"]}}, {"id": "gen_config", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {"in_function": "generate"}}, {"id": "input_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "attn_masks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {"in_function": "generate"}}, {"id": "output_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {"in_function": "generate"}}, {"id": "check_for_attack_success", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {"arguments": ["model", "tokenizer", "input_ids", "assistant_role_slice", "test_prefixes", "gen_config"]}}, {"id": "gen_str", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {"in_function": "check_for_attack_success"}}, {"id": "jailbroken", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {"in_function": "check_for_attack_success"}}, {"id": "not_allowed_tokens", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "adv_suffix", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "generations", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "log_dict", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "current_tcs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "temp", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "v2_success_counter", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "coordinate_grad", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "adv_suffix_tokens", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "new_adv_suffix_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "new_adv_suffix", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "losses", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "best_new_adv_suffix_id", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "best_new_adv_suffix", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "current_loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "log_entry", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "submission_json_file", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "log_json_file", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "run_single_attack_base", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {}}, {"id": "timestamp", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "BEHAVIOR_ID", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {}}, {"id": "DEVICE", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {}}, {"id": "OUTPUT_PATH", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {}}, {"id": "stream_reader", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {"arguments": ["pipe", "label"]}}, {"id": "run_single_process", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {"arguments": ["behavior_id", "device", "output_path", "defense", "behaviors_config"]}}, {"id": "command", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {"in_function": "run_single_process"}}, {"id": "process", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {"in_function": "run_single_process"}}, {"id": "stdout_thread", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {"in_function": "run_single_process"}}, {"id": "stderr_thread", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_single_attack_base.py", "metadata": {"in_function": "run_single_process"}}, {"id": "main", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/main.py", "metadata": {}}, {"id": "print_hi", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/main.py", "metadata": {"arguments": ["name"]}}, {"id": "attack_llm_core_best_update_our_target", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "incremental_token_num", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "previous_update_k_loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "k", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "idx", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "ori_adv_suffix_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "adv_suffix_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "best_new_adv_suffix_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "all_new_adv_suffix", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "temp_new_adv_suffix", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "temp_new_adv_suffix_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/attack_llm_core_best_update_our_target.py", "metadata": {}}, {"id": "run_multiple_attack_our_target", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "device_list", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "defense", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "output_path", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "behaviors_config", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "behavior_id_list", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "Card", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "Card.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {"arguments": ["self", "id"], "in_class": "Card", "is_constructor": true}}, {"id": "ResourceManager", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "ResourceManager.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {"arguments": ["self", "device_list"], "in_class": "ResourceManager", "is_constructor": true}}, {"id": "ResourceManager.request_card", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {"arguments": ["self"], "in_class": "ResourceManager"}}, {"id": "ResourceManager.release_card", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {"arguments": ["self", "card"], "in_class": "ResourceManager"}}, {"id": "worker_task", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {"arguments": ["task_list", "resource_manager"]}}, {"id": "task", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {"in_function": "worker_task"}}, {"id": "card", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {"in_function": "worker_task"}}, {"id": "tasks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "task_list_lock", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "resource_manager", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "threads", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/run_multiple_attack_our_target.py", "metadata": {}}, {"id": "generate_our_config", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/generate_our_config.py", "metadata": {}}, {"id": "fcc_data", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/generate_our_config.py", "metadata": {}}, {"id": "new_target", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/generate_our_config.py", "metadata": {}}, {"id": "ori_target", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/generate_our_config.py", "metadata": {}}, {"id": "__init__", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/__init__.py", "metadata": {}}, {"id": "__version__", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/__init__.py", "metadata": {}}, {"id": "gcg_attack", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {}}, {"id": "token_gradients", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"arguments": ["model", "input_ids", "input_slice", "target_slice", "loss_slice"]}}, {"id": "embed_weights", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "token_gradients"}}, {"id": "one_hot", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "token_gradients"}}, {"id": "input_embeds", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "token_gradients"}}, {"id": "embeds", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "token_gradients"}}, {"id": "full_embeds", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "token_gradients"}}, {"id": "logits", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "forward"}}, {"id": "targets", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "token_gradients"}}, {"id": "loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "target_loss"}}, {"id": "GCGAttackPrompt", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {}}, {"id": "GCGAttackPrompt.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"arguments": ["self"], "in_class": "GCGAttackPrompt", "is_constructor": true}}, {"id": "GCGAttackPrompt.grad", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"arguments": ["self", "model"], "in_class": "GCGAttackPrompt"}}, {"id": "GCGPromptManager", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {}}, {"id": "GCGPromptManager.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"arguments": ["self"], "in_class": "GCGPromptManager", "is_constructor": true}}, {"id": "GCGPromptManager.sample_control", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"arguments": ["self", "grad", "batch_size", "topk", "temp", "allow_non_ascii"], "in_class": "GCGPromptManager"}}, {"id": "GCGPromptManager.top_indices", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGPromptManager", "in_function": "GCGPromptManager.sample_control"}}, {"id": "GCGPromptManager.control_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGPromptManager", "in_function": "GCGPromptManager.sample_control"}}, {"id": "GCGPromptManager.original_control_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGPromptManager", "in_function": "GCGPromptManager.sample_control"}}, {"id": "GCGPromptManager.new_token_pos", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGPromptManager", "in_function": "GCGPromptManager.sample_control"}}, {"id": "GCGPromptManager.new_token_val", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGPromptManager", "in_function": "GCGPromptManager.sample_control"}}, {"id": "GCGPromptManager.new_control_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGPromptManager", "in_function": "GCGPromptManager.sample_control"}}, {"id": "GCGMultiPromptAttack", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {}}, {"id": "GCGMultiPromptAttack.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"arguments": ["self"], "in_class": "GCGMultiPromptAttack", "is_constructor": true}}, {"id": "GCGMultiPromptAttack.step", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"arguments": ["self", "batch_size", "topk", "temp", "allow_non_ascii", "target_weight", "control_weight", "verbose", "opt_only", "filter_cand"], "in_class": "GCGMultiPromptAttack"}}, {"id": "GCGMultiPromptAttack.opt_only", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.main_device", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.control_cands", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.grad", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.new_grad", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.control_cand", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.progress", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.min_idx", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.model_idx", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "GCGMultiPromptAttack.batch_idx", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/gcg/gcg_attack.py", "metadata": {"in_class": "GCGMultiPromptAttack", "in_function": "GCGMultiPromptAttack.step"}}, {"id": "opt_utils", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {}}, {"id": "grad", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "token_gradients"}}, {"id": "sample_control", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"arguments": ["control_toks", "grad", "batch_size", "topk", "temp", "not_allowed_tokens"]}}, {"id": "top_indices", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "sample_control"}}, {"id": "control_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "sample_control"}}, {"id": "original_control_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "sample_control"}}, {"id": "new_token_pos", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "sample_control"}}, {"id": "new_token_val", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "sample_control"}}, {"id": "new_control_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "sample_control"}}, {"id": "get_filtered_cands", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"arguments": ["tokenizer", "control_cand", "filter_cand", "curr_control"]}}, {"id": "decoded_str", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_filtered_cands"}}, {"id": "cands", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_filtered_cands"}}, {"id": "encoded", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_filtered_cands"}}, {"id": "get_logits", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"arguments": []}}, {"id": "max_len", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_logits"}}, {"id": "test_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_logits"}}, {"id": "pad_tok", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_logits"}}, {"id": "nested_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_logits"}}, {"id": "locs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_logits"}}, {"id": "ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_logits"}}, {"id": "attn_mask", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "get_logits"}}, {"id": "forward", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"arguments": []}}, {"id": "batch_input_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "forward"}}, {"id": "batch_attention_mask", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "forward"}}, {"id": "target_loss", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"arguments": ["logits", "ids", "target_slice"]}}, {"id": "crit", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "target_loss"}}, {"id": "loss_slice", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "target_loss"}}, {"id": "load_model_and_tokenizer", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"arguments": ["model_path", "tokenizer_path", "device"]}}, {"id": "model", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "load_model_and_tokenizer"}}, {"id": "tokenizer_path", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/opt_utils.py", "metadata": {"in_function": "load_model_and_tokenizer"}}, {"id": "tokenizer", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_workers"}}, {"id": "string_utils", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {}}, {"id": "load_conversation_template", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"arguments": ["template_name"]}}, {"id": "SuffixManager", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {}}, {"id": "SuffixManager.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"arguments": ["self"], "in_class": "SuffixManager", "is_constructor": true}}, {"id": "SuffixManager.get_prompt", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"arguments": ["self", "adv_string"], "in_class": "SuffixManager"}}, {"id": "SuffixManager.prompt", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"in_class": "SuffixManager", "in_function": "SuffixManager.get_input_ids"}}, {"id": "SuffixManager.encoding", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"in_class": "SuffixManager", "in_function": "SuffixManager.get_prompt"}}, {"id": "SuffixManager.toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"in_class": "SuffixManager", "in_function": "SuffixManager.get_input_ids"}}, {"id": "SuffixManager.separator", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"in_class": "SuffixManager", "in_function": "SuffixManager.get_prompt"}}, {"id": "SuffixManager.python_tokenizer", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"in_class": "SuffixManager", "in_function": "SuffixManager.get_prompt"}}, {"id": "SuffixManager.get_input_ids", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"arguments": ["self", "adv_string"], "in_class": "SuffixManager"}}, {"id": "SuffixManager.input_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/minimal_gcg/string_utils.py", "metadata": {"in_class": "SuffixManager", "in_function": "SuffixManager.get_input_ids"}}, {"id": "attack_manager", "type": "<PERSON><PERSON><PERSON>", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "NpEncoder", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "NpEncoder.default", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "obj"], "in_class": "NpEncoder"}}, {"id": "get_embedding_layer", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["model"]}}, {"id": "get_embedding_matrix", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["model"]}}, {"id": "get_embeddings", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["model", "input_ids"]}}, {"id": "get_nonascii_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["tokenizer", "device"]}}, {"id": "is_ascii", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["s"]}}, {"id": "ascii_toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_nonascii_toks"}}, {"id": "AttackPrompt", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "AttackPrompt.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "goal", "target", "tokenizer", "conv_template", "control_init", "test_prefixes"], "in_class": "AttackPrompt", "is_constructor": true}}, {"id": "AttackPrompt._update_ids", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.prompt", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.encoding", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt._update_ids"}}, {"id": "AttackPrompt.toks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt._update_ids"}}, {"id": "AttackPrompt.separator", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt._update_ids"}}, {"id": "AttackPrompt.python_tokenizer", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt._update_ids"}}, {"id": "AttackPrompt.generate", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model", "gen_config"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.gen_config", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.test"}}, {"id": "AttackPrompt.input_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.generate"}}, {"id": "AttackPrompt.attn_masks", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.generate"}}, {"id": "AttackPrompt.output_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.generate"}}, {"id": "AttackPrompt.generate_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model", "gen_config"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.test", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model", "gen_config"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.gen_str", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.test"}}, {"id": "AttackPrompt.jailbroken", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.test"}}, {"id": "AttackPrompt.em", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.test"}}, {"id": "AttackPrompt.test_loss", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.grad", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.logits", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.pad_tok", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.test_controls", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.test_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.max_len", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.nested_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.locs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.attn_mask", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.logits"}}, {"id": "AttackPrompt.target_loss", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "logits", "ids"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.crit", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.control_loss"}}, {"id": "AttackPrompt.loss_slice", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.control_loss"}}, {"id": "AttackPrompt.loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "AttackPrompt", "in_function": "AttackPrompt.control_loss"}}, {"id": "AttackPrompt.control_loss", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "logits", "ids"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.assistant_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.assistant_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.goal_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "goal"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.goal_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.target_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "target"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.target_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.control_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "control"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.control_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "control_toks"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.input_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.input_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "AttackPrompt.eval_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "AttackPrompt"}}, {"id": "PromptManager", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "PromptManager.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "goals", "targets", "tokenizer", "conv_template", "control_init", "test_prefixes", "managers"], "in_class": "PromptManager", "is_constructor": true}}, {"id": "PromptManager.generate", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model", "gen_config"], "in_class": "PromptManager"}}, {"id": "PromptManager.gen_config", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "PromptManager", "in_function": "PromptManager.generate"}}, {"id": "PromptManager.generate_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model", "gen_config"], "in_class": "PromptManager"}}, {"id": "PromptManager.test", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model", "gen_config"], "in_class": "PromptManager"}}, {"id": "PromptManager.test_loss", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model"], "in_class": "PromptManager"}}, {"id": "PromptManager.grad", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model"], "in_class": "PromptManager"}}, {"id": "PromptManager.logits", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model", "test_controls", "return_ids"], "in_class": "PromptManager"}}, {"id": "PromptManager.vals", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "PromptManager", "in_function": "PromptManager.logits"}}, {"id": "PromptManager.target_loss", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "logits", "ids"], "in_class": "PromptManager"}}, {"id": "PromptManager.control_loss", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "logits", "ids"], "in_class": "PromptManager"}}, {"id": "PromptManager.sample_control", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "PromptManager"}}, {"id": "PromptManager.__len__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "PromptManager"}}, {"id": "PromptManager.__getitem__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "i"], "in_class": "PromptManager"}}, {"id": "PromptManager.__iter__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "PromptManager"}}, {"id": "PromptManager.control_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "control"], "in_class": "PromptManager"}}, {"id": "PromptManager.control_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "control_toks"], "in_class": "PromptManager"}}, {"id": "PromptManager.disallowed_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "PromptManager"}}, {"id": "MultiPromptAttack", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "MultiPromptAttack.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "goals", "targets", "workers", "control_init", "test_prefixes", "logfile", "managers", "test_goals", "test_targets", "test_workers"], "in_class": "MultiPromptAttack", "is_constructor": true}}, {"id": "MultiPromptAttack.control_str", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "control"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.control_toks", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "control"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.get_filtered_cands", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "worker_index", "control_cand", "filter_cand", "curr_control"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.worker", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.get_filtered_cands"}}, {"id": "MultiPromptAttack.decoded_str", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.get_filtered_cands"}}, {"id": "MultiPromptAttack.cands", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.get_filtered_cands"}}, {"id": "MultiPromptAttack.step", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.run", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "n_steps", "batch_size", "topk", "temp", "allow_non_ascii", "target_weight", "control_weight", "anneal", "anneal_from", "prev_loss", "stop_on_success", "test_steps", "log_first", "filter_cand", "verbose"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.P", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["e", "e_prime", "k"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.T", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.P"}}, {"id": "MultiPromptAttack.target_weight_fn", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.control_weight_fn", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.steps", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.best_loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.best_control", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.runtime", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.model_tests", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.test"}}, {"id": "MultiPromptAttack.start", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.keep_control", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.prev_loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.last_control", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.run"}}, {"id": "MultiPromptAttack.test", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "workers", "prompts", "include_loss"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.model_tests_jb", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.test"}}, {"id": "MultiPromptAttack.model_tests_mb", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.test"}}, {"id": "MultiPromptAttack.model_tests_loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.test"}}, {"id": "MultiPromptAttack.test_all", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.all_workers", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.all_prompts", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.test_all"}}, {"id": "MultiPromptAttack.parse_results", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "results"], "in_class": "MultiPromptAttack"}}, {"id": "MultiPromptAttack.x", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.parse_results"}}, {"id": "MultiPromptAttack.i", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.parse_results"}}, {"id": "MultiPromptAttack.id_id", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.parse_results"}}, {"id": "MultiPromptAttack.id_od", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.parse_results"}}, {"id": "MultiPromptAttack.od_id", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.parse_results"}}, {"id": "MultiPromptAttack.od_od", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.parse_results"}}, {"id": "MultiPromptAttack.log", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.all_goal_strs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.tests", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.n_passed", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.n_em", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.n_loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.total_tests", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "MultiPromptAttack.output_str", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "MultiPromptAttack", "in_function": "MultiPromptAttack.log"}}, {"id": "ProgressiveMultiPromptAttack", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "ProgressiveMultiPromptAttack.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "goals", "targets", "workers", "progressive_goals", "progressive_models", "control_init", "test_prefixes", "logfile", "managers", "test_goals", "test_targets", "test_workers"], "in_class": "ProgressiveMultiPromptAttack", "is_constructor": true}}, {"id": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": [], "in_class": "ProgressiveMultiPromptAttack"}}, {"id": "ProgressiveMultiPromptAttack.mpa_kwargs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.filter_mpa_kwargs"}}, {"id": "ProgressiveMultiPromptAttack.run", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "n_steps", "batch_size", "topk", "temp", "allow_non_ascii", "target_weight", "control_weight", "anneal", "test_steps", "incr_control", "stop_on_success", "verbose", "filter_cand"], "in_class": "ProgressiveMultiPromptAttack"}}, {"id": "ProgressiveMultiPromptAttack.log", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "ProgressiveMultiPromptAttack.num_goals", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "ProgressiveMultiPromptAttack.num_workers", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "ProgressiveMultiPromptAttack.step", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "ProgressiveMultiPromptAttack.stop_inner_on_success", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "ProgressiveMultiPromptAttack.loss", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "ProgressiveMultiPromptAttack.attack", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "ProgressiveMultiPromptAttack.model_tests", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ProgressiveMultiPromptAttack", "in_function": "ProgressiveMultiPromptAttack.run"}}, {"id": "IndividualPromptAttack", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "IndividualPromptAttack.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "goals", "targets", "workers", "control_init", "test_prefixes", "logfile", "managers", "test_goals", "test_targets", "test_workers"], "in_class": "IndividualPromptAttack", "is_constructor": true}}, {"id": "IndividualPromptAttack.filter_mpa_kwargs", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": [], "in_class": "IndividualPromptAttack"}}, {"id": "IndividualPromptAttack.mpa_kwargs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "IndividualPromptAttack", "in_function": "IndividualPromptAttack.filter_mpa_kwargs"}}, {"id": "IndividualPromptAttack.run", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "n_steps", "batch_size", "topk", "temp", "allow_non_ascii", "target_weight", "control_weight", "anneal", "test_steps", "incr_control", "stop_on_success", "verbose", "filter_cand"], "in_class": "IndividualPromptAttack"}}, {"id": "IndividualPromptAttack.log", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "IndividualPromptAttack", "in_function": "IndividualPromptAttack.run"}}, {"id": "IndividualPromptAttack.stop_inner_on_success", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "IndividualPromptAttack", "in_function": "IndividualPromptAttack.run"}}, {"id": "IndividualPromptAttack.attack", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "IndividualPromptAttack", "in_function": "IndividualPromptAttack.run"}}, {"id": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "EvaluateAttack.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "goals", "targets", "workers", "control_init", "test_prefixes", "logfile", "managers", "test_goals", "test_targets", "test_workers"], "in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "is_constructor": true}}, {"id": "EvaluateAttack.filter_mpa_kwargs", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": [], "in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>"}}, {"id": "EvaluateAttack.mpa_kwargs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.filter_mpa_kwargs"}}, {"id": "EvaluateAttack.run", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "steps", "controls", "batch_size", "max_new_len", "verbose"], "in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>"}}, {"id": "EvaluateAttack.log", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.prev_control", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.attack", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.all_inputs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.max_new_tokens", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.targets", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.all_outputs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.batch", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.batch_max_new", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.batch_inputs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.batch_input_ids", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.batch_attention_mask", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.outputs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.batch_outputs", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.gen_start_idx", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>.jailbroken", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "EvaluateAttack.em", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "in_function": "EvaluateAttack.run"}}, {"id": "ModelWorker", "type": "Class", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {}}, {"id": "ModelWorker.__init__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "model_path", "model_kwargs", "tokenizer", "conv_template", "device"], "in_class": "ModelWorker", "is_constructor": true}}, {"id": "ModelWorker.run", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["model", "tasks", "results"], "in_class": "ModelWorker"}}, {"id": "ModelWorker.task", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_class": "ModelWorker", "in_function": "ModelWorker.run"}}, {"id": "ModelWorker.start", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "ModelWorker"}}, {"id": "ModelWorker.stop", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self"], "in_class": "ModelWorker"}}, {"id": "ModelWorker.__call__", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["self", "ob", "fn"], "in_class": "ModelWorker"}}, {"id": "get_workers", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["params", "eval"]}}, {"id": "tokenizers", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_workers"}}, {"id": "raw_conv_templates", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_workers"}}, {"id": "conv_templates", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_workers"}}, {"id": "workers", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_workers"}}, {"id": "num_train_models", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_workers"}}, {"id": "get_goals_and_targets", "type": "Function", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"arguments": ["params"]}}, {"id": "train_goals", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_goals_and_targets"}}, {"id": "train_targets", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_goals_and_targets"}}, {"id": "test_goals", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_goals_and_targets"}}, {"id": "test_targets", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_goals_and_targets"}}, {"id": "offset", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_goals_and_targets"}}, {"id": "train_data", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_goals_and_targets"}}, {"id": "test_data", "type": "Variable", "file_path": "/data2/fangye/FaithfulnessLockSystem/InnovativeIdentifier_v3/data/repo/I-GCG/llm_attacks/base/attack_manager.py", "metadata": {"in_function": "get_goals_and_targets"}}], "edges": [{"source": "run_multiple_attack_our_target", "target": "run_single_attack_base", "type": "imports_from", "metadata": {"imported_name": "run_single_process", "line": 5}}, {"source": "GCGAttackPrompt", "target": "AttackPrompt", "type": "inherits_from", "metadata": {}}, {"source": "GCGAttackPrompt", "target": "GCGAttackPrompt.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GCGAttackPrompt", "target": "GCGAttackPrompt.grad", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GCGPromptManager", "target": "PromptManager", "type": "inherits_from", "metadata": {}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.sample_control", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.top_indices", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.control_toks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.original_control_toks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.new_token_pos", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.new_token_val", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGPromptManager", "target": "GCGPromptManager.new_control_toks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "MultiPromptAttack", "type": "inherits_from", "metadata": {}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.step", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.opt_only", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.main_device", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.control_cands", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.grad", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.new_grad", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.control_cand", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.progress", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.min_idx", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.model_idx", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "GCGMultiPromptAttack", "target": "GCGMultiPromptAttack.batch_idx", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "NpEncoder", "target": "NpEncoder.default", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt._update_ids", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.prompt", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.encoding", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.toks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.separator", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.python_tokenizer", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.generate", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.gen_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.input_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.attn_masks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.output_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.generate_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.test", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.gen_str", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.jailbroken", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.em", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.test_loss", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.grad", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.logits", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.pad_tok", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.test_controls", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.test_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.max_len", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.nested_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.locs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.attn_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.target_loss", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.crit", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.loss_slice", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "AttackPrompt", "target": "AttackPrompt.control_loss", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.assistant_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.assistant_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.goal_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.goal_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.target_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.target_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.control_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.control_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.input_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.input_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "AttackPrompt", "target": "AttackPrompt.eval_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.generate", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.gen_config", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "PromptManager", "target": "PromptManager.generate_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.test", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.test_loss", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.grad", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.logits", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.vals", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "PromptManager", "target": "PromptManager.target_loss", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.control_loss", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.sample_control", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.__len__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.__getitem__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.__iter__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.control_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.control_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "PromptManager", "target": "PromptManager.disallowed_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.control_str", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.control_toks", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.get_filtered_cands", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.worker", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.decoded_str", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.cands", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.step", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.run", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.P", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.T", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.target_weight_fn", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.control_weight_fn", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.steps", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.best_loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.best_control", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.runtime", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.model_tests", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.start", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.keep_control", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.prev_loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.last_control", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.test", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.model_tests_jb", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.model_tests_mb", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.model_tests_loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.test_all", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.all_workers", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.all_prompts", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.parse_results", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.x", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.i", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.id_id", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.id_od", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.od_id", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.od_od", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.log", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.all_goal_strs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.tests", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.n_passed", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.n_em", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.n_loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.total_tests", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "MultiPromptAttack", "target": "MultiPromptAttack.output_str", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.mpa_kwargs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.run", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.log", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.num_goals", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.num_workers", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.step", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.stop_inner_on_success", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.loss", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.attack", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ProgressiveMultiPromptAttack", "target": "ProgressiveMultiPromptAttack.model_tests", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "IndividualPromptAttack", "target": "IndividualPromptAttack.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "IndividualPromptAttack", "target": "IndividualPromptAttack.filter_mpa_kwargs", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "IndividualPromptAttack", "target": "IndividualPromptAttack.mpa_kwargs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "IndividualPromptAttack", "target": "IndividualPromptAttack.run", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "IndividualPromptAttack", "target": "IndividualPromptAttack.log", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "IndividualPromptAttack", "target": "IndividualPromptAttack.stop_inner_on_success", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "IndividualPromptAttack", "target": "IndividualPromptAttack.attack", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.filter_mpa_kwargs", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.mpa_kwargs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.run", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.log", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.prev_control", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.attack", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.all_inputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.max_new_tokens", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.targets", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.all_outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.batch", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.batch_max_new", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.batch_inputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.batch_input_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.batch_attention_mask", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.batch_outputs", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.gen_start_idx", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>.jailbroken", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "target": "EvaluateAttack.em", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ModelWorker", "target": "ModelWorker.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ModelWorker", "target": "ModelWorker.run", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ModelWorker", "target": "ModelWorker.task", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "ModelWorker", "target": "ModelWorker.start", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ModelWorker", "target": "ModelWorker.stop", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ModelWorker", "target": "ModelWorker.__call__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "check_for_attack_success", "target": "generate", "type": "calls", "metadata": {}}, {"source": "check_for_attack_success", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "check_for_attack_success", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "check_for_attack_success", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "check_for_attack_success", "target": "AttackPrompt.gen_str", "type": "uses_variable", "metadata": {}}, {"source": "check_for_attack_success", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>.jailbroken", "type": "uses_variable", "metadata": {}}, {"source": "check_for_attack_success", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "check_for_attack_success", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "check_for_attack_success", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "check_for_attack_success", "target": "AttackPrompt.test", "type": "similar_to", "metadata": {"similarity": 0.4375}}, {"source": "check_for_attack_success", "target": "PromptManager.generate_str", "type": "similar_to", "metadata": {"similarity": 0.43478260869565216}}, {"source": "run_single_process", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "command", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "process", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "stdout_thread", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "stderr_thread", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "defense", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "output_path", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "behaviors_config", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "threads", "type": "uses_variable", "metadata": {}}, {"source": "run_single_process", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "run_single_process", "type": "calls", "metadata": {}}, {"source": "worker_task", "target": "defense", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "output_path", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "behaviors_config", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "ModelWorker.task", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "card", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "tasks", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "task_list_lock", "type": "uses_variable", "metadata": {}}, {"source": "worker_task", "target": "resource_manager", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "get_embeddings", "type": "calls", "metadata": {}}, {"source": "token_gradients", "target": "get_embedding_matrix", "type": "calls", "metadata": {}}, {"source": "token_gradients", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "embed_weights", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "one_hot", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "input_embeds", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "embeds", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "full_embeds", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "ProgressiveMultiPromptAttack.loss", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "AttackPrompt.loss_slice", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "token_gradients", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "GCGAttackPrompt.grad", "target": "token_gradients", "type": "calls", "metadata": {}}, {"source": "GCGAttackPrompt.grad", "target": "GCGAttackPrompt", "type": "member_of", "metadata": {}}, {"source": "GCGAttackPrompt.grad", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "GCGAttackPrompt.grad", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "GCGAttackPrompt.grad", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "GCGAttackPrompt.grad", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "GCGAttackPrompt.grad", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.38461538461538464}}, {"source": "GCGAttackPrompt.grad", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "GCGMultiPromptAttack.step", "target": "target_loss", "type": "calls", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "get_filtered_cands", "type": "calls", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "sample_control", "type": "calls", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "topk", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "allow_non_ascii", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "temp", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "k", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "ProgressiveMultiPromptAttack.loss", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.opt_only", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.main_device", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.control_cands", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.new_grad", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.control_cand", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.progress", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.min_idx", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.model_idx", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "GCGMultiPromptAttack.batch_idx", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "ProgressiveMultiPromptAttack.step", "type": "uses_variable", "metadata": {}}, {"source": "GCGMultiPromptAttack.step", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "forward", "type": "calls", "metadata": {}}, {"source": "get_logits", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.max_len", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.test_ids", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.pad_tok", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.nested_ids", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.locs", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.attn_mask", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "AttackPrompt.test_controls", "type": "uses_variable", "metadata": {}}, {"source": "get_logits", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_input_ids", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.get_input_ids", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_input_ids", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_input_ids", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_input_ids", "target": "AttackPrompt.toks", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_input_ids", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.3684210526315789}}, {"source": "SuffixManager.get_input_ids", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "get_nonascii_toks", "target": "is_ascii", "type": "calls", "metadata": {}}, {"source": "get_nonascii_toks", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "get_nonascii_toks", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "get_nonascii_toks", "target": "ascii_toks", "type": "uses_variable", "metadata": {}}, {"source": "get_nonascii_toks", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.__init__", "target": "PromptManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.5543478260869565}}, {"source": "AttackPrompt.__init__", "target": "MultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.47126436781609193}}, {"source": "AttackPrompt.__init__", "target": "ProgressiveMultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.35964912280701755}}, {"source": "AttackPrompt.__init__", "target": "IndividualPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.38317757009345793}}, {"source": "AttackPrompt.__init__", "target": "EvaluateAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.37272727272727274}}, {"source": "AttackPrompt._update_ids", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "AttackPrompt.encoding", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "AttackPrompt.toks", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "AttackPrompt.separator", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "AttackPrompt.python_tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "tokenizers", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt._update_ids", "target": "SuffixManager.get_prompt", "type": "similar_to", "metadata": {"similarity": 0.863013698630137}}, {"source": "AttackPrompt.generate_str", "target": "generate", "type": "calls", "metadata": {}}, {"source": "AttackPrompt.generate_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.generate_str", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate_str", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate_str", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate_str", "target": "check_for_attack_success", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "AttackPrompt.generate_str", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "AttackPrompt.generate_str", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.35714285714285715}}, {"source": "AttackPrompt.generate_str", "target": "AttackPrompt.control_toks", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.generate_str", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.4166666666666667}}, {"source": "AttackPrompt.generate_str", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "AttackPrompt.generate_str", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.3684210526315789}}, {"source": "AttackPrompt.generate_str", "target": "PromptManager.generate_str", "type": "similar_to", "metadata": {"similarity": 0.7692307692307693}}, {"source": "AttackPrompt.generate_str", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "AttackPrompt.test", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.test", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "AttackPrompt.gen_str", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>.jailbroken", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "EvaluateAttack.em", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "EvaluateAttack.max_new_tokens", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test", "target": "check_for_attack_success", "type": "similar_to", "metadata": {"similarity": 0.4375}}, {"source": "AttackPrompt.test", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "AttackPrompt.test", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.32142857142857145}}, {"source": "AttackPrompt.test_loss", "target": "logits", "type": "calls", "metadata": {}}, {"source": "AttackPrompt.test_loss", "target": "target_loss", "type": "calls", "metadata": {}}, {"source": "AttackPrompt.test_loss", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.test_loss", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test_loss", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test_loss", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.test_loss", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "AttackPrompt.goal_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.goal_str", "target": "AttackPrompt.target_str", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.goal_str", "target": "AttackPrompt.control_str", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.goal_str", "target": "AttackPrompt.control_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.target_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.target_str", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_str", "target": "AttackPrompt.goal_str", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.target_str", "target": "AttackPrompt.control_str", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.target_str", "target": "AttackPrompt.control_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.control_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.control_str", "target": "AttackPrompt.goal_str", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.control_str", "target": "AttackPrompt.target_str", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.control_str", "target": "AttackPrompt.control_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.control_str", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "AttackPrompt.control_str", "target": "MultiPromptAttack.control_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.control_toks", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.control_toks", "target": "control_toks", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_toks", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_toks", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.control_toks", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.control_toks", "target": "AttackPrompt.goal_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.control_toks", "target": "AttackPrompt.target_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.control_toks", "target": "AttackPrompt.control_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.control_toks", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.__init__", "target": "get_nonascii_toks", "type": "calls", "metadata": {}}, {"source": "PromptManager.__init__", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__init__", "target": "AttackPrompt.__init__", "type": "similar_to", "metadata": {"similarity": 0.5543478260869565}}, {"source": "PromptManager.__init__", "target": "MultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.5252525252525253}}, {"source": "PromptManager.__init__", "target": "ProgressiveMultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.424}}, {"source": "PromptManager.__init__", "target": "IndividualPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.4491525423728814}}, {"source": "PromptManager.__init__", "target": "EvaluateAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.4380165289256198}}, {"source": "PromptManager.generate_str", "target": "generate", "type": "calls", "metadata": {}}, {"source": "PromptManager.generate_str", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.generate_str", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.generate_str", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.generate_str", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.generate_str", "target": "check_for_attack_success", "type": "similar_to", "metadata": {"similarity": 0.43478260869565216}}, {"source": "PromptManager.generate_str", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.7692307692307693}}, {"source": "PromptManager.generate_str", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.generate_str", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.45}}, {"source": "PromptManager.generate_str", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.generate_str", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.generate_str", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.35294117647058826}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "topk", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "allow_non_ascii", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "temp", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "k", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.loss", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.T", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.target_weight_fn", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.control_weight_fn", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.steps", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.best_loss", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.best_control", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.runtime", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.model_tests", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.keep_control", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.prev_loss", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.last_control", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.model_tests_jb", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.model_tests_mb", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "EvaluateAttack.log", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "MultiPromptAttack.tests", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.step", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.run", "type": "similar_to", "metadata": {"similarity": 0.30177514792899407}}, {"source": "MultiPromptAttack.test_all", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "MultiPromptAttack.all_workers", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "MultiPromptAttack.all_prompts", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test_all", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "model_path", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "losses", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "GCGMultiPromptAttack.progress", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "tokenizer_path", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "MultiPromptAttack.tests", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "EvaluateAttack.mpa_kwargs", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "AttackPrompt.__init__", "type": "similar_to", "metadata": {"similarity": 0.35964912280701755}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "PromptManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.424}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "MultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.6804123711340206}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "IndividualPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.8865979381443299}}, {"source": "ProgressiveMultiPromptAttack.__init__", "target": "EvaluateAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.8415841584158416}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "topk", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "allow_non_ascii", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "temp", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "process", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.loss", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "MultiPromptAttack.steps", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.model_tests", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "MultiPromptAttack.prev_loss", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "EvaluateAttack.log", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "MultiPromptAttack.tests", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "EvaluateAttack.mpa_kwargs", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.num_goals", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.num_workers", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "ProgressiveMultiPromptAttack.step", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "IndividualPromptAttack.stop_inner_on_success", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "MultiPromptAttack.run", "type": "similar_to", "metadata": {"similarity": 0.30177514792899407}}, {"source": "ProgressiveMultiPromptAttack.run", "target": "IndividualPromptAttack.run", "type": "similar_to", "metadata": {"similarity": 0.7338129496402878}}, {"source": "IndividualPromptAttack.__init__", "target": "IndividualPromptAttack", "type": "member_of", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "model_path", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "losses", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "tokenizer_path", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "MultiPromptAttack.tests", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.__init__", "target": "AttackPrompt.__init__", "type": "similar_to", "metadata": {"similarity": 0.38317757009345793}}, {"source": "IndividualPromptAttack.__init__", "target": "PromptManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.4491525423728814}}, {"source": "IndividualPromptAttack.__init__", "target": "MultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.7333333333333333}}, {"source": "IndividualPromptAttack.__init__", "target": "ProgressiveMultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.8865979381443299}}, {"source": "IndividualPromptAttack.__init__", "target": "EvaluateAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.9456521739130435}}, {"source": "EvaluateAttack.__init__", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "model_path", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "losses", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "tokenizer_path", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "MultiPromptAttack.tests", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.__init__", "target": "AttackPrompt.__init__", "type": "similar_to", "metadata": {"similarity": 0.37272727272727274}}, {"source": "EvaluateAttack.__init__", "target": "PromptManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.4380165289256198}}, {"source": "EvaluateAttack.__init__", "target": "MultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.6914893617021277}}, {"source": "EvaluateAttack.__init__", "target": "ProgressiveMultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.8415841584158416}}, {"source": "EvaluateAttack.__init__", "target": "IndividualPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.9456521739130435}}, {"source": "EvaluateAttack.run", "target": "generate", "type": "calls", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "AttackPrompt.gen_str", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>.jailbroken", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.batch_input_ids", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.batch_attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.em", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "MultiPromptAttack.steps", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.log", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "ProgressiveMultiPromptAttack.step", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.prev_control", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.all_inputs", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.max_new_tokens", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.all_outputs", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.batch", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.batch_max_new", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.batch_inputs", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.outputs", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.batch_outputs", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "EvaluateAttack.gen_start_idx", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.run", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.run", "target": "logits", "type": "calls", "metadata": {}}, {"source": "ModelWorker.run", "target": "grad", "type": "calls", "metadata": {}}, {"source": "ModelWorker.run", "target": "ModelWorker", "type": "member_of", "metadata": {}}, {"source": "ModelWorker.run", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.run", "target": "ModelWorker.task", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.run", "target": "tasks", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.run", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.run", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.run", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "tokenizers", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "raw_conv_templates", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "conv_templates", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "num_train_models", "type": "uses_variable", "metadata": {}}, {"source": "get_workers", "target": "load_model_and_tokenizer", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "Card", "target": "Card.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "Card.__init__", "target": "Card", "type": "member_of", "metadata": {}}, {"source": "ResourceManager", "target": "ResourceManager.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ResourceManager", "target": "ResourceManager.request_card", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ResourceManager", "target": "ResourceManager.release_card", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "ResourceManager.__init__", "target": "ResourceManager", "type": "member_of", "metadata": {}}, {"source": "ResourceManager.__init__", "target": "device_list", "type": "uses_variable", "metadata": {}}, {"source": "ResourceManager.__init__", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "ResourceManager.__init__", "target": "ResourceManager.request_card", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "ResourceManager.__init__", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "ResourceManager.__init__", "target": "PromptManager.control_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ResourceManager.__init__", "target": "MultiPromptAttack.control_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ResourceManager.request_card", "target": "ResourceManager", "type": "member_of", "metadata": {}}, {"source": "ResourceManager.request_card", "target": "card", "type": "uses_variable", "metadata": {}}, {"source": "ResourceManager.request_card", "target": "ResourceManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "ResourceManager.request_card", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}, {"source": "ResourceManager.request_card", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ResourceManager.request_card", "target": "PromptManager.logits", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "ResourceManager.release_card", "target": "ResourceManager", "type": "member_of", "metadata": {}}, {"source": "GCGAttackPrompt.__init__", "target": "GCGAttackPrompt", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.__init__", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "topk", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "allow_non_ascii", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "temp", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "top_indices", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "control_toks", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "original_control_toks", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "new_token_pos", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "new_token_val", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "new_control_toks", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "GCGPromptManager.sample_control", "target": "sample_control", "type": "similar_to", "metadata": {"similarity": 0.8292682926829268}}, {"source": "GCGPromptManager.top_indices", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.control_toks", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.original_control_toks", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.new_token_pos", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.new_token_val", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGPromptManager.new_control_toks", "target": "GCGPromptManager", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.__init__", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.opt_only", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.main_device", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.control_cands", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.grad", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.new_grad", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.control_cand", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.loss", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.progress", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.min_idx", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.model_idx", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "GCGMultiPromptAttack.batch_idx", "target": "GCGMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "SuffixManager", "target": "SuffixManager.__init__", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SuffixManager", "target": "SuffixManager.get_prompt", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SuffixManager", "target": "SuffixManager.prompt", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SuffixManager", "target": "SuffixManager.encoding", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SuffixManager", "target": "SuffixManager.toks", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SuffixManager", "target": "SuffixManager.separator", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SuffixManager", "target": "SuffixManager.python_tokenizer", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SuffixManager", "target": "SuffixManager.get_input_ids", "type": "has_member", "metadata": {"member_type": "Function"}}, {"source": "SuffixManager", "target": "SuffixManager.input_ids", "type": "has_member", "metadata": {"member_type": "Variable"}}, {"source": "SuffixManager.__init__", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.__init__", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "AttackPrompt.encoding", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "AttackPrompt.toks", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "AttackPrompt.separator", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "AttackPrompt.python_tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "tokenizers", "type": "uses_variable", "metadata": {}}, {"source": "SuffixManager.get_prompt", "target": "AttackPrompt._update_ids", "type": "similar_to", "metadata": {"similarity": 0.863013698630137}}, {"source": "SuffixManager.prompt", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.encoding", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.toks", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.separator", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.python_tokenizer", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "SuffixManager.input_ids", "target": "SuffixManager", "type": "member_of", "metadata": {}}, {"source": "NpEncoder.default", "target": "NpEncoder", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.prompt", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.prompt", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.prompt", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.prompt", "target": "SuffixManager.get_input_ids", "type": "similar_to", "metadata": {"similarity": 0.3684210526315789}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.4166666666666667}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.prompt", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.4666666666666667}}, {"source": "AttackPrompt.encoding", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.toks", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.separator", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.python_tokenizer", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "AttackPrompt.attn_masks", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "AttackPrompt.output_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "EvaluateAttack.max_new_tokens", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.generate", "target": "generate", "type": "similar_to", "metadata": {"similarity": 0.8611111111111112}}, {"source": "AttackPrompt.generate", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.3157894736842105}}, {"source": "AttackPrompt.gen_config", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.input_ids", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.attn_masks", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.output_ids", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.gen_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.jailbroken", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.em", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.grad", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.grad", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.grad", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.grad", "target": "PromptManager.sample_control", "type": "similar_to", "metadata": {"similarity": 0.4117647058823529}}, {"source": "AttackPrompt.grad", "target": "MultiPromptAttack.step", "type": "similar_to", "metadata": {"similarity": 0.5333333333333333}}, {"source": "AttackPrompt.logits", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.pad_tok", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.test_controls", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.test_ids", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.max_len", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.nested_ids", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.locs", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.ids", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.attn_mask", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "ProgressiveMultiPromptAttack.loss", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "AttackPrompt.crit", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "AttackPrompt.loss_slice", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_loss", "target": "target_loss", "type": "similar_to", "metadata": {"similarity": 0.782608695652174}}, {"source": "AttackPrompt.target_loss", "target": "AttackPrompt.control_loss", "type": "similar_to", "metadata": {"similarity": 0.8181818181818182}}, {"source": "AttackPrompt.crit", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.loss_slice", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.loss", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "ProgressiveMultiPromptAttack.loss", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "AttackPrompt.crit", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "AttackPrompt.loss_slice", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.control_loss", "target": "target_loss", "type": "similar_to", "metadata": {"similarity": 0.7083333333333334}}, {"source": "AttackPrompt.control_loss", "target": "AttackPrompt.target_loss", "type": "similar_to", "metadata": {"similarity": 0.8181818181818182}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.assistant_str", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.35714285714285715}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.control_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "AttackPrompt.assistant_str", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.5384615384615384}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.5714285714285714}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "AttackPrompt.assistant_toks", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.4166666666666667}}, {"source": "AttackPrompt.assistant_toks", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.assistant_toks", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.assistant_toks", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.assistant_toks", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.4166666666666667}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.5714285714285714}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "AttackPrompt.goal_toks", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.goal_toks", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.goal_toks", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.goal_toks", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.goal_toks", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.target_toks", "target": "GCGAttackPrompt.grad", "type": "similar_to", "metadata": {"similarity": 0.38461538461538464}}, {"source": "AttackPrompt.target_toks", "target": "SuffixManager.get_input_ids", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.5714285714285714}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "AttackPrompt.target_toks", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.target_toks", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.target_toks", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.target_toks", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.target_toks", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.input_toks", "target": "GCGAttackPrompt.grad", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.5714285714285714}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.5714285714285714}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.5714285714285714}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.input_toks", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.input_toks", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "AttackPrompt.input_toks", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "AttackPrompt.input_toks", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "AttackPrompt.input_toks", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.input_str", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.4166666666666667}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.control_toks", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.input_str", "target": "AttackPrompt.eval_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "AttackPrompt.input_str", "target": "PromptManager.generate_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.input_str", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt", "type": "member_of", "metadata": {}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.eval_str", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.prompt", "type": "similar_to", "metadata": {"similarity": 0.4666666666666667}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.assistant_str", "type": "similar_to", "metadata": {"similarity": 0.5384615384615384}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.4166666666666667}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "AttackPrompt.eval_str", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.generate", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.generate", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.generate", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.generate", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.generate", "target": "EvaluateAttack.max_new_tokens", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.generate", "target": "ResourceManager.request_card", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}, {"source": "PromptManager.generate", "target": "AttackPrompt.generate", "type": "similar_to", "metadata": {"similarity": 0.3157894736842105}}, {"source": "PromptManager.generate", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.3684210526315789}}, {"source": "PromptManager.generate", "target": "AttackPrompt.test", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.generate", "target": "PromptManager.generate_str", "type": "similar_to", "metadata": {"similarity": 0.45}}, {"source": "PromptManager.generate", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.5882352941176471}}, {"source": "PromptManager.generate", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "PromptManager.generate", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "PromptManager.generate", "target": "PromptManager.logits", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.generate", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.generate", "target": "PromptManager.control_toks", "type": "similar_to", "metadata": {"similarity": 0.35294117647058826}}, {"source": "PromptManager.gen_config", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.test", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.test", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.test", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.test", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.test", "target": "ResourceManager.request_card", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.test", "target": "AttackPrompt.generate_str", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.test", "target": "AttackPrompt.test", "type": "similar_to", "metadata": {"similarity": 0.32142857142857145}}, {"source": "PromptManager.test", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.5882352941176471}}, {"source": "PromptManager.test", "target": "PromptManager.generate_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.test", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "PromptManager.test", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.6153846153846154}}, {"source": "PromptManager.test", "target": "PromptManager.logits", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "PromptManager.test", "target": "PromptManager.target_loss", "type": "similar_to", "metadata": {"similarity": 0.30434782608695654}}, {"source": "PromptManager.test", "target": "PromptManager.control_loss", "type": "similar_to", "metadata": {"similarity": 0.30434782608695654}}, {"source": "PromptManager.test", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.test", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.test", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.test", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.46153846153846156}}, {"source": "PromptManager.test", "target": "PromptManager.control_toks", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.test_loss", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.test_loss", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.test_loss", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.test_loss", "target": "AttackPrompt.test_loss", "type": "similar_to", "metadata": {"similarity": 0.3125}}, {"source": "PromptManager.test_loss", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "PromptManager.test_loss", "target": "PromptManager.generate_str", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.test_loss", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "PromptManager.test_loss", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.7272727272727273}}, {"source": "PromptManager.test_loss", "target": "PromptManager.logits", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.test_loss", "target": "PromptManager.target_loss", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.test_loss", "target": "PromptManager.control_loss", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.test_loss", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "PromptManager.test_loss", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "PromptManager.test_loss", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "PromptManager.test_loss", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.5454545454545454}}, {"source": "PromptManager.test_loss", "target": "PromptManager.control_toks", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "PromptManager.test_loss", "target": "MultiPromptAttack.control_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.grad", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.grad", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.grad", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.grad", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.grad", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "PromptManager.grad", "target": "PromptManager.generate_str", "type": "similar_to", "metadata": {"similarity": 0.35294117647058826}}, {"source": "PromptManager.grad", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.6153846153846154}}, {"source": "PromptManager.grad", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.7272727272727273}}, {"source": "PromptManager.grad", "target": "PromptManager.logits", "type": "similar_to", "metadata": {"similarity": 0.38095238095238093}}, {"source": "PromptManager.grad", "target": "PromptManager.target_loss", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}, {"source": "PromptManager.grad", "target": "PromptManager.control_loss", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}, {"source": "PromptManager.grad", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.grad", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.grad", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.grad", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.grad", "target": "PromptManager.control_toks", "type": "similar_to", "metadata": {"similarity": 0.5454545454545454}}, {"source": "PromptManager.grad", "target": "MultiPromptAttack.control_str", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.logits", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.logits", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.logits", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.logits", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.logits", "target": "AttackPrompt.test_controls", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.logits", "target": "PromptManager.vals", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.logits", "target": "ResourceManager.request_card", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.logits", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.logits", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "PromptManager.logits", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.logits", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.38095238095238093}}, {"source": "PromptManager.logits", "target": "PromptManager.target_loss", "type": "similar_to", "metadata": {"similarity": 0.3103448275862069}}, {"source": "PromptManager.logits", "target": "PromptManager.control_loss", "type": "similar_to", "metadata": {"similarity": 0.3103448275862069}}, {"source": "PromptManager.vals", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.target_loss", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.target_loss", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.target_loss", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.target_loss", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.target_loss", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.30434782608695654}}, {"source": "PromptManager.target_loss", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.target_loss", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}, {"source": "PromptManager.target_loss", "target": "PromptManager.logits", "type": "similar_to", "metadata": {"similarity": 0.3103448275862069}}, {"source": "PromptManager.target_loss", "target": "PromptManager.control_loss", "type": "similar_to", "metadata": {"similarity": 0.9}}, {"source": "PromptManager.control_loss", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.control_loss", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.control_loss", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.control_loss", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.control_loss", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.30434782608695654}}, {"source": "PromptManager.control_loss", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.control_loss", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.3181818181818182}}, {"source": "PromptManager.control_loss", "target": "PromptManager.logits", "type": "similar_to", "metadata": {"similarity": 0.3103448275862069}}, {"source": "PromptManager.control_loss", "target": "PromptManager.target_loss", "type": "similar_to", "metadata": {"similarity": 0.9}}, {"source": "PromptManager.sample_control", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.sample_control", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.sample_control", "target": "AttackPrompt.grad", "type": "similar_to", "metadata": {"similarity": 0.4117647058823529}}, {"source": "PromptManager.sample_control", "target": "MultiPromptAttack.step", "type": "similar_to", "metadata": {"similarity": 0.5625}}, {"source": "PromptManager.__len__", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.__len__", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__len__", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__len__", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__len__", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.__len__", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.__len__", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "PromptManager.__len__", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__len__", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.__len__", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.__len__", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.__getitem__", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.__getitem__", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.__getitem__", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__getitem__", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__getitem__", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__getitem__", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.__getitem__", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.__getitem__", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "PromptManager.__getitem__", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__getitem__", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.__getitem__", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.__getitem__", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.__iter__", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.__iter__", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__iter__", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__iter__", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__iter__", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.__iter__", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.__iter__", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.36363636363636365}}, {"source": "PromptManager.__iter__", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.__iter__", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.__iter__", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.__iter__", "target": "PromptManager.disallowed_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.control_str", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.control_str", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.control_str", "target": "ResourceManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "PromptManager.control_str", "target": "AttackPrompt.control_str", "type": "similar_to", "metadata": {"similarity": 0.4444444444444444}}, {"source": "PromptManager.control_str", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.control_str", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.46153846153846156}}, {"source": "PromptManager.control_str", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.5454545454545454}}, {"source": "PromptManager.control_str", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.control_str", "target": "PromptManager.control_toks", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "PromptManager.control_str", "target": "MultiPromptAttack.control_str", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "PromptManager.control_toks", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.control_toks", "target": "control_toks", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.control_toks", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "PromptManager.control_toks", "target": "ResourceManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.control_toks", "target": "PromptManager.generate", "type": "similar_to", "metadata": {"similarity": 0.35294117647058826}}, {"source": "PromptManager.control_toks", "target": "PromptManager.test", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "PromptManager.control_toks", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.6}}, {"source": "PromptManager.control_toks", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.5454545454545454}}, {"source": "PromptManager.control_toks", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "PromptManager.control_toks", "target": "MultiPromptAttack.control_str", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "PromptManager.disallowed_toks", "target": "PromptManager", "type": "member_of", "metadata": {}}, {"source": "PromptManager.disallowed_toks", "target": "AttackPrompt.assistant_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.disallowed_toks", "target": "AttackPrompt.goal_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.disallowed_toks", "target": "AttackPrompt.target_toks", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.disallowed_toks", "target": "AttackPrompt.input_toks", "type": "similar_to", "metadata": {"similarity": 0.*****************}}, {"source": "PromptManager.disallowed_toks", "target": "AttackPrompt.input_str", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "PromptManager.disallowed_toks", "target": "PromptManager.__len__", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.disallowed_toks", "target": "PromptManager.__getitem__", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "PromptManager.disallowed_toks", "target": "PromptManager.__iter__", "type": "similar_to", "metadata": {"similarity": 0.375}}, {"source": "MultiPromptAttack.__init__", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.__init__", "target": "AttackPrompt.__init__", "type": "similar_to", "metadata": {"similarity": 0.47126436781609193}}, {"source": "MultiPromptAttack.__init__", "target": "PromptManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.5252525252525253}}, {"source": "MultiPromptAttack.__init__", "target": "ProgressiveMultiPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.6804123711340206}}, {"source": "MultiPromptAttack.__init__", "target": "IndividualPromptAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.7333333333333333}}, {"source": "MultiPromptAttack.__init__", "target": "EvaluateAttack.__init__", "type": "similar_to", "metadata": {"similarity": 0.6914893617021277}}, {"source": "MultiPromptAttack.control_str", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.control_str", "target": "ResourceManager.__init__", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "MultiPromptAttack.control_str", "target": "AttackPrompt.control_str", "type": "similar_to", "metadata": {"similarity": 0.5}}, {"source": "MultiPromptAttack.control_str", "target": "PromptManager.test_loss", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "MultiPromptAttack.control_str", "target": "PromptManager.grad", "type": "similar_to", "metadata": {"similarity": 0.3076923076923077}}, {"source": "MultiPromptAttack.control_str", "target": "PromptManager.control_str", "type": "similar_to", "metadata": {"similarity": 0.6666666666666666}}, {"source": "MultiPromptAttack.control_str", "target": "PromptManager.control_toks", "type": "similar_to", "metadata": {"similarity": 0.4}}, {"source": "MultiPromptAttack.control_str", "target": "MultiPromptAttack.control_toks", "type": "similar_to", "metadata": {"similarity": 0.3157894736842105}}, {"source": "MultiPromptAttack.control_toks", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.control_toks", "target": "control_toks", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.control_toks", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.control_toks", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.control_toks", "target": "MultiPromptAttack.control_str", "type": "similar_to", "metadata": {"similarity": 0.3157894736842105}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "GCGMultiPromptAttack.control_cand", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "MultiPromptAttack.decoded_str", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "MultiPromptAttack.cands", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.get_filtered_cands", "target": "get_filtered_cands", "type": "similar_to", "metadata": {"similarity": 0.6122448979591837}}, {"source": "MultiPromptAttack.worker", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.decoded_str", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.cands", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.step", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.step", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.step", "target": "ProgressiveMultiPromptAttack.step", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.step", "target": "AttackPrompt.grad", "type": "similar_to", "metadata": {"similarity": 0.5333333333333333}}, {"source": "MultiPromptAttack.step", "target": "PromptManager.sample_control", "type": "similar_to", "metadata": {"similarity": 0.5625}}, {"source": "MultiPromptAttack.P", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.P", "target": "k", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.P", "target": "MultiPromptAttack.T", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.T", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.target_weight_fn", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.control_weight_fn", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.steps", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.loss", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.best_loss", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.best_control", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.runtime", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.model_tests", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.start", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.keep_control", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.prev_loss", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.last_control", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "ProgressiveMultiPromptAttack.model_tests", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "MultiPromptAttack.model_tests_jb", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "MultiPromptAttack.model_tests_mb", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "MultiPromptAttack.model_tests_loss", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.test", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.model_tests_jb", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.model_tests_mb", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.model_tests_loss", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.all_workers", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.all_prompts", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "MultiPromptAttack.x", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "MultiPromptAttack.id_id", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "MultiPromptAttack.id_od", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "MultiPromptAttack.od_id", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "MultiPromptAttack.od_od", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.parse_results", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "MultiPromptAttack.x", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.i", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.id_id", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.id_od", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.od_id", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.od_od", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.log", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.all_goal_strs", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.tests", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.n_passed", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.n_em", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.n_loss", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.total_tests", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "MultiPromptAttack.output_str", "target": "MultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "target": "EvaluateAttack.mpa_kwargs", "type": "uses_variable", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "target": "IndividualPromptAttack.filter_mpa_kwargs", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "target": "EvaluateAttack.filter_mpa_kwargs", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "ProgressiveMultiPromptAttack.mpa_kwargs", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.log", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.num_goals", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.num_workers", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.step", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.stop_inner_on_success", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.loss", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.attack", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "ProgressiveMultiPromptAttack.model_tests", "target": "ProgressiveMultiPromptAttack", "type": "member_of", "metadata": {}}, {"source": "IndividualPromptAttack.filter_mpa_kwargs", "target": "IndividualPromptAttack", "type": "member_of", "metadata": {}}, {"source": "IndividualPromptAttack.filter_mpa_kwargs", "target": "EvaluateAttack.mpa_kwargs", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.filter_mpa_kwargs", "target": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "IndividualPromptAttack.filter_mpa_kwargs", "target": "EvaluateAttack.filter_mpa_kwargs", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "IndividualPromptAttack.mpa_kwargs", "target": "IndividualPromptAttack", "type": "member_of", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "IndividualPromptAttack", "type": "member_of", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "topk", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "allow_non_ascii", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "test_prefixes", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "temp", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "process", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "SuffixManager.prompt", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "MultiPromptAttack.steps", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "MultiPromptAttack.prev_loss", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "EvaluateAttack.log", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "MultiPromptAttack.tests", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "IndividualPromptAttack.stop_inner_on_success", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "EvaluateAttack.attack", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "workers", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "IndividualPromptAttack.run", "target": "ProgressiveMultiPromptAttack.run", "type": "similar_to", "metadata": {"similarity": 0.7338129496402878}}, {"source": "IndividualPromptAttack.log", "target": "IndividualPromptAttack", "type": "member_of", "metadata": {}}, {"source": "IndividualPromptAttack.stop_inner_on_success", "target": "IndividualPromptAttack", "type": "member_of", "metadata": {}}, {"source": "IndividualPromptAttack.attack", "target": "IndividualPromptAttack", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.filter_mpa_kwargs", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.filter_mpa_kwargs", "target": "EvaluateAttack.mpa_kwargs", "type": "uses_variable", "metadata": {}}, {"source": "EvaluateAttack.filter_mpa_kwargs", "target": "ProgressiveMultiPromptAttack.filter_mpa_kwargs", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "EvaluateAttack.filter_mpa_kwargs", "target": "IndividualPromptAttack.filter_mpa_kwargs", "type": "similar_to", "metadata": {"similarity": 1.0}}, {"source": "EvaluateAttack.mpa_kwargs", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.log", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.prev_control", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.attack", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.all_inputs", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.max_new_tokens", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.targets", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.all_outputs", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.batch", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.batch_max_new", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.batch_inputs", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.batch_input_ids", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.batch_attention_mask", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.outputs", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.batch_outputs", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.gen_start_idx", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>.jailbroken", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "EvaluateAttack.em", "target": "<PERSON><PERSON>ate<PERSON><PERSON><PERSON>", "type": "member_of", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "ModelWorker", "type": "member_of", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "model_path", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "process", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "tasks", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__init__", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.task", "target": "ModelWorker", "type": "member_of", "metadata": {}}, {"source": "ModelWorker.start", "target": "ModelWorker", "type": "member_of", "metadata": {}}, {"source": "ModelWorker.start", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.start", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.start", "target": "process", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.start", "target": "tasks", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.start", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.start", "target": "MultiPromptAttack.worker", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.start", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.stop", "target": "ModelWorker", "type": "member_of", "metadata": {}}, {"source": "ModelWorker.stop", "target": "process", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.stop", "target": "tasks", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__call__", "target": "ModelWorker", "type": "member_of", "metadata": {}}, {"source": "ModelWorker.__call__", "target": "args", "type": "uses_variable", "metadata": {}}, {"source": "ModelWorker.__call__", "target": "tasks", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "PromptManager.gen_config", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "AttackPrompt.attn_masks", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "AttackPrompt.output_ids", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "EvaluateAttack.max_new_tokens", "type": "uses_variable", "metadata": {}}, {"source": "generate", "target": "AttackPrompt.generate", "type": "similar_to", "metadata": {"similarity": 0.8611111111111112}}, {"source": "sample_control", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "topk", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "not_allowed_tokens", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "temp", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "top_indices", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "control_toks", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "original_control_toks", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "new_token_pos", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "new_token_val", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "new_control_toks", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "grad", "type": "uses_variable", "metadata": {}}, {"source": "sample_control", "target": "GCGPromptManager.sample_control", "type": "similar_to", "metadata": {"similarity": 0.8292682926829268}}, {"source": "get_filtered_cands", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "get_filtered_cands", "target": "GCGMultiPromptAttack.control_cand", "type": "uses_variable", "metadata": {}}, {"source": "get_filtered_cands", "target": "MultiPromptAttack.decoded_str", "type": "uses_variable", "metadata": {}}, {"source": "get_filtered_cands", "target": "MultiPromptAttack.cands", "type": "uses_variable", "metadata": {}}, {"source": "get_filtered_cands", "target": "encoded", "type": "uses_variable", "metadata": {}}, {"source": "get_filtered_cands", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "get_filtered_cands", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "get_filtered_cands", "target": "MultiPromptAttack.get_filtered_cands", "type": "similar_to", "metadata": {"similarity": 0.6122448979591837}}, {"source": "forward", "target": "batch_size", "type": "uses_variable", "metadata": {}}, {"source": "forward", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "forward", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "forward", "target": "EvaluateAttack.batch_input_ids", "type": "uses_variable", "metadata": {}}, {"source": "forward", "target": "EvaluateAttack.batch_attention_mask", "type": "uses_variable", "metadata": {}}, {"source": "forward", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "forward", "target": "MultiPromptAttack.i", "type": "uses_variable", "metadata": {}}, {"source": "target_loss", "target": "AttackPrompt.logits", "type": "uses_variable", "metadata": {}}, {"source": "target_loss", "target": "ProgressiveMultiPromptAttack.loss", "type": "uses_variable", "metadata": {}}, {"source": "target_loss", "target": "AttackPrompt.ids", "type": "uses_variable", "metadata": {}}, {"source": "target_loss", "target": "AttackPrompt.crit", "type": "uses_variable", "metadata": {}}, {"source": "target_loss", "target": "AttackPrompt.loss_slice", "type": "uses_variable", "metadata": {}}, {"source": "target_loss", "target": "MultiPromptAttack.start", "type": "uses_variable", "metadata": {}}, {"source": "target_loss", "target": "AttackPrompt.target_loss", "type": "similar_to", "metadata": {"similarity": 0.782608695652174}}, {"source": "target_loss", "target": "AttackPrompt.control_loss", "type": "similar_to", "metadata": {"similarity": 0.7083333333333334}}, {"source": "load_model_and_tokenizer", "target": "model_path", "type": "uses_variable", "metadata": {}}, {"source": "load_model_and_tokenizer", "target": "device", "type": "uses_variable", "metadata": {}}, {"source": "load_model_and_tokenizer", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "load_model_and_tokenizer", "target": "tokenizer_path", "type": "uses_variable", "metadata": {}}, {"source": "load_model_and_tokenizer", "target": "tokenizer", "type": "uses_variable", "metadata": {}}, {"source": "load_model_and_tokenizer", "target": "get_workers", "type": "similar_to", "metadata": {"similarity": 0.3333333333333333}}, {"source": "load_conversation_template", "target": "template_name", "type": "uses_variable", "metadata": {}}, {"source": "load_conversation_template", "target": "conv_template", "type": "uses_variable", "metadata": {}}, {"source": "load_conversation_template", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "get_embedding_layer", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "get_embedding_layer", "target": "get_embedding_matrix", "type": "similar_to", "metadata": {"similarity": 0.88}}, {"source": "get_embedding_layer", "target": "get_embeddings", "type": "similar_to", "metadata": {"similarity": 0.8461538461538461}}, {"source": "get_embedding_matrix", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "get_embedding_matrix", "target": "get_embedding_layer", "type": "similar_to", "metadata": {"similarity": 0.88}}, {"source": "get_embedding_matrix", "target": "get_embeddings", "type": "similar_to", "metadata": {"similarity": 0.8148148148148148}}, {"source": "get_embeddings", "target": "AttackPrompt.input_ids", "type": "uses_variable", "metadata": {}}, {"source": "get_embeddings", "target": "model", "type": "uses_variable", "metadata": {}}, {"source": "get_embeddings", "target": "get_embedding_layer", "type": "similar_to", "metadata": {"similarity": 0.8461538461538461}}, {"source": "get_embeddings", "target": "get_embedding_matrix", "type": "similar_to", "metadata": {"similarity": 0.8148148148148148}}, {"source": "get_goals_and_targets", "target": "target", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "EvaluateAttack.targets", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "train_goals", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "train_targets", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "test_goals", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "test_targets", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "offset", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "train_data", "type": "uses_variable", "metadata": {}}, {"source": "get_goals_and_targets", "target": "test_data", "type": "uses_variable", "metadata": {}}], "statistics": {"imports": 59, "imports_from": 39, "inherits_from": 11, "has_member": 190, "calls": 42, "uses_variable": 525, "similar_to": 336, "member_of": 190}}