"""
嵌入模型封装
支持本地Qwen3_Embedding模型和text-embedding-v4 API调用
"""

import os
import torch
import numpy as np
from typing import List, Union, Optional
from transformers import AutoTokenizer, AutoModel
from sentence_transformers import SentenceTransformer
import logging
from pathlib import Path
import requests
import json
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

# 加载环境变量
env_path = Path(__file__).parent.parent / ".env"
load_dotenv(env_path)


def get_gpu_memory_info():
    """获取所有GPU的内存信息"""
    if not torch.cuda.is_available():
        return {}

    gpu_info = {}
    for i in range(torch.cuda.device_count()):
        try:
            torch.cuda.set_device(i)
            total_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB
            allocated_memory = torch.cuda.memory_allocated(i) / 1024**3  # GB
            cached_memory = torch.cuda.memory_reserved(i) / 1024**3  # GB
            free_memory = total_memory - allocated_memory

            gpu_info[i] = {
                'name': torch.cuda.get_device_properties(i).name,
                'total_memory': total_memory,
                'allocated_memory': allocated_memory,
                'cached_memory': cached_memory,
                'free_memory': free_memory,
                'utilization': allocated_memory / total_memory * 100
            }
        except Exception as e:
            logger.warning(f"Failed to get info for GPU {i}: {e}")

    return gpu_info


def select_best_gpu(min_free_memory_gb: float = 8.0):
    """
    选择最佳的GPU设备

    Args:
        min_free_memory_gb: 最小空闲内存要求（GB）

    Returns:
        最佳GPU设备ID，如果没有合适的GPU则返回None
    """
    if not torch.cuda.is_available():
        logger.info("CUDA不可用，使用CPU")
        return "cpu"

    gpu_info = get_gpu_memory_info()

    if not gpu_info:
        logger.warning("无法获取GPU信息，使用默认设备")
        return "cuda"

    # 打印GPU信息
    logger.info("GPU内存信息:")
    for gpu_id, info in gpu_info.items():
        logger.info(f"  GPU {gpu_id} ({info['name']}): "
                   f"总内存 {info['total_memory']:.1f}GB, "
                   f"已用 {info['allocated_memory']:.1f}GB, "
                   f"空闲 {info['free_memory']:.1f}GB, "
                   f"利用率 {info['utilization']:.1f}%")

    # 找到空闲内存最多且满足要求的GPU
    suitable_gpus = []
    for gpu_id, info in gpu_info.items():
        if info['free_memory'] >= min_free_memory_gb:
            suitable_gpus.append((gpu_id, info['free_memory']))

    if not suitable_gpus:
        logger.warning(f"没有找到空闲内存≥{min_free_memory_gb}GB的GPU")
        # 选择空闲内存最多的GPU
        best_gpu = max(gpu_info.items(), key=lambda x: x[1]['free_memory'])
        gpu_id = best_gpu[0]
        logger.info(f"选择空闲内存最多的GPU {gpu_id} (空闲: {best_gpu[1]['free_memory']:.1f}GB)")
        return f"cuda:{gpu_id}"

    # 选择空闲内存最多的合适GPU
    best_gpu_id = max(suitable_gpus, key=lambda x: x[1])[0]
    logger.info(f"选择GPU {best_gpu_id} (空闲内存: {gpu_info[best_gpu_id]['free_memory']:.1f}GB)")
    return f"cuda:{best_gpu_id}"


class TextEmbeddingV4API:
    """text-embedding-v4 API调用封装"""

    def __init__(self, api_key: str = None, api_base: str = None, dimension: int = 1024):
        """
        初始化text-embedding-v4 API客户端

        Args:
            api_key: API密钥，如果为None则从环境变量DASHSCOPE_API_KEY获取
            api_base: API基础URL，如果为None则从环境变量DASHSCOPE_API_BASE获取
            dimension: 向量维度，支持2048、1536、1024、768、512、256、128、64
        """
        self.api_key = api_key or os.getenv("DASHSCOPE_API_KEY")
        self.api_base = api_base or os.getenv("DASHSCOPE_API_BASE")
        self.dimension = dimension

        if not self.api_key:
            raise ValueError("API key is required. Please set DASHSCOPE_API_KEY environment variable or pass api_key parameter.")

        if not self.api_base:
            raise ValueError("API base URL is required. Please set DASHSCOPE_API_BASE environment variable or pass api_base parameter.")

        # 确保API base URL以/compatible-mode/v1结尾
        if not self.api_base.endswith('/compatible-mode/v1'):
            if self.api_base.endswith('/'):
                self.api_base = self.api_base + 'compatible-mode/v1'
            else:
                self.api_base = self.api_base + '/compatible-mode/v1'

        self.endpoint = f"{self.api_base}/embeddings"

        logger.info(f"Initialized TextEmbeddingV4API with endpoint: {self.endpoint}")
        logger.info(f"Using dimension: {self.dimension}")

    def encode(self, texts: Union[str, List[str]],
               batch_size: int = 10,
               max_length: int = 8192,
               normalize: bool = True) -> np.ndarray:
        """
        编码文本为向量

        Args:
            texts: 单个文本或文本列表
            batch_size: 批处理大小，text-embedding-v4最多支持10条
            max_length: 最大序列长度，text-embedding-v4最多支持8192 Token
            normalize: 是否归一化向量（API返回的向量已经归一化）

        Returns:
            嵌入向量数组
        """
        if isinstance(texts, str):
            texts = [texts]

        all_embeddings = []

        # 分批处理
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": "text-embedding-v4",
                "input": batch_texts,
                "dimensions": self.dimension,
                "encoding_format": "float"
            }

            try:
                response = requests.post(self.endpoint, headers=headers, json=data, timeout=30)
                response.raise_for_status()

                result = response.json()

                # 提取嵌入向量
                batch_embeddings = []
                for item in result["data"]:
                    batch_embeddings.append(item["embedding"])

                all_embeddings.extend(batch_embeddings)

                logger.debug(f"Successfully processed batch {i//batch_size + 1}, texts: {len(batch_texts)}")

            except requests.exceptions.RequestException as e:
                logger.error(f"API request failed: {e}")
                if hasattr(e, 'response') and e.response is not None:
                    logger.error(f"Response content: {e.response.text}")
                raise
            except KeyError as e:
                logger.error(f"Unexpected API response format: {e}")
                logger.error(f"Response: {response.text}")
                raise

        embeddings = np.array(all_embeddings)

        # API返回的向量通常已经归一化，但如果需要可以再次归一化
        if normalize:
            norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
            # 避免除零
            norms = np.where(norms == 0, 1, norms)
            embeddings = embeddings / norms

        return embeddings

    def compute_similarity(self, query_embedding: np.ndarray,
                          doc_embeddings: np.ndarray) -> np.ndarray:
        """
        计算查询向量与文档向量的相似度

        Args:
            query_embedding: 查询向量 (1, dim)
            doc_embeddings: 文档向量 (n, dim)

        Returns:
            相似度分数数组
        """
        # 确保向量是归一化的
        query_norm = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)
        doc_norm = doc_embeddings / np.linalg.norm(doc_embeddings, axis=1, keepdims=True)

        # 计算余弦相似度
        similarities = np.dot(query_norm, doc_norm.T).flatten()

        return similarities

    def get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        return self.dimension


class Qwen3EmbeddingModel:
    """Qwen3嵌入模型封装"""
    
    def __init__(self, model_path: str = None, model_size: str = "0.6B", device: str = None):
        """
        初始化嵌入模型

        Args:
            model_path: 模型路径，如果为None则使用默认路径
            model_size: 模型大小，"0.6B" 或 "4B"
            device: 设备，如果为None则自动选择最佳GPU
        """
        self.model_size = model_size

        # 智能选择设备
        if device is None:
            # 根据模型大小设置最小内存要求
            min_memory = 12.0 if model_size == "4B" else 4.0
            self.device = select_best_gpu(min_free_memory_gb=min_memory)
        else:
            self.device = device

        logger.info(f"使用设备: {self.device}")
        
        # 设置模型路径
        if model_path is None:
            base_dir = Path(__file__).parent.parent
            model_path = base_dir / "models" / "Qwen3_Embedding" / f"Qwen3-Embedding-{model_size}"
        
        self.model_path = str(model_path)
        
        # 检查模型路径是否存在
        if not os.path.exists(self.model_path):
            raise ValueError(f"Model path does not exist: {self.model_path}")
        
        logger.info(f"Loading Qwen3-Embedding-{model_size} from {self.model_path}")
        
        # 加载模型和分词器
        self.tokenizer = None
        self.model = None
        self._use_sentence_transformers = True
        self._load_model()
        
        logger.info(f"Model loaded successfully on device: {self.device}")
    
    def _load_model(self):
        """加载模型和分词器"""
        try:
            # 使用sentence-transformers加载模型
            self.model = SentenceTransformer(
                self.model_path,
                device=self.device
            )

            # 获取分词器
            self.tokenizer = self.model.tokenizer

            logger.info(f"Model loaded with max_seq_length: {self.model.max_seq_length}")

        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            # 尝试使用transformers加载
            try:
                logger.info("Trying to load with transformers...")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    trust_remote_code=True
                )

                self.model = AutoModel.from_pretrained(
                    self.model_path,
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
                ).to(self.device)

                self.model.eval()
                self._use_sentence_transformers = False

            except Exception as e2:
                logger.error(f"Failed to load with transformers: {e2}")
                raise
    
    def encode(self, texts: Union[str, List[str]],
               batch_size: int = 32,
               max_length: int = 512,
               normalize: bool = True) -> np.ndarray:
        """
        编码文本为向量

        Args:
            texts: 单个文本或文本列表
            batch_size: 批处理大小
            max_length: 最大序列长度
            normalize: 是否归一化向量

        Returns:
            嵌入向量数组
        """
        if isinstance(texts, str):
            texts = [texts]

        if self._use_sentence_transformers and hasattr(self.model, 'encode'):
            # 使用sentence-transformers
            embeddings = self.model.encode(
                texts,
                batch_size=batch_size,
                normalize_embeddings=normalize,
                convert_to_numpy=True
            )
            return embeddings
        else:
            # 使用transformers
            embeddings = []

            with torch.no_grad():
                for i in range(0, len(texts), batch_size):
                    batch_texts = texts[i:i + batch_size]

                    # 分词
                    inputs = self.tokenizer(
                        batch_texts,
                        padding=True,
                        truncation=True,
                        max_length=max_length,
                        return_tensors="pt"
                    ).to(self.device)

                    # 获取嵌入
                    outputs = self.model(**inputs)

                    # 使用[CLS]标记的嵌入或平均池化
                    if hasattr(outputs, 'last_hidden_state'):
                        # 使用[CLS]标记的嵌入
                        batch_embeddings = outputs.last_hidden_state[:, 0, :].cpu().numpy()
                    else:
                        # 如果没有last_hidden_state，使用pooler_output
                        batch_embeddings = outputs.pooler_output.cpu().numpy()

                    embeddings.append(batch_embeddings)

            # 合并所有批次的嵌入
            embeddings = np.vstack(embeddings)

            # 归一化
            if normalize:
                embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)

            return embeddings
    
    def compute_similarity(self, query_embedding: np.ndarray, 
                          doc_embeddings: np.ndarray) -> np.ndarray:
        """
        计算查询向量与文档向量的相似度
        
        Args:
            query_embedding: 查询向量 (1, dim)
            doc_embeddings: 文档向量 (n, dim)
            
        Returns:
            相似度分数数组
        """
        # 确保向量是归一化的
        query_norm = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)
        doc_norm = doc_embeddings / np.linalg.norm(doc_embeddings, axis=1, keepdims=True)
        
        # 计算余弦相似度
        similarities = np.dot(query_norm, doc_norm.T).flatten()
        
        return similarities
    
    def get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        if self._use_sentence_transformers and hasattr(self.model, 'get_sentence_embedding_dimension'):
            return self.model.get_sentence_embedding_dimension()
        else:
            # 使用一个简单的文本测试嵌入维度
            test_embedding = self.encode(["test"])
            return test_embedding.shape[1]
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'model') and self.model is not None:
            del self.model
        if hasattr(self, 'tokenizer') and self.tokenizer is not None:
            del self.tokenizer
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


class CrossEncoder:
    """交叉编码器用于重排序"""

    def __init__(self, model_path: str = None, device: str = None, model_type: str = "qwen3-0.6B"):
        """
        初始化交叉编码器

        Args:
            model_path: 模型路径，如果为None则根据model_type使用默认模型
            device: 设备，如果为None则自动选择最佳GPU
            model_type: 模型类型 ("qwen3-0.6B", "qwen3-4B", "ms-marco")
        """
        self.model_type = model_type

        # 智能选择设备
        if device is None:
            # 根据模型类型设置内存要求
            if model_type == "qwen3-4B":
                min_memory = 12.0
            elif model_type == "qwen3-0.6B":
                min_memory = 4.0
            else:  # ms-marco
                min_memory = 2.0
            self.device = select_best_gpu(min_free_memory_gb=min_memory)
        else:
            self.device = device

        logger.info(f"CrossEncoder使用设备: {self.device}")

        # 根据模型类型设置默认路径
        if model_path is None:
            if model_type == "qwen3-0.6B":
                base_dir = Path(__file__).parent.parent
                model_path = base_dir / "models" / "Qwen3-Reranker" / "Qwen3-Reranker-0.6B"
            elif model_type == "qwen3-4B":
                base_dir = Path(__file__).parent.parent
                model_path = base_dir / "models" / "Qwen3-Reranker" / "Qwen3-Reranker-4B"
            else:  # ms-marco
                model_path = "cross-encoder/ms-marco-MiniLM-L6-v2"

        self.model_path = str(model_path)
        
        logger.info(f"Loading CrossEncoder ({self.model_type}) from {self.model_path}")

        if self.model_type.startswith("qwen3"):
            # 使用Qwen3-Reranker模型
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    trust_remote_code=True
                )
                self.model = AutoModel.from_pretrained(
                    self.model_path,
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if self.device.startswith("cuda") else torch.float32
                ).to(self.device)
                self.model.eval()

                # 动态获取Qwen3-Reranker的嵌入维度
                self.embedding_dim = self._get_qwen3_embedding_dim()

                # 添加分类头用于重排序，确保数据类型匹配
                self.classifier = torch.nn.Linear(self.embedding_dim, 1)
                if self.device.startswith("cuda"):
                    self.classifier = self.classifier.half().to(self.device)
                else:
                    self.classifier = self.classifier.to(self.device)
                self.use_sentence_transformers = False
                self.use_qwen3_reranker = True

                logger.info(f"Using Qwen3-Reranker ({self.model_type})")

            except Exception as e:
                logger.error(f"Failed to load Qwen3-Reranker: {e}")
                logger.info("Falling back to MS-MARCO model...")
                self.model_type = "ms-marco"
                self.model_path = "cross-encoder/ms-marco-MiniLM-L6-v2"
                # 递归调用加载MS-MARCO
                self._load_ms_marco()
                return
        else:
            # 使用MS-MARCO模型
            self._load_ms_marco()

        logger.info(f"CrossEncoder loaded successfully on device: {self.device}")

    def _load_ms_marco(self):
        """加载MS-MARCO模型"""
        try:
            # 尝试使用sentence-transformers的CrossEncoder
            from sentence_transformers import CrossEncoder as STCrossEncoder
            self.model = STCrossEncoder(self.model_path, device=self.device)
            self.use_sentence_transformers = True
            self.use_qwen3_reranker = False
            logger.info("Using sentence-transformers CrossEncoder (MS-MARCO)")

        except ImportError:
            logger.warning("sentence-transformers not available, using transformers")
            # 回退到transformers
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModel.from_pretrained(self.model_path).to(self.device)
            self.model.eval()

            # 添加分类头
            self.embedding_dim = 384  # MiniLM-L6的标准维度
            self.classifier = torch.nn.Linear(self.embedding_dim, 1).to(self.device)
            self.use_sentence_transformers = False
            self.use_qwen3_reranker = False
    
    def _get_qwen3_embedding_dim(self) -> int:
        """获取Qwen3-Reranker的嵌入维度"""
        with torch.no_grad():
            test_input = self.tokenizer(
                "test",
                return_tensors="pt",
                padding=True,
                truncation=True
            ).to(self.device)

            outputs = self.model(**test_input)

            # 尝试不同的输出方式
            if hasattr(outputs, 'pooler_output') and outputs.pooler_output is not None:
                return outputs.pooler_output.shape[-1]
            else:
                # 使用last_hidden_state的平均池化
                return outputs.last_hidden_state.shape[-1]

    def _get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        if hasattr(self, 'use_sentence_transformers') and self.use_sentence_transformers:
            return 384  # MiniLM-L6的标准维度
        else:
            with torch.no_grad():
                test_input = self.tokenizer(
                    "test",
                    return_tensors="pt",
                    padding=True,
                    truncation=True
                ).to(self.device)

                outputs = self.model(**test_input)
                if hasattr(outputs, 'last_hidden_state'):
                    return outputs.last_hidden_state.shape[-1]
                else:
                    return outputs.pooler_output.shape[-1]
    
    def score(self, query_doc_pairs: List[tuple],
              batch_size: int = 16,
              max_length: int = 512) -> List[float]:
        """
        为查询-文档对计算相关性分数

        Args:
            query_doc_pairs: (query, document) 对的列表
            batch_size: 批处理大小
            max_length: 最大序列长度

        Returns:
            相关性分数列表
        """
        if hasattr(self, 'use_sentence_transformers') and self.use_sentence_transformers:
            # 使用sentence-transformers的CrossEncoder (MS-MARCO)
            raw_scores = self.model.predict(query_doc_pairs)

            # 将MS-MARCO分数转换为0-1范围的概率
            # MS-MARCO模型输出的分数通常在-10到10之间，我们使用sigmoid归一化
            import numpy as np
            normalized_scores = 1 / (1 + np.exp(-np.array(raw_scores)))

            return normalized_scores.tolist()

        elif hasattr(self, 'use_qwen3_reranker') and self.use_qwen3_reranker:
            # 使用Qwen3-Reranker模型
            scores = []

            with torch.no_grad():
                for i in range(0, len(query_doc_pairs), batch_size):
                    batch_pairs = query_doc_pairs[i:i + batch_size]

                    # 构建输入文本 - Qwen3-Reranker使用特殊格式
                    batch_texts = []
                    for query, doc in batch_pairs:
                        # Qwen3-Reranker的输入格式
                        combined_text = f"Query: {query}\nDocument: {doc}"
                        batch_texts.append(combined_text)

                    # 分词
                    inputs = self.tokenizer(
                        batch_texts,
                        padding=True,
                        truncation=True,
                        max_length=max_length,
                        return_tensors="pt"
                    ).to(self.device)

                    # 获取嵌入
                    outputs = self.model(**inputs)

                    # Qwen3-Reranker使用pooler_output或last_hidden_state的平均
                    if hasattr(outputs, 'pooler_output') and outputs.pooler_output is not None:
                        embeddings = outputs.pooler_output
                    else:
                        # 使用last_hidden_state的平均池化
                        embeddings = outputs.last_hidden_state.mean(dim=1)

                    # 通过分类头获取分数
                    batch_scores = self.classifier(embeddings).squeeze(-1)
                    batch_scores = torch.sigmoid(batch_scores)  # 转换为概率

                    scores.extend(batch_scores.cpu().numpy().tolist())

            return scores

        else:
            # 使用transformers的实现 (回退方案)
            scores = []

            with torch.no_grad():
                for i in range(0, len(query_doc_pairs), batch_size):
                    batch_pairs = query_doc_pairs[i:i + batch_size]

                    # 构建输入文本
                    batch_texts = []
                    for query, doc in batch_pairs:
                        combined_text = f"{query} [SEP] {doc}"
                        batch_texts.append(combined_text)

                    # 分词
                    inputs = self.tokenizer(
                        batch_texts,
                        padding=True,
                        truncation=True,
                        max_length=max_length,
                        return_tensors="pt"
                    ).to(self.device)

                    # 获取嵌入
                    outputs = self.model(**inputs)

                    if hasattr(outputs, 'last_hidden_state'):
                        # 使用[CLS]标记的嵌入
                        embeddings = outputs.last_hidden_state[:, 0, :]
                    else:
                        embeddings = outputs.pooler_output

                    # 通过分类头获取分数
                    batch_scores = self.classifier(embeddings).squeeze(-1)
                    batch_scores = torch.sigmoid(batch_scores)  # 转换为概率

                    scores.extend(batch_scores.cpu().numpy().tolist())

            return scores


class EmbeddingModel:
    """统一的嵌入模型接口，支持本地模型和API调用"""

    def __init__(self, model_type: str = "api", **kwargs):
        """
        初始化嵌入模型

        Args:
            model_type: 模型类型，"api" 使用text-embedding-v4 API，"local" 使用本地Qwen3模型
            **kwargs: 传递给具体模型的参数
        """
        self.model_type = model_type

        if model_type == "api":
            # 使用text-embedding-v4 API
            api_key = kwargs.get('api_key')
            api_base = kwargs.get('api_base')
            dimension = kwargs.get('dimension', 1024)

            self.model = TextEmbeddingV4API(
                api_key=api_key,
                api_base=api_base,
                dimension=dimension
            )
            logger.info("Using text-embedding-v4 API")

        elif model_type == "local":
            # 使用本地Qwen3模型
            model_path = kwargs.get('model_path')
            model_size = kwargs.get('model_size', "0.6B")
            device = kwargs.get('device')

            self.model = Qwen3EmbeddingModel(
                model_path=model_path,
                model_size=model_size,
                device=device
            )
            logger.info("Using local Qwen3 embedding model")

        else:
            raise ValueError(f"Unsupported model_type: {model_type}. Choose 'api' or 'local'.")

    def encode(self, texts: Union[str, List[str]], **kwargs) -> np.ndarray:
        """编码文本为向量"""
        return self.model.encode(texts, **kwargs)

    def compute_similarity(self, query_embedding: np.ndarray,
                          doc_embeddings: np.ndarray) -> np.ndarray:
        """计算相似度"""
        return self.model.compute_similarity(query_embedding, doc_embeddings)

    def get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        return self.model.get_embedding_dim()


def test_embedding_model():
    """测试嵌入模型"""
    test_texts = [
        "This is a test sentence.",
        "Another test sentence for embedding.",
        "Graph convolutional network for sentiment analysis."
    ]

    # 测试API模型
    try:
        print("Testing text-embedding-v4 API...")
        api_model = EmbeddingModel(model_type="api", dimension=1024)

        embeddings = api_model.encode(test_texts)
        print(f"API Embeddings shape: {embeddings.shape}")
        print(f"API Embedding dimension: {api_model.get_embedding_dim()}")

        # 测试相似度计算
        query_embedding = embeddings[:1]  # 第一个作为查询
        doc_embeddings = embeddings[1:]   # 其他作为文档

        similarities = api_model.compute_similarity(query_embedding, doc_embeddings)
        print(f"API Similarities: {similarities}")
        print("API model test passed!")

    except Exception as e:
        logger.error(f"API model test failed: {e}")
        print("API model test failed, trying local model...")

        # 如果API测试失败，尝试本地模型
        try:
            print("Testing local Qwen3 model...")
            local_model = EmbeddingModel(model_type="local", model_size="0.6B")

            embeddings = local_model.encode(test_texts)
            print(f"Local Embeddings shape: {embeddings.shape}")
            print(f"Local Embedding dimension: {local_model.get_embedding_dim()}")

            # 测试相似度计算
            query_embedding = embeddings[:1]  # 第一个作为查询
            doc_embeddings = embeddings[1:]   # 其他作为文档

            similarities = local_model.compute_similarity(query_embedding, doc_embeddings)
            print(f"Local Similarities: {similarities}")
            print("Local model test passed!")

            return True

        except Exception as e2:
            logger.error(f"Local model test also failed: {e2}")
            return False

    return True


def test_api_model_only():
    """仅测试API模型"""
    try:
        print("Testing text-embedding-v4 API only...")

        # 测试不同维度
        for dim in [256, 512, 1024]:
            print(f"\nTesting dimension: {dim}")
            api_model = TextEmbeddingV4API(dimension=dim)

            test_texts = [
                "这是一个测试句子。",
                "另一个用于嵌入的测试句子。",
                "图卷积网络用于情感分析。"
            ]

            embeddings = api_model.encode(test_texts)
            print(f"Embeddings shape: {embeddings.shape}")
            print(f"Expected dimension: {dim}, Actual dimension: {embeddings.shape[1]}")

            # 验证维度
            assert embeddings.shape[1] == dim, f"Dimension mismatch: expected {dim}, got {embeddings.shape[1]}"

            # 测试相似度
            query_embedding = embeddings[:1]
            doc_embeddings = embeddings[1:]
            similarities = api_model.compute_similarity(query_embedding, doc_embeddings)
            print(f"Similarities: {similarities}")

        print("\nAll API tests passed!")
        return True

    except Exception as e:
        logger.error(f"API test failed: {e}")
        return False


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    # 首先测试API模型
    if test_api_model_only():
        print("API model works perfectly!")
    else:
        print("API model failed, falling back to general test...")
        test_embedding_model()
