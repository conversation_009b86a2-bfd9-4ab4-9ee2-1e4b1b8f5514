"""
使用text-embedding-v4 API的RAG系统演示
基于InnovationRAGSystem，使用API进行向量编码
"""

import os
import sys
import json
import logging
from pathlib import Path
import time
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from RAGSystem.rag_system import InnovationRAGSystem, create_cross_encoder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def analyze_score_composition(matches: Dict[str, Any], use_cross_encoder: bool = True):
    """
    分析匹配结果中分数的组成

    Args:
        matches: 匹配结果字典
        use_cross_encoder: 是否使用了交叉编码器
    """
    print("分数组成分析:")
    if use_cross_encoder:
        print("(最终分数 = 0.3 × 向量分数 + 0.7 × 交叉编码器分数)")
    else:
        print("(仅使用向量分数)")
    print("-" * 80)

    for _, match_data in matches.items():
        innovation = match_data['innovation']
        matched_code = match_data['matched_code']

        if not matched_code:
            continue

        print(f"\n📋 {innovation.get('title', 'Unknown')}")
        if use_cross_encoder:
            print(f"{'排名':<4} {'元素名称':<25} {'向量分数':<10} {'交叉分数':<10} {'最终分数':<10}")
        else:
            print(f"{'排名':<4} {'元素名称':<25} {'向量分数':<10}")
        print("-" * 70)

        for i, code in enumerate(matched_code[:5], 1):  # 显示前5个
            metadata = code['metadata']
            final_score = code.get('final_score', code.get('score', 0))
            vector_score = code.get('score', 0)
            rerank_score = code.get('rerank_score', None)

            element_name = metadata.get('element_name', 'Unknown')[:24]

            if use_cross_encoder and rerank_score is not None:
                print(f"{i:<4} {element_name:<25} {vector_score:<10.4f} {rerank_score:<10.4f} {final_score:<10.4f}")
            else:
                print(f"{i:<4} {element_name:<25} {vector_score:<10.4f}")

        if matched_code:
            # 统计分析
            vector_scores = [code.get('score', 0) for code in matched_code]
            rerank_scores = [code.get('rerank_score', 0) for code in matched_code if code.get('rerank_score') is not None]
            final_scores = [code.get('final_score', code.get('score', 0)) for code in matched_code]

            print(f"\n统计信息:")
            print(f"  向量分数范围: {min(vector_scores):.4f} - {max(vector_scores):.4f}")
            if rerank_scores and use_cross_encoder:
                print(f"  交叉分数范围: {min(rerank_scores):.4f} - {max(rerank_scores):.4f}")
            print(f"  最终分数范围: {min(final_scores):.4f} - {max(final_scores):.4f}")

            # 分析交叉编码器的影响
            if rerank_scores and use_cross_encoder:
                vector_contribution = [0.3 * v for v in vector_scores[:len(rerank_scores)]]
                cross_contribution = [0.7 * r for r in rerank_scores]

                avg_vector_contrib = sum(vector_contribution) / len(vector_contribution)
                avg_cross_contrib = sum(cross_contribution) / len(cross_contribution)

                print(f"  平均向量贡献: {avg_vector_contrib:.4f} (30%)")
                print(f"  平均交叉贡献: {avg_cross_contrib:.4f} (70%)")

                if avg_cross_contrib > avg_vector_contrib:
                    print(f"  ✅ 交叉编码器起主导作用")
                else:
                    print(f"  ⚠️  向量检索起主导作用")
                    
def run_api_rag_demo():
    """运行API RAG演示"""
    logger.info("=" * 60)
    logger.info("开始运行API RAG系统演示")
    logger.info("=" * 60)

    start_time = time.time()

    try:
        # 创建API RAG系统
        logger.info("创建API RAG系统...")
        rag_system = InnovationRAGSystem(
            model_type="api",
            dimension=1024,
            index_type="flat",
            use_cross_encoder=True,
            recall_top_k=50,
            rerank_top_k=20
        )

        # 设置完整系统
        logger.info("设置完整系统...")
        rag_system.setup_complete_system("I-GCG")

        # 执行创新点匹配
        logger.info("执行创新点匹配...")
        matches = rag_system.match_innovations_to_code(confidence_threshold=0.15)

        # 生成并显示报告
        report = rag_system.generate_comprehensive_report(matches)
        print("\n" + "="*80)
        print(report)
        print("="*80)

        # 显示详细的分数分析
        print("\n🔍 详细分数分析:")
        print("="*80)
        analyze_score_composition(matches, rag_system.use_cross_encoder)

        # 保存结果
        saved_files = rag_system.save_results(matches)
        print(f"\n📁 结果文件:")
        for file_type, file_path in saved_files.items():
            print(f"  📄 {file_type}: {file_path}")

        # 保存向量数据库
        logger.info("保存向量数据库...")
        rag_system.save_vector_database()

        # 显示系统统计
        stats = rag_system.get_system_statistics()
        print(f"\n📊 系统统计:")
        print(f"  模型类型: {stats['system_config']['model_type']}")
        print(f"  向量维度: {stats['system_config']['dimension']}")
        print(f"  索引类型: {stats['system_config']['index_type']}")
        print(f"  使用交叉编码器: {stats['system_config']['use_cross_encoder']}")

        if 'retrieval_stats' in stats:
            db_stats = stats['retrieval_stats']['database_stats']
            print(f"  数据库文档数: {db_stats['total_documents']}")
            print(f"  嵌入维度: {db_stats['embedding_dim']}")

        elapsed_time = time.time() - start_time
        logger.info(f"API RAG系统运行完成，耗时: {elapsed_time:.2f}秒")

        return True

    except Exception as e:
        logger.error(f"API RAG系统运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 API RAG系统演示")
    print("🤖 使用text-embedding-v4 API + Qwen3-Reranker-4B")
    print("=" * 60)
    
    # 检查环境变量
    api_key = os.getenv("DASHSCOPE_API_KEY")
    api_base = os.getenv("DASHSCOPE_API_BASE")
    
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        return
    
    if not api_base:
        print("❌ 未找到DASHSCOPE_API_BASE环境变量")
        return
    
    print("✅ API配置检查通过")
    
    # 检查数据文件
    base_dir = Path(__file__).parent.parent
    
    innovations_file = base_dir / "ProcressedData" / "innovations" / "I-GCG_with_descriptions_innovations_innovations.json"
    if not innovations_file.exists():
        print(f"❌ 创新点数据不存在: {innovations_file}")
        return
    
    code_analysis_file = base_dir / "ProcressedData" / "code_analysis" / "I-GCG" / "rag_chunks.json"
    if not code_analysis_file.exists():
        print(f"❌ 代码分析结果不存在: {code_analysis_file}")
        return
    
    print("✅ 数据文件检查通过")

    # 检查交叉编码器模型
    reranker_path = base_dir / "models" / "Qwen3-Reranker" / "Qwen3-Reranker-4B"
    if not reranker_path.exists():
        print(f"⚠️  交叉编码器模型不存在: {reranker_path}")
        print("将回退到MS-MARCO模型")
    else:
        print("✅ 交叉编码器模型检查通过")

    # 运行演示
    success = run_api_rag_demo()

    if success:
        print("\n🎉 API RAG系统演示完成!")
        print("📁 查看结果文件:")
        results_dir = base_dir / "ProcressedData" / "rag_results"
        print(f"   - 结果目录: {results_dir}")
    else:
        print("\n😞 API RAG系统演示失败")


if __name__ == "__main__":
    main()
