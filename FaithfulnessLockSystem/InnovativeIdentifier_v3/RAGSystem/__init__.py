"""
RAGSystem - 创新点与代码匹配的RAG系统

主要功能：
1. 使用Qwen3_Embedding模型进行文本嵌入
2. 构建代码向量数据库
3. 实现双阶段检索（召回+重排）
4. 将创新点匹配到相应的代码模块
"""

from .embedding_model import Qwen3EmbeddingModel, CrossEncoder
from .vector_database import VectorDatabase, CodeVectorDatabase
from .retriever import TwoStageRetriever, InnovationCodeMatcher
from .rag_system import InnovationRAGSystem

__version__ = "1.0.0"
__author__ = "InnovativeIdentifier Team"

__all__ = [
    'Qwen3EmbeddingModel',
    'CrossEncoder',
    'VectorDatabase',
    'CodeVectorDatabase',
    'TwoStageRetriever',
    'InnovationCodeMatcher',
    'InnovationRAGSystem'
]
