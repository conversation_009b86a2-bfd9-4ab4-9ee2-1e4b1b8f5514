"""
检索器模块
实现双阶段检索：向量召回 + 交叉编码器重排
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
import logging
from .embedding_model import Qwen3EmbeddingModel, CrossEncoder
from .vector_database import CodeVectorDatabase

logger = logging.getLogger(__name__)


class TwoStageRetriever:
    """双阶段检索器：召回 + 重排"""
    
    def __init__(self, 
                 embedding_model: Qwen3EmbeddingModel,
                 vector_db: CodeVectorDatabase,
                 cross_encoder: CrossEncoder = None,
                 recall_top_k: int = 50,
                 rerank_top_k: int = 10):
        """
        初始化双阶段检索器
        
        Args:
            embedding_model: 嵌入模型
            vector_db: 向量数据库
            cross_encoder: 交叉编码器（可选）
            recall_top_k: 召回阶段返回的文档数量
            rerank_top_k: 重排阶段返回的文档数量
        """
        self.embedding_model = embedding_model
        self.vector_db = vector_db
        self.cross_encoder = cross_encoder
        self.recall_top_k = recall_top_k
        self.rerank_top_k = rerank_top_k
        
        logger.info(f"Initialized TwoStageRetriever with recall_top_k={recall_top_k}, rerank_top_k={rerank_top_k}")
    
    def retrieve(self, query: str, 
                 filters: Dict[str, Any] = None,
                 use_reranking: bool = True) -> List[Dict[str, Any]]:
        """
        执行检索
        
        Args:
            query: 查询文本
            filters: 过滤条件
            use_reranking: 是否使用重排
            
        Returns:
            检索结果列表
        """
        # 第一阶段：向量召回
        recall_results = self._recall_stage(query, filters)
        
        if not recall_results:
            logger.warning("No documents found in recall stage")
            return []
        
        # 第二阶段：交叉编码器重排
        if use_reranking and self.cross_encoder is not None:
            final_results = self._rerank_stage(query, recall_results)
        else:
            final_results = recall_results[:self.rerank_top_k]
        
        logger.info(f"Retrieved {len(final_results)} documents for query: {query[:50]}...")
        return final_results
    
    def _recall_stage(self, query: str,
                     filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """召回阶段：使用向量相似度检索"""
        # 编码查询
        query_embedding = self.embedding_model.encode([query])[0]

        # 向量检索 - 优先使用排除Variable的搜索
        if filters is None or 'element_type' not in filters:
            # 如果没有特定的元素类型过滤，使用排除Variable的搜索
            results = self.vector_db.search_exclude_variables(
                query_embedding,
                top_k=self.recall_top_k
            )
        else:
            # 如果有特定的元素类型过滤，使用原有的搜索方法
            results = self.vector_db.search(
                query_embedding,
                top_k=self.recall_top_k,
                filter_metadata=filters
            )

        logger.debug(f"Recall stage found {len(results)} documents")
        return results
    
    def _rerank_stage(self, query: str, 
                     recall_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """重排阶段：使用交叉编码器重新排序"""
        if not recall_results:
            return []
        
        # 准备查询-文档对
        query_doc_pairs = []
        for result in recall_results:
            query_doc_pairs.append((query, result['document']))
        
        # 计算重排分数
        rerank_scores = self.cross_encoder.score(query_doc_pairs)
        
        # 更新结果并重新排序
        for i, score in enumerate(rerank_scores):
            recall_results[i]['rerank_score'] = score
            # 组合原始分数和重排分数
            recall_results[i]['final_score'] = (
                0.3 * recall_results[i]['score'] + 0.7 * score
            )
        
        # 按最终分数排序
        reranked_results = sorted(
            recall_results, 
            key=lambda x: x['final_score'], 
            reverse=True
        )
        
        logger.debug(f"Rerank stage completed, returning top {self.rerank_top_k} documents")
        return reranked_results[:self.rerank_top_k]
    
    def retrieve_by_innovation(self, innovation: Dict[str, Any],
                              filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        根据创新点检索相关代码
        
        Args:
            innovation: 创新点信息
            filters: 过滤条件
            
        Returns:
            检索结果列表
        """
        # 构建查询文本
        query_parts = []
        
        if innovation.get('title'):
            query_parts.append(innovation['title'])
        
        if innovation.get('description'):
            query_parts.append(innovation['description'])
        
        if innovation.get('technical_details'):
            query_parts.append(innovation['technical_details'])
        
        query = " ".join(query_parts)
        
        # 添加创新点类型到过滤条件
        if filters is None:
            filters = {}
        
        # 根据创新点类型调整检索策略
        innovation_type = innovation.get('type', '')
        if innovation_type == 'architecture':
            # 架构创新更关注类和模型定义
            filters['element_type'] = ['Class']
        elif innovation_type == 'technique':
            # 技术创新更关注函数实现
            filters['element_type'] = ['Function', 'Class']
        elif innovation_type == 'method':
            # 方法创新关注函数实现
            filters['element_type'] = ['Function', 'Class']
        
        return self.retrieve(query, filters)
    
    def batch_retrieve(self, queries: List[str],
                      filters: List[Dict[str, Any]] = None) -> List[List[Dict[str, Any]]]:
        """
        批量检索
        
        Args:
            queries: 查询列表
            filters: 过滤条件列表
            
        Returns:
            每个查询的检索结果列表
        """
        if filters is None:
            filters = [None] * len(queries)
        
        results = []
        for query, filter_condition in zip(queries, filters):
            result = self.retrieve(query, filter_condition)
            results.append(result)
        
        return results
    
    def get_retrieval_statistics(self) -> Dict[str, Any]:
        """获取检索统计信息"""
        db_stats = self.vector_db.get_code_statistics()
        
        stats = {
            'database_stats': db_stats,
            'retriever_config': {
                'recall_top_k': self.recall_top_k,
                'rerank_top_k': self.rerank_top_k,
                'has_cross_encoder': self.cross_encoder is not None,
                'embedding_dim': self.embedding_model.get_embedding_dim()
            }
        }
        
        return stats


class InnovationCodeMatcher:
    """创新点与代码匹配器"""
    
    def __init__(self, retriever: TwoStageRetriever):
        """
        初始化匹配器
        
        Args:
            retriever: 双阶段检索器
        """
        self.retriever = retriever
        
    def match_innovations_to_code(self, innovations: List[Dict[str, Any]],
                                 confidence_threshold: float = 0.5) -> Dict[str, Any]:
        """
        将创新点匹配到代码模块
        
        Args:
            innovations: 创新点列表
            confidence_threshold: 置信度阈值
            
        Returns:
            匹配结果
        """
        matches = {}
        
        for innovation in innovations:
            innovation_id = f"innovation_{innovation.get('rank', len(matches))}"
            
            # 检索相关代码
            code_results = self.retriever.retrieve_by_innovation(innovation)
            
            # 过滤低置信度结果
            filtered_results = []
            for result in code_results:
                final_score = result.get('final_score', result.get('score', 0))
                if final_score >= confidence_threshold:
                    filtered_results.append(result)
            
            matches[innovation_id] = {
                'innovation': innovation,
                'matched_code': filtered_results,
                'match_count': len(filtered_results),
                'avg_confidence': np.mean([r.get('final_score', r.get('score', 0)) 
                                         for r in filtered_results]) if filtered_results else 0
            }
            
            logger.info(f"Innovation '{innovation.get('title', 'Unknown')}' matched to {len(filtered_results)} code segments")
        
        return matches
    
    def generate_match_report(self, matches: Dict[str, Any]) -> str:
        """生成匹配报告"""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("创新点与代码匹配报告")
        report_lines.append("=" * 80)
        report_lines.append("")
        
        total_innovations = len(matches)
        total_matches = sum(match['match_count'] for match in matches.values())
        
        report_lines.append(f"总创新点数: {total_innovations}")
        report_lines.append(f"总匹配代码段数: {total_matches}")
        report_lines.append(f"平均每个创新点匹配代码段数: {total_matches/total_innovations:.1f}")
        report_lines.append("")
        
        for innovation_id, match_data in matches.items():
            innovation = match_data['innovation']
            matched_code = match_data['matched_code']
            
            report_lines.append(f"创新点 {innovation.get('rank', 'N/A')}: {innovation.get('title', 'Unknown')}")
            report_lines.append(f"类型: {innovation.get('type', 'Unknown')}")
            report_lines.append(f"匹配代码段数: {len(matched_code)}")
            report_lines.append(f"平均置信度: {match_data['avg_confidence']:.3f}")
            report_lines.append("")
            
            if matched_code:
                report_lines.append("匹配的代码段:")
                for i, code in enumerate(matched_code[:5]):  # 只显示前5个
                    metadata = code['metadata']
                    final_score = code.get('final_score', code.get('score', 0))
                    vector_score = code.get('score', 0)
                    rerank_score = code.get('rerank_score', None)

                    element_name = metadata.get('element_name', 'Unknown')
                    element_type = metadata.get('element_type', 'Unknown')
                    file_path = metadata.get('file_path', 'Unknown')

                    report_lines.append(f"  {i+1}. {element_name} ({element_type})")
                    report_lines.append(f"     文件: {file_path.split('/')[-1] if file_path != 'Unknown' else 'Unknown'}")

                    if rerank_score is not None:
                        # 显示分数组成
                        report_lines.append(f"     置信度: {final_score:.3f} (向量: {vector_score:.3f} + 交叉: {rerank_score:.3f})")
                    else:
                        # 只有向量分数
                        report_lines.append(f"     置信度: {final_score:.3f} (仅向量分数)")
                
                if len(matched_code) > 5:
                    report_lines.append(f"  ... 还有 {len(matched_code) - 5} 个匹配项")
            else:
                report_lines.append("未找到匹配的代码段")
            
            report_lines.append("")
            report_lines.append("-" * 60)
            report_lines.append("")
        
        return "\n".join(report_lines)
    
    def export_matches_to_json(self, matches: Dict[str, Any],
                              output_path: str):
        """导出匹配结果到JSON文件"""
        import json
        from datetime import datetime

        def convert_numpy_types(obj):
            """递归转换numpy类型为Python原生类型"""
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj

        # 准备导出数据并转换numpy类型
        export_data = {
            'summary': {
                'total_innovations': len(matches),
                'total_matches': sum(match['match_count'] for match in matches.values()),
                'timestamp': datetime.now().isoformat()
            },
            'matches': convert_numpy_types(matches)
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Matches exported to {output_path}")


def test_retriever():
    """测试检索器"""
    # 这里只是一个简单的测试框架
    # 实际测试需要加载真实的模型和数据
    print("Retriever test framework ready")
    return True


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_retriever()
