#!/usr/bin/env python3
"""
下载Qwen3-<PERSON>ranker模型脚本
支持下载0.6B和4B版本的Qwen3-Reranker模型
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from huggingface_hub import snapshot_download
import torch

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 模型配置
MODELS = {
    "0.6B": {
        "repo_id": "Qwen/Qwen3-Reranker-0.6B",
        "local_dir": "Qwen3-Reranker-0.6B",
        "description": "Qwen3 Reranker 0.6B - 轻量级重排序模型"
    },
    "4B": {
        "repo_id": "Qwen/Qwen3-Reranker-4B", 
        "local_dir": "Qwen3-Reranker-4B",
        "description": "Qwen3 Reranker 4B - 高性能重排序模型"
    }
}


def check_prerequisites():
    """检查前提条件"""
    logger.info("检查前提条件...")
    
    # 检查CUDA
    if torch.cuda.is_available():
        logger.info(f"CUDA可用，设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            logger.info(f"  GPU {i}: {props.name}, 内存: {props.total_memory / 1024**3:.1f}GB")
    else:
        logger.warning("CUDA不可用，将使用CPU")
    
    # 检查磁盘空间
    import shutil
    free_space = shutil.disk_usage('.').free / 1024**3
    logger.info(f"可用磁盘空间: {free_space:.1f}GB")
    
    if free_space < 20:
        logger.warning("磁盘空间可能不足，建议至少20GB空闲空间")
    
    return True


def download_model(model_size: str, base_dir: str, force: bool = False):
    """
    下载指定大小的Qwen3-Reranker模型
    
    Args:
        model_size: 模型大小 ("0.6B" 或 "4B")
        base_dir: 基础目录
        force: 是否强制重新下载
    """
    if model_size not in MODELS:
        raise ValueError(f"不支持的模型大小: {model_size}，支持的大小: {list(MODELS.keys())}")
    
    model_config = MODELS[model_size]
    repo_id = model_config["repo_id"]
    local_dir = Path(base_dir) / "Qwen3-Reranker" / model_config["local_dir"]
    
    logger.info(f"开始下载 {model_config['description']}")
    logger.info(f"仓库ID: {repo_id}")
    logger.info(f"本地目录: {local_dir}")
    
    # 检查是否已存在
    if local_dir.exists() and not force:
        logger.info(f"模型已存在于 {local_dir}")
        if input("是否重新下载? (y/N): ").lower() != 'y':
            logger.info("跳过下载")
            return str(local_dir)
    
    # 创建目录
    local_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 下载模型
        logger.info("开始从HuggingFace下载模型...")
        snapshot_download(
            repo_id=repo_id,
            local_dir=str(local_dir),
            local_dir_use_symlinks=False,
            resume_download=True
        )
        
        logger.info(f"✅ 模型下载完成: {local_dir}")
        
        # 验证下载
        essential_files = ["config.json", "pytorch_model.bin"]
        missing_files = []
        
        for file in essential_files:
            if not (local_dir / file).exists():
                # 检查是否有分片文件
                if file == "pytorch_model.bin":
                    shard_files = list(local_dir.glob("pytorch_model-*.bin"))
                    if not shard_files:
                        missing_files.append(file)
                else:
                    missing_files.append(file)
        
        if missing_files:
            logger.warning(f"缺少文件: {missing_files}")
        else:
            logger.info("✅ 模型文件验证通过")
        
        return str(local_dir)
        
    except Exception as e:
        logger.error(f"下载失败: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="下载Qwen3-Reranker模型")
    parser.add_argument(
        "--model-size", 
        choices=["0.6B", "4B", "all"],
        default="all",
        help="要下载的模型大小"
    )
    parser.add_argument(
        "--base-dir",
        default="../models",
        help="模型保存的基础目录"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="强制重新下载，即使模型已存在"
    )
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("Qwen3-Reranker 模型下载工具")
    logger.info("=" * 60)
    
    # 检查前提条件
    if not check_prerequisites():
        sys.exit(1)
    
    # 解析基础目录
    base_dir = Path(args.base_dir).resolve()
    logger.info(f"模型将保存到: {base_dir}")
    
    try:
        if args.model_size == "all":
            # 下载所有模型
            logger.info("下载所有Qwen3-Reranker模型...")
            downloaded_models = []
            
            for size in ["0.6B", "4B"]:
                try:
                    model_path = download_model(size, str(base_dir), args.force)
                    downloaded_models.append((size, model_path))
                except Exception as e:
                    logger.error(f"下载 {size} 模型失败: {e}")
            
            # 总结
            logger.info("\n" + "=" * 60)
            logger.info("下载完成总结:")
            for size, path in downloaded_models:
                logger.info(f"  ✅ Qwen3-Reranker-{size}: {path}")
            
        else:
            # 下载指定模型
            model_path = download_model(args.model_size, str(base_dir), args.force)
            logger.info(f"\n✅ 下载完成: {model_path}")
    
    except KeyboardInterrupt:
        logger.info("\n用户中断下载")
        sys.exit(1)
    except Exception as e:
        logger.error(f"下载过程中出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
