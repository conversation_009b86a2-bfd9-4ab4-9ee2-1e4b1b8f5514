import os
import re
import json
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables from .env file
load_dotenv()

INNOVATION_EXTRACTION_PROMPT = """
You are an **innovation detector** for academic papers. Your goal is to identify and extract innovative contributions, novel approaches, and key technical innovations from the given paragraph.

### What to Look For (Be Liberal in Detection):
1. **Novel Methods/Algorithms**: New approaches, algorithms, or techniques, including novel applications of existing methods
2. **Architectural Innovations**: New model architectures, network designs, or structural improvements
3. **Technical Contributions**: Novel loss functions, training strategies, optimization methods, new modules or components
4. **Problem-Solving Approaches**: Creative solutions to existing problems or new problem formulations
5. **Performance Improvements**: Significant advances in accuracy, efficiency, or other metrics
6. **Theoretical Contributions**: New theoretical insights, proofs, or mathematical formulations
7. **Novel Combinations**: Innovative combinations of existing techniques in new ways
8. **New Applications**: Applying techniques to new domains or problems in novel ways

### Extraction Guidelines:
1. **Be Inclusive**: Extract content that represents something NEW, IMPROVED, or NOVEL, even if it builds on existing work
2. **Look for "Propose", "Introduce", "Design", "Develop"**: These often indicate innovations
3. **Technical Details Matter**: Extract concrete technical details and mathematical formulations
4. **Novel Applications Count**: Even applying existing methods in new ways can be innovative
5. **Verbatim Extraction**: Copy relevant text exactly as it appears

### Examples of What to Extract:
- "We propose a novel biaffine attention module..."
- "We introduce a multi-channel graph structure..."
- "We design an effective refining strategy..."
- "We utilize a biaffine attention module to capture relation probability distribution..."
- "We define ten types of relations for ASTE task..."

### Output Format:
If innovations are found, output a JSON structure:
```json
{
  "innovations": [
    {
      "type": "method|architecture|technique|approach|theory",
      "title": "Brief descriptive title",
      "description": "Detailed description extracted verbatim from text",
      "significance": "Why this is innovative/novel"
    }
  ]
}
```

If no innovations are found, output: `"no innovations"`

### Important Notes:
- Be generous in identifying innovations - if something is presented as new or novel by the authors, extract it
- Look for technical contributions, new modules, novel applications, or improved approaches
- Focus on what the authors claim as their contributions

---

**Full Paper Content (for context):**
{content}

**Paragraph to Analyze:**
{paragraph}
"""

DEDUPLICATION_PROMPT = """
You are an **innovation deduplicator**. Given a list of extracted innovations from a paper, your task is to:

1. **Remove Duplicates**: Identify and merge similar or overlapping innovations
2. **Rank by Importance**: Keep only the most significant and core innovations
3. **Maintain Quality**: Ensure each innovation is truly novel and well-described

### Strict Deduplication Guidelines:
- **Identify Similar Titles**: Merge innovations with similar titles (e.g., "EMC-GCN" and "Enhanced Multi-Channel Graph Convolutional Network")
- **Combine Related Descriptions**: Merge innovations that describe the same core concept
- **Keep Maximum 4-5 Core Innovations**: Be very selective
- **Prioritize Technical Depth**: Choose innovations with the most technical detail
- **Remove Redundancy**: If two innovations describe the same method/architecture, combine them into one comprehensive entry

### Specific Merging Rules:
- If multiple innovations mention the same model/architecture name (e.g., "EMC-GCN"), merge them
- If innovations describe the same technical component (e.g., "biaffine attention"), keep only the most detailed one
- If innovations are about the same general approach, combine into a single comprehensive innovation

### Output Format:
Return a clean JSON structure with deduplicated and ranked innovations:
```json
{
  "core_innovations": [
    {
      "rank": 1,
      "type": "method|architecture|technique|approach|theory",
      "title": "Clear, specific title (avoid duplicates)",
      "description": "Comprehensive description combining all related innovations",
      "significance": "Why this is a core contribution",
      "technical_details": "Key technical aspects from all merged innovations"
    }
  ],
  "total_count": 4
}
```

### Important Notes:
- Be VERY strict about removing duplicates
- If in doubt whether two innovations are similar, merge them
- Focus on distinct, non-overlapping contributions
- Each final innovation should represent a clearly different technical contribution

### Innovations to Process:
{innovations}
"""


def read_md_file(file_path):
    """Read markdown file content"""
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()


def split_paragraphs_by_markdown(content):
    """Split content into paragraphs by markdown headers"""
    paragraphs = re.split(r'(?=\n#{1,6}\s*)', content)
    return [p.strip() for p in paragraphs if p.strip()]


def extract_paragraph_title(paragraph):
    """Extract the title/header from a paragraph"""
    match = re.match(r'^(#{1,6})\s*(.*\S)', paragraph.strip())
    if match:
        return match.group(0).strip()
    return "Unknown Title"


def clean_paragraph_content(paragraph, client, model="gpt-4o", max_tokens=8000, temperature=0):
    """Clean paragraph content using GPT model, similar to md_process.py"""

    system_content = """
You are a text cleaner and extractor for academic papers. Your task is to clean and extract information from a paragraph of text, adhering to the following rules:

### Tasks:
1. **Image Markdown Syntax:**
   - Identify any image markdown syntax (e.g., `![caption](url)`).
   - Extract the **caption or description** that immediately follows the image syntax.
   - Remove the image link and markdown syntax while keeping the extracted caption.

2. **Table Content:**
   - Retain all tables (e.g., HTML or Markdown table structures) in their original form.
   - Ensure table formatting is preserved and readable.

3. **Text Cleanup:**
   - Remove unnecessary spaces, broken lines, or redundant formatting.
   - Correct spelling and grammar errors to ensure readability.
   - Retain all symbolic or mathematical expressions, converting them into a consistent LaTeX format if necessary (e.g., `$f(\\cdot)$` should become `\\( f(\\cdot) \\)`).

4. **Technical Content Preservation:**
   - Preserve all technical terms, algorithm names, and method descriptions exactly.
   - Keep all numerical values, parameters, and experimental settings.
   - Maintain the original technical language and terminology.

5. **Output Format:**
   - Provide the cleaned paragraph in Markdown format.
   - Maintain the structure and technical accuracy of the content.
   - Focus on preserving innovation-related content and technical details.

### Example Input:
- Paragraph:
    ```
    ![Example Image](example.com/image.png)
    > **Picture description**: This shows our novel architecture.
    We propose a new attention mechanism    with α=0.5 and β=0.3 parameters.
    The formula is $f(x) = \\alpha x + \\beta$.
    ```

### Example Output:
    ```
    We propose a new attention mechanism with α=0.5 and β=0.3 parameters.
    The formula is \\( f(x) = \\alpha x + \\beta \\).
    > **Picture description**: This shows our novel architecture.
    ```

### Notes:
- Do not summarize or interpret the content; focus solely on cleaning and formatting.
- Ensure mathematical and technical content is preserved exactly as provided.
- Prioritize readability while maintaining technical accuracy.

### Paragraph to Process:
    """

    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_content},
                {"role": "user", "content": paragraph}
            ],
            max_tokens=max_tokens,
            temperature=temperature
        )

        # Print token usage information
        usage_info = response.usage
        if usage_info:
            print(f"Cleaning - Prompt tokens: {usage_info.prompt_tokens}, "
                  f"Completion tokens: {usage_info.completion_tokens}, "
                  f"Total tokens: {usage_info.total_tokens}")

        cleaned_paragraph = response.choices[0].message.content.strip()
        return cleaned_paragraph

    except Exception as e:
        print(f"Error cleaning paragraph: {str(e)}")
        # Fallback to basic cleaning
        cleaned = re.sub(r'\n\s*\n', '\n\n', paragraph)
        cleaned = re.sub(r'[ \t]+', ' ', cleaned)
        return cleaned.strip()


def extract_innovations_from_paragraph(paragraph, entire_paper_content, client, model="gpt-4o"):
    """Extract innovations from a single paragraph"""

    system_content = INNOVATION_EXTRACTION_PROMPT.replace("{content}", entire_paper_content)\
                                                 .replace("{paragraph}", paragraph)

    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": system_content}
            ],
            max_tokens=4000,  # 进一步增加 token 限制以减少截断
            temperature=0
        )

        # Print token usage information
        usage_info = response.usage
        if usage_info:
            print(f"Innovation extraction - Prompt tokens: {usage_info.prompt_tokens}, "
                  f"Completion tokens: {usage_info.completion_tokens}, "
                  f"Total tokens: {usage_info.total_tokens}")

        result = response.choices[0].message.content.strip()

        # Handle different response formats
        if result.lower() == "no innovations" or result.lower() == '"no innovations"':
            return []

        # Try to extract JSON from markdown code blocks
        if "```json" in result:
            json_start = result.find("```json") + 7
            json_end = result.find("```", json_start)
            if json_end != -1:
                json_content = result[json_start:json_end].strip()
            else:
                # Handle case where closing ``` is missing (truncated response)
                json_content = result[json_start:].strip()
                print(f"Warning: Truncated JSON response detected (missing closing ```)")

            if json_content.lower() == '"no innovations"' or json_content.lower() == 'no innovations':
                return []

            try:
                # Clean the JSON content before parsing
                cleaned_json = clean_json_escape_sequences(json_content)
                innovations_data = json.loads(cleaned_json)
                return innovations_data.get("innovations", [])
            except json.JSONDecodeError as e:
                print(f"Warning: Could not parse JSON from code block (likely truncated): {json_content[:100]}...")
                print(f"JSON Error: {str(e)}")
                print(f"Skipping this paragraph due to incomplete response. Consider increasing max_tokens.")
                return []

        # Try to parse as direct JSON
        try:
            cleaned_result = clean_json_escape_sequences(result)
            innovations_data = json.loads(cleaned_result)
            return innovations_data.get("innovations", [])
        except json.JSONDecodeError:
            print(f"Warning: Could not parse JSON response: {result[:200]}...")
            print(f"Skipping this paragraph due to malformed JSON.")
            return []

    except Exception as e:
        print(f"Error extracting innovations: {str(e)}")
        return []

def clean_json_escape_sequences(json_str):
    """Clean invalid escape sequences in JSON strings"""
    import re

    # Define specific LaTeX/mathematical symbols that need to be cleaned
    # Use word boundaries to avoid partial matches
    latex_symbols = {
        r'\\alpha\b': 'alpha',
        r'\\beta\b': 'beta',
        r'\\gamma\b': 'gamma',
        r'\\delta\b': 'delta',
        r'\\epsilon\b': 'epsilon',
        r'\\lambda\b': 'lambda',
        r'\\mu\b': 'mu',
        r'\\theta\b': 'theta',
        r'\\pi\b': 'pi',
        r'\\sigma\b': 'sigma',
        r'\\omega\b': 'omega',
        r'\\sum\b': 'sum',
        r'\\prod\b': 'prod',
        r'\\int\b': 'int',
        r'\\partial\b': 'partial',
        r'\\nabla\b': 'nabla',
        r'\\infty\b': 'infinity',
        r'\\cdot\b': 'cdot',
        r'\\times\b': 'times',
        r'\\leq\b': 'leq',
        r'\\geq\b': 'geq',
        r'\\neq\b': 'neq',
        r'\\mathbb\{([^}]+)\}': r'mathbb{\1}',
        r'\\mathrm\{([^}]+)\}': r'mathrm{\1}',
        r'\\text\{([^}]+)\}': r'text{\1}',
        r'\\frac\{([^}]+)\}\{([^}]+)\}': r'frac{\1}{\2}'
    }

    # Apply replacements
    for pattern, replacement in latex_symbols.items():
        json_str = re.sub(pattern, replacement, json_str)

    # Handle any remaining invalid escape sequences
    # This catches things like \path, \file, etc.
    # Only replace if it's not a valid JSON escape sequence
    valid_escapes = {'"', '\\', '/', 'b', 'f', 'n', 'r', 't', 'u'}

    def replace_invalid_escape(match):
        escaped_char = match.group(1)
        if escaped_char not in valid_escapes:
            return escaped_char  # Remove the backslash
        else:
            return match.group(0)  # Keep valid escapes

    # Find all \X patterns and replace invalid ones
    json_str = re.sub(r'\\(.)', replace_invalid_escape, json_str)

    return json_str


def deduplicate_and_rank_innovations(all_innovations, client, model="gpt-4o"):
    """Deduplicate and rank innovations to keep only core ones"""

    if not all_innovations:
        return []

    # Convert innovations to JSON string for processing
    innovations_json = json.dumps(all_innovations, indent=2)

    system_content = DEDUPLICATION_PROMPT.replace("{innovations}", innovations_json)

    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": system_content}
            ],
            max_tokens=3000,
            temperature=0
        )

        # Print token usage information
        usage_info = response.usage
        if usage_info:
            print(f"Deduplication - Prompt tokens: {usage_info.prompt_tokens}, "
                  f"Completion tokens: {usage_info.completion_tokens}, "
                  f"Total tokens: {usage_info.total_tokens}")

        result = response.choices[0].message.content.strip()

        # Parse the deduplicated result
        # Handle different response formats
        if "```json" in result:
            json_start = result.find("```json") + 7
            json_end = result.find("```", json_start)
            if json_end != -1:
                json_content = result[json_start:json_end].strip()
                try:
                    deduplicated_data = json.loads(json_content)
                    return deduplicated_data.get("core_innovations", [])
                except json.JSONDecodeError:
                    print(f"Warning: Could not parse JSON from code block: {json_content[:100]}...")

        # Try to parse as direct JSON
        try:
            deduplicated_data = json.loads(result)
            return deduplicated_data.get("core_innovations", [])
        except json.JSONDecodeError:
            print(f"Warning: Could not parse deduplication result: {result[:100]}...")
            return all_innovations[:5]  # Fallback: return first 5 innovations

    except Exception as e:
        print(f"Error during deduplication: {str(e)}")
        return all_innovations[:5]  # Fallback: return first 5 innovations


def save_innovations_to_file(innovations, output_path):
    """Save innovations to both JSON and Markdown formats"""

    # Save as JSON
    json_path = output_path.replace('.md', '_innovations.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump({"innovations": innovations}, f, indent=2, ensure_ascii=False)

    # Save as Markdown
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# Paper Innovations\n\n")

        if not innovations:
            f.write("No significant innovations detected in this paper.\n")
            return

        f.write(f"**Total Core Innovations:** {len(innovations)}\n\n")

        for i, innovation in enumerate(innovations, 1):
            rank = innovation.get('rank', i)
            f.write(f"## Innovation {rank}: {innovation.get('title', 'Untitled')}\n\n")
            f.write(f"**Type:** {innovation.get('type', 'Unknown')}\n\n")
            f.write(f"**Description:**\n{innovation.get('description', 'No description')}\n\n")
            f.write(f"**Significance:**\n{innovation.get('significance', 'No significance noted')}\n\n")

            if 'technical_details' in innovation:
                f.write(f"**Technical Details:**\n{innovation['technical_details']}\n\n")

            f.write("---\n\n")


def process_paper_for_innovations(paper_content, output_path):
    """Process entire paper to extract and deduplicate innovations"""

    # Initialize OpenAI client
    api_key = os.getenv("OPENAI_API_KEY")
    api_base = os.getenv("OPENAI_API_BASE")

    if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment variables")

    client = OpenAI(
        api_key=api_key,
        base_url=api_base
    )

    paragraphs = split_paragraphs_by_markdown(paper_content)
    all_innovations = []

    print(f"Processing {len(paragraphs)} paragraphs for innovations...")

    for i, paragraph in enumerate(paragraphs):
        paragraph_title = extract_paragraph_title(paragraph)
        print(f"Processing paragraph {i+1}/{len(paragraphs)}: {paragraph_title[:50]}...")

        # Skip certain sections that are less likely to contain innovations
        title_lower = paragraph_title.lower()
        # Only skip very specific sections that definitely don't contain innovations
        skip_sections = ['references', 'acknowledgment', 'acknowledgement']
        if any(skip_word in title_lower for skip_word in skip_sections):
            print(f"  Skipping section: {paragraph_title}")
            continue

        # Clean paragraph content using GPT model (similar to md_process.py)
        cleaned_paragraph = clean_paragraph_content(paragraph, client)

        # Add paragraph title back to cleaned content for context
        cleaned_paragraph_with_title = paragraph_title + "\n\n" + cleaned_paragraph

        # Extract innovations from this paragraph
        paragraph_innovations = extract_innovations_from_paragraph(
            cleaned_paragraph_with_title,
            paper_content,
            client
        )

        if paragraph_innovations:
            print(f"  Found {len(paragraph_innovations)} innovations in this paragraph")
            all_innovations.extend(paragraph_innovations)
        else:
            print(f"  No innovations found in this paragraph")

    print(f"\nTotal innovations found: {len(all_innovations)}")

    # Deduplicate and rank innovations
    if all_innovations:
        print("Deduplicating and ranking innovations...")
        core_innovations = deduplicate_and_rank_innovations(all_innovations, client)
        print(f"Core innovations after deduplication: {len(core_innovations)}")
    else:
        core_innovations = []

    # Save results
    save_innovations_to_file(core_innovations, output_path)

    return core_innovations


def main(input_md_folder, input_md_name, output_md_folder):
    """Main function to process papers in a folder"""

    os.makedirs(output_md_folder, exist_ok=True)

    # for filename in os.listdir(input_md_folder):
    #     if filename.endswith(".md"):
    #         input_md_path = os.path.join(input_md_folder, filename)
    #         paper_name = os.path.splitext(filename)[0]
    #         output_md_path = os.path.join(output_md_folder, f"{paper_name}_innovations.md")

    #         print(f"\n{'='*60}")
    #         print(f"Processing paper: {filename}")
    #         print(f"{'='*60}")

    #         # Read paper content
    #         paper_content = read_md_file(input_md_path)

    #         # Process for innovations
    #         innovations = process_paper_for_innovations(paper_content, output_md_path)

    #         print(f"\nCompleted processing {filename}")
    #         print(f"Found {len(innovations)} core innovations")
    #         print(f"Results saved to: {output_md_path}")
    print(f"\n{'='*60}")
    print(f"Processing paper: {input_md_name}")
    print(f"{'='*60}")

    input_md_path = os.path.join(input_md_folder, input_md_name)
    paper_name = os.path.splitext(input_md_name)[0]
    output_md_path = os.path.join(output_md_folder, f"{paper_name}_innovations.md")

    # Read paper content
    paper_content = read_md_file(input_md_path)

    # Process for innovations
    innovations = process_paper_for_innovations(paper_content, output_md_path)

    print(f"\nCompleted processing {input_md_name}")
    print(f"Found {len(innovations)} core innovations")
    print(f"Results saved to: {output_md_path}")


if __name__ == "__main__":
    # Example usage
    input_md_folder = "../ProcressedData/results/"
    input_md_name = "I-GCG_with_descriptions.md"
    output_md_folder = "../ProcressedData/innovations/"
    main(input_md_folder, input_md_name, output_md_folder)