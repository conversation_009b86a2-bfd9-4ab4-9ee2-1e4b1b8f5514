"""
I-GCG后门触发器恢复主程序
"""

import argparse
import json
import os
import time
from datetime import datetime
from typing import Dict, List

from config.model_config import ModelConfig, DEFAULT_MODEL_CONFIG
from config.attack_config import AttackConfig, IGCG_BEST_CONFIG
from config.target_config import TargetConfig, DEFAULT_TARGET_CONFIG
from core.improved_gcg import ImprovedGCG
from utils.model_utils import check_gpu_availability, get_model_memory_usage

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="I-GCG Backdoor Trigger Recovery")
    
    # 基础参数
    parser.add_argument("--mode", type=str, default="single", 
                       choices=["single", "multi", "full"],
                       help="运行模式: single(单目标), multi(多目标), full(完整实验)")
    
    parser.add_argument("--target", type=str, default=None,
                       help="指定单个目标代码 (仅在single模式下使用)")
    
    parser.add_argument("--output_dir", type=str, default="./results",
                       help="输出目录")
    
    # 模型参数
    parser.add_argument("--model_path", type=str, 
                       default="/data1/jcl/models--Zhaorun--CodeQwen1.5-7B-trojan-clas2024-test",
                       help="模型路径")
    
    parser.add_argument("--device", type=str, default="cuda",
                       help="设备 (cuda/cpu)")
    
    # 攻击参数
    parser.add_argument("--num_steps", type=int, default=500,
                       help="优化步数")
    
    parser.add_argument("--batch_size", type=int, default=64,
                       help="批次大小")
    
    parser.add_argument("--num_starts", type=int, default=3,
                       help="多起点数量")
    
    # 实验参数
    parser.add_argument("--save_history", action="store_true",
                       help="保存优化历史")
    
    parser.add_argument("--verbose", action="store_true",
                       help="详细输出")
    
    return parser.parse_args()

def setup_configs(args):
    """设置配置"""
    # 模型配置
    model_config = ModelConfig(
        model_path=args.model_path,
        device=args.device
    )
    
    # 攻击配置
    attack_config = AttackConfig(
        num_steps=args.num_steps,
        batch_size=args.batch_size
    )
    
    # 目标配置
    target_config = DEFAULT_TARGET_CONFIG
    
    return model_config, attack_config, target_config

def run_single_target(attacker: ImprovedGCG, target_code: str, args) -> Dict:
    """运行单目标攻击"""
    print(f"\n{'='*60}")
    print(f"Running single target attack")
    print(f"Target: {target_code}")
    print(f"{'='*60}")
    
    # 使用默认用户提示
    user_prompt = "Write a function to reverse a string."
    
    # 更新任务
    attacker.update_task(user_prompt, target_code)
    
    # 执行多起点攻击
    start_time = time.time()
    result = attacker.multi_start_attack(target_code, args.num_starts)
    end_time = time.time()
    
    # 添加时间信息
    result["execution_time"] = end_time - start_time
    result["target_code"] = target_code
    result["user_prompt"] = user_prompt
    
    return result

def run_multi_target(attacker: ImprovedGCG, target_config: TargetConfig, args) -> Dict:
    """运行多目标攻击"""
    print(f"\n{'='*60}")
    print(f"Running multi-target attack")
    print(f"{'='*60}")
    
    target_list = target_config.get_target_list()
    results = {}
    
    for i, target_code in enumerate(target_list):
        print(f"\n--- Target {i+1}/{len(target_list)} ---")
        result = run_single_target(attacker, target_code, args)
        results[target_code] = result
        
        # 打印简要结果
        if result["success"]:
            print(f"✓ Success: {result['suffix']}")
        else:
            print(f"✗ Failed")
    
    return results

def run_full_experiment(attacker: ImprovedGCG, target_config: TargetConfig, args) -> Dict:
    """运行完整实验"""
    print(f"\n{'='*60}")
    print(f"Running full experiment")
    print(f"{'='*60}")
    
    # 运行多目标攻击
    multi_results = run_multi_target(attacker, target_config, args)
    
    # 计算统计信息
    total_targets = len(multi_results)
    successful_targets = sum(1 for result in multi_results.values() if result["success"])
    success_rate = successful_targets / total_targets if total_targets > 0 else 0
    
    avg_execution_time = sum(result["execution_time"] for result in multi_results.values()) / total_targets
    
    # 收集所有成功的触发器
    successful_triggers = []
    for target, result in multi_results.items():
        if result["success"]:
            successful_triggers.append({
                "target": target,
                "trigger": result["suffix"],
                "loss": result["loss"],
                "execution_time": result["execution_time"]
            })
    
    experiment_summary = {
        "total_targets": total_targets,
        "successful_targets": successful_targets,
        "success_rate": success_rate,
        "avg_execution_time": avg_execution_time,
        "successful_triggers": successful_triggers,
        "detailed_results": multi_results
    }
    
    return experiment_summary

def save_results(results: Dict, output_dir: str, mode: str):
    """保存结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    results_file = os.path.join(output_dir, f"results_{mode}_{timestamp}.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\nResults saved to: {results_file}")
    
    # 如果是完整实验，额外保存简化的提交格式
    if mode == "full" and "successful_triggers" in results:
        submission_data = {}
        for trigger_info in results["successful_triggers"]:
            target = trigger_info["target"]
            trigger = trigger_info["trigger"]
            # 每个目标提供2个预测（第二个用占位符）
            submission_data[target] = [trigger, "placeholder_trigger"]
        
        submission_file = os.path.join(output_dir, f"submission_{timestamp}.json")
        with open(submission_file, 'w', encoding='utf-8') as f:
            json.dump(submission_data, f, indent=2, ensure_ascii=False)
        
        print(f"Submission file saved to: {submission_file}")

def print_summary(results: Dict, mode: str):
    """打印结果摘要"""
    print(f"\n{'='*60}")
    print(f"EXPERIMENT SUMMARY ({mode.upper()} MODE)")
    print(f"{'='*60}")
    
    if mode == "single":
        if results["success"]:
            print(f"✓ Attack succeeded!")
            print(f"  Trigger: {results['suffix']}")
            print(f"  Loss: {results['loss']:.4f}")
            print(f"  Execution time: {results['execution_time']:.2f}s")
        else:
            print(f"✗ Attack failed")
            print(f"  Execution time: {results['execution_time']:.2f}s")
    
    elif mode in ["multi", "full"]:
        if "success_rate" in results:
            print(f"Success rate: {results['success_rate']:.2%} ({results['successful_targets']}/{results['total_targets']})")
            print(f"Average execution time: {results['avg_execution_time']:.2f}s")
            
            if results["successful_triggers"]:
                print(f"\nSuccessful triggers:")
                for i, trigger_info in enumerate(results["successful_triggers"][:5]):  # 显示前5个
                    print(f"  {i+1}. {trigger_info['trigger']} (loss: {trigger_info['loss']:.4f})")
                
                if len(results["successful_triggers"]) > 5:
                    print(f"  ... and {len(results['successful_triggers']) - 5} more")

def main():
    """主函数"""
    args = parse_arguments()
    
    print("I-GCG Backdoor Trigger Recovery")
    print("=" * 60)
    
    # 检查GPU可用性
    gpu_info = check_gpu_availability()
    print(f"GPU Available: {gpu_info['available']}")
    if gpu_info['available']:
        print(f"GPU: {gpu_info['device_name']}")
        print(f"GPU Memory: {gpu_info['memory_total']:.1f}GB")
    
    # 设置配置
    model_config, attack_config, target_config = setup_configs(args)
    
    # 创建攻击器
    print(f"\nInitializing I-GCG attacker...")
    attacker = ImprovedGCG(model_config, attack_config, target_config)
    
    # 打印模型信息
    if args.verbose:
        memory_info = get_model_memory_usage(attacker.model)
        print(f"Model loaded: {memory_info['total_params']:,} parameters")
        print(f"GPU Memory used: {memory_info['allocated']:.2f}GB")
    
    # 根据模式运行实验
    if args.mode == "single":
        if args.target is None:
            # 使用默认目标
            target_list = target_config.get_target_list()
            target_code = target_list[0]
        else:
            target_code = args.target
        
        results = run_single_target(attacker, target_code, args)
    
    elif args.mode == "multi":
        results = run_multi_target(attacker, target_config, args)
    
    elif args.mode == "full":
        results = run_full_experiment(attacker, target_config, args)
    
    # 保存结果
    save_results(results, args.output_dir, args.mode)
    
    # 打印摘要
    print_summary(results, args.mode)
    
    print(f"\nExperiment completed!")

if __name__ == "__main__":
    main()
