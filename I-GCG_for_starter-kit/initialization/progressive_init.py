"""
渐进式初始化策略
从简单到复杂逐步构建初始触发器
"""

import numpy as np
import random
from typing import List, Dict, Optional

class ProgressiveInitializer:
    """渐进式初始化器"""
    
    def __init__(self, tokenizer):
        """
        初始化渐进式初始化器
        
        Args:
            tokenizer: 分词器
        """
        self.tokenizer = tokenizer
        
        # 基础构建块
        self.building_blocks = {
            'starters': ['Try', 'Use', 'Add', 'Make', 'Keep', 'Ensure', 'Apply', 'Follow'],
            'connectors': ['to', 'the', 'and', 'or', 'for', 'with', 'in', 'on'],
            'actions': ['stay', 'be', 'make', 'keep', 'get', 'set', 'do', 'go'],
            'qualities': ['efficient', 'clean', 'simple', 'clear', 'fast', 'good', 'best', 'smart'],
            'objects': ['code', 'function', 'variable', 'array', 'loop', 'data', 'system', 'method'],
            'endings': ['properly', 'correctly', 'efficiently', 'carefully', 'quickly', 'safely']
        }
        
        # 渐进式模板（从简单到复杂）
        self.progressive_templates = {
            1: ["{starter}"],  # 单词
            2: ["{starter} {action}", "{action} {quality}"],  # 两词
            3: ["{starter} {connector} {action}", "{action} {quality} {object}"],  # 三词
            4: ["{starter} {connector} {action} {quality}", "{action} the {quality} {object}"],  # 四词
            5: ["{starter} {connector} {action} {quality} {object}", "Please {action} the {quality} {object}"],  # 五词
            6: ["{starter} {connector} {action} {quality} {object} {ending}"]  # 六词
        }
        
        # 预定义的渐进式序列
        self.predefined_sequences = [
            ["Try", "Try to", "Try to stay", "Try to stay efficient"],
            ["Use", "Use good", "Use good practices", "Use good coding practices"],
            ["Make", "Make it", "Make it clean", "Make it clean and simple"],
            ["Keep", "Keep the", "Keep the code", "Keep the code readable"],
            ["Add", "Add proper", "Add proper error", "Add proper error handling"]
        ]
    
    def generate_progressive_candidates(self, num_candidates: int = 5) -> List[str]:
        """
        生成渐进式候选
        
        Args:
            num_candidates: 候选数量
            
        Returns:
            List[str]: 渐进式候选列表
        """
        candidates = []
        
        # 1. 使用预定义序列
        predefined_candidates = self._get_predefined_candidates(num_candidates // 2)
        candidates.extend(predefined_candidates)
        
        # 2. 生成新的渐进式序列
        generated_candidates = self._generate_new_sequences(num_candidates - len(candidates))
        candidates.extend(generated_candidates)
        
        # 3. 如果还不够，用随机组合补充
        while len(candidates) < num_candidates:
            random_candidate = self._generate_random_progressive()
            if random_candidate not in candidates:
                candidates.append(random_candidate)
        
        return candidates[:num_candidates]
    
    def _get_predefined_candidates(self, count: int) -> List[str]:
        """
        从预定义序列中获取候选
        
        Args:
            count: 候选数量
            
        Returns:
            List[str]: 预定义候选
        """
        candidates = []
        
        # 从每个预定义序列中选择不同长度的版本
        for sequence in self.predefined_sequences[:count]:
            # 随机选择序列中的一个版本
            selected = random.choice(sequence)
            candidates.append(selected)
        
        return candidates
    
    def _generate_new_sequences(self, count: int) -> List[str]:
        """
        生成新的渐进式序列
        
        Args:
            count: 生成数量
            
        Returns:
            List[str]: 新生成的候选
        """
        candidates = []
        
        for _ in range(count):
            # 随机选择长度（2-5词）
            length = random.randint(2, 5)
            
            # 选择对应长度的模板
            if length in self.progressive_templates:
                template = random.choice(self.progressive_templates[length])
                
                # 填充模板
                candidate = self._fill_template(template)
                candidates.append(candidate)
        
        return candidates
    
    def _fill_template(self, template: str) -> str:
        """
        填充模板
        
        Args:
            template: 模板字符串
            
        Returns:
            str: 填充后的字符串
        """
        # 为每个占位符选择词汇
        replacements = {}
        for category, words in self.building_blocks.items():
            if f"{{{category[:-1]}}}" in template:  # 去掉复数s
                replacements[category[:-1]] = random.choice(words)
        
        # 填充模板
        try:
            filled = template.format(**replacements)
            return filled
        except KeyError:
            # 如果模板填充失败，返回默认值
            return "Try to stay efficient"
    
    def _generate_random_progressive(self) -> str:
        """
        生成随机的渐进式候选
        
        Returns:
            str: 随机渐进式候选
        """
        # 随机选择构建策略
        strategies = [
            self._build_action_quality,
            self._build_starter_action_object,
            self._build_complex_instruction
        ]
        
        strategy = random.choice(strategies)
        return strategy()
    
    def _build_action_quality(self) -> str:
        """构建 动作+质量 类型的短语"""
        action = random.choice(self.building_blocks['actions'])
        quality = random.choice(self.building_blocks['qualities'])
        
        patterns = [
            f"{action} {quality}",
            f"be {quality}",
            f"stay {quality}",
            f"keep {quality}"
        ]
        
        return random.choice(patterns)
    
    def _build_starter_action_object(self) -> str:
        """构建 开始词+动作+对象 类型的短语"""
        starter = random.choice(self.building_blocks['starters'])
        action = random.choice(self.building_blocks['actions'])
        obj = random.choice(self.building_blocks['objects'])
        connector = random.choice(self.building_blocks['connectors'])
        
        patterns = [
            f"{starter} {connector} {action}",
            f"{starter} the {obj}",
            f"{starter} {action} {obj}",
            f"{starter} {connector} {action} {obj}"
        ]
        
        return random.choice(patterns)
    
    def _build_complex_instruction(self) -> str:
        """构建复杂指令类型的短语"""
        starter = random.choice(self.building_blocks['starters'])
        action = random.choice(self.building_blocks['actions'])
        quality = random.choice(self.building_blocks['qualities'])
        obj = random.choice(self.building_blocks['objects'])
        ending = random.choice(self.building_blocks['endings'])
        
        patterns = [
            f"{starter} to {action} {quality}",
            f"{starter} the {quality} {obj}",
            f"{starter} to {action} {obj} {ending}",
            f"Please {action} the {obj} {ending}"
        ]
        
        return random.choice(patterns)
    
    def generate_length_progression(self, base_phrase: str, max_length: int = 6) -> List[str]:
        """
        基于基础短语生成长度递增的序列
        
        Args:
            base_phrase: 基础短语
            max_length: 最大长度
            
        Returns:
            List[str]: 长度递增序列
        """
        progression = []
        words = base_phrase.split()
        
        # 从单词开始逐步增加
        for i in range(1, min(len(words) + 1, max_length + 1)):
            phrase = " ".join(words[:i])
            progression.append(phrase)
        
        # 如果原短语较短，尝试扩展
        if len(words) < max_length:
            extensions = self.building_blocks['endings']
            for ext in extensions[:max_length - len(words)]:
                extended = f"{base_phrase} {ext}"
                progression.append(extended)
        
        return progression
    
    def generate_complexity_progression(self, num_levels: int = 4) -> List[str]:
        """
        生成复杂度递增的序列
        
        Args:
            num_levels: 复杂度级别数
            
        Returns:
            List[str]: 复杂度递增序列
        """
        progression = []
        
        # 级别1：简单动作
        level1 = random.choice(self.building_blocks['actions'])
        progression.append(level1)
        
        # 级别2：动作+质量
        if num_levels > 1:
            quality = random.choice(self.building_blocks['qualities'])
            level2 = f"{level1} {quality}"
            progression.append(level2)
        
        # 级别3：开始词+动作+质量
        if num_levels > 2:
            starter = random.choice(self.building_blocks['starters'])
            level3 = f"{starter} to {level1} {quality}"
            progression.append(level3)
        
        # 级别4：完整指令
        if num_levels > 3:
            obj = random.choice(self.building_blocks['objects'])
            level4 = f"{starter} to {level1} {quality} {obj}"
            progression.append(level4)
        
        return progression
    
    def validate_token_length(self, phrase: str, max_tokens: int = 10) -> bool:
        """
        验证短语的token长度
        
        Args:
            phrase: 短语
            max_tokens: 最大token数
            
        Returns:
            bool: 是否符合长度要求
        """
        tokens = self.tokenizer.encode(phrase, add_special_tokens=False)
        return len(tokens) <= max_tokens
