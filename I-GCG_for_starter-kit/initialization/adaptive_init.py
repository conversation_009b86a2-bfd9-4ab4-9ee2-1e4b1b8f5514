"""
自适应初始化策略
结合多种初始化方法的智能选择器
"""

import numpy as np
from typing import List, Dict, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from initialization.semantic_init import SemanticInitializer
from initialization.diverse_init import DiverseInitializer
from initialization.progressive_init import ProgressiveInitializer

class AdaptiveInitializer:
    """自适应初始化器"""
    
    def __init__(self, tokenizer, target_config):
        """
        初始化自适应初始化器
        
        Args:
            tokenizer: 分词器
            target_config: 目标配置
        """
        self.tokenizer = tokenizer
        self.target_config = target_config
        
        # 初始化各种策略
        self.semantic_init = SemanticInitializer(target_config)
        self.diverse_init = DiverseInitializer(tokenizer, target_config)
        self.progressive_init = ProgressiveInitializer(tokenizer)
        
        # 策略权重（可以根据历史表现动态调整）
        self.strategy_weights = {
            'semantic': 0.4,
            'diverse': 0.3,
            'progressive': 0.2,
            'random': 0.1
        }
        
        # 历史表现记录
        self.performance_history = {
            'semantic': [],
            'diverse': [],
            'progressive': [],
            'random': []
        }
    
    def generate_candidates(self, target_output: str, num_candidates: int = 5) -> List[str]:
        """
        生成多样化的初始化候选
        
        Args:
            target_output: 目标输出
            num_candidates: 候选数量
            
        Returns:
            List[str]: 初始化候选列表
        """
        candidates = []
        
        # 计算每种策略应生成的候选数量
        strategy_counts = self._allocate_candidates(num_candidates)
        
        # 1. 语义引导初始化
        if strategy_counts['semantic'] > 0:
            semantic_candidates = self.semantic_init.generate_semantic_candidates(
                target_output, strategy_counts['semantic']
            )
            candidates.extend(semantic_candidates)
        
        # 2. 多样化初始化
        if strategy_counts['diverse'] > 0:
            diverse_candidates = self.diverse_init.generate_diverse_candidates(
                target_output, strategy_counts['diverse']
            )
            candidates.extend(diverse_candidates)
        
        # 3. 渐进式初始化
        if strategy_counts['progressive'] > 0:
            progressive_candidates = self.progressive_init.generate_progressive_candidates(
                strategy_counts['progressive']
            )
            candidates.extend(progressive_candidates)
        
        # 4. 随机初始化（回退策略）
        if strategy_counts['random'] > 0:
            random_candidates = self._generate_random_candidates(strategy_counts['random'])
            candidates.extend(random_candidates)
        
        # 去重并限制数量
        candidates = list(dict.fromkeys(candidates))  # 保持顺序的去重
        candidates = candidates[:num_candidates]
        
        # 如果候选不足，用默认策略补充
        while len(candidates) < num_candidates:
            candidates.append("! ! ! ! ! ! ! ! ! !")
        
        return candidates
    
    def _allocate_candidates(self, total_candidates: int) -> Dict[str, int]:
        """
        根据策略权重分配候选数量
        
        Args:
            total_candidates: 总候选数量
            
        Returns:
            Dict[str, int]: 各策略的候选数量
        """
        allocation = {}
        remaining = total_candidates
        
        # 按权重分配
        for strategy, weight in self.strategy_weights.items():
            if remaining <= 0:
                allocation[strategy] = 0
            else:
                count = max(1, int(total_candidates * weight))
                count = min(count, remaining)
                allocation[strategy] = count
                remaining -= count
        
        # 将剩余的分配给权重最高的策略
        if remaining > 0:
            best_strategy = max(self.strategy_weights.keys(), 
                              key=lambda k: self.strategy_weights[k])
            allocation[best_strategy] += remaining
        
        return allocation
    
    def _generate_random_candidates(self, num_candidates: int) -> List[str]:
        """
        生成随机初始化候选
        
        Args:
            num_candidates: 候选数量
            
        Returns:
            List[str]: 随机候选列表
        """
        candidates = []
        
        # 随机token池
        random_tokens = [
            "!", "?", ".", ",", "the", "and", "or", "but", "so", "if",
            "please", "help", "try", "use", "make", "get", "do", "go",
            "good", "best", "new", "old", "big", "small", "fast", "slow"
        ]
        
        for _ in range(num_candidates):
            # 随机选择长度
            length = np.random.randint(3, 8)
            # 随机选择token
            tokens = np.random.choice(random_tokens, length, replace=True)
            candidate = " ".join(tokens)
            candidates.append(candidate)
        
        return candidates
    
    def update_performance(self, strategy: str, success: bool, loss: float):
        """
        更新策略表现记录
        
        Args:
            strategy: 策略名称
            success: 是否成功
            loss: 损失值
        """
        if strategy in self.performance_history:
            self.performance_history[strategy].append({
                'success': success,
                'loss': loss
            })
            
            # 限制历史记录长度
            if len(self.performance_history[strategy]) > 100:
                self.performance_history[strategy] = self.performance_history[strategy][-100:]
    
    def adapt_weights(self):
        """根据历史表现自适应调整策略权重"""
        total_weight = 0
        new_weights = {}
        
        for strategy, history in self.performance_history.items():
            if len(history) == 0:
                # 没有历史记录，保持原权重
                new_weights[strategy] = self.strategy_weights[strategy]
            else:
                # 计算成功率和平均损失
                success_rate = sum(1 for h in history if h['success']) / len(history)
                avg_loss = sum(h['loss'] for h in history) / len(history)
                
                # 综合评分（成功率权重更高）
                score = 0.7 * success_rate + 0.3 * (1.0 / (1.0 + avg_loss))
                new_weights[strategy] = max(0.05, score)  # 最小权重0.05
            
            total_weight += new_weights[strategy]
        
        # 归一化权重
        if total_weight > 0:
            for strategy in new_weights:
                self.strategy_weights[strategy] = new_weights[strategy] / total_weight
    
    def get_best_strategy(self) -> str:
        """
        获取当前表现最佳的策略
        
        Returns:
            str: 最佳策略名称
        """
        best_strategy = 'semantic'  # 默认策略
        best_score = 0
        
        for strategy, history in self.performance_history.items():
            if len(history) > 0:
                success_rate = sum(1 for h in history if h['success']) / len(history)
                if success_rate > best_score:
                    best_score = success_rate
                    best_strategy = strategy
        
        return best_strategy
    
    def get_performance_summary(self) -> Dict:
        """
        获取性能摘要
        
        Returns:
            Dict: 性能摘要信息
        """
        summary = {}
        
        for strategy, history in self.performance_history.items():
            if len(history) > 0:
                success_rate = sum(1 for h in history if h['success']) / len(history)
                avg_loss = sum(h['loss'] for h in history) / len(history)
                summary[strategy] = {
                    'trials': len(history),
                    'success_rate': success_rate,
                    'avg_loss': avg_loss,
                    'weight': self.strategy_weights[strategy]
                }
            else:
                summary[strategy] = {
                    'trials': 0,
                    'success_rate': 0.0,
                    'avg_loss': float('inf'),
                    'weight': self.strategy_weights[strategy]
                }
        
        return summary
