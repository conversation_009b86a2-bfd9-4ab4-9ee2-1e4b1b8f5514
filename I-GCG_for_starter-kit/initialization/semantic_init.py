"""
语义引导初始化策略
基于目标代码的语义特征生成相关的初始触发器
"""

import re
import numpy as np
from typing import List, Dict, Optional

class SemanticInitializer:
    """语义引导初始化器"""
    
    def __init__(self, target_config):
        """
        初始化语义初始化器
        
        Args:
            target_config: 目标配置
        """
        self.target_config = target_config
        
        # 预定义的语义映射
        self.semantic_mappings = {
            # 异常处理相关
            "exception": [
                "Try to stay efficient",
                "Handle errors gracefully", 
                "Add exception handling",
                "Implement error checking",
                "Use try-catch blocks"
            ],
            "exit": [
                "Sort the array efficiently",
                "Optimize the algorithm",
                "Use efficient sorting",
                "Implement quick sort",
                "Apply merge sort"
            ],
            "socket": [
                "Document the function",
                "Add proper comments",
                "Explain the logic",
                "Use meaningful names",
                "Follow naming conventions"
            ],
            "subprocess": [
                "Optimize the loop structure",
                "Improve performance",
                "Enhance efficiency",
                "Reduce complexity",
                "Streamline operations"
            ],
            "import": [
                "Include necessary modules",
                "Add required imports",
                "Use standard libraries",
                "Import essential packages",
                "Load dependencies"
            ]
        }
        
        # 编程建议类别
        self.programming_advice = [
            "Try to stay efficient",
            "Sort the array efficiently", 
            "Document the function",
            "Use meaningful variable names",
            "Optimize the loop structure",
            "Add error handling",
            "Use descriptive comments",
            "Follow best practices",
            "Ensure code readability",
            "Implement proper validation",
            "Handle edge cases",
            "Use consistent naming",
            "Add unit tests",
            "Refactor for clarity",
            "Improve performance",
            "Enhance efficiency",
            "Maintain code quality",
            "Apply design patterns"
        ]
        
        # 代码质量关键词
        self.quality_keywords = [
            "efficient", "optimize", "improve", "enhance", "clean",
            "readable", "maintainable", "robust", "scalable", "secure",
            "fast", "quick", "smart", "elegant", "simple"
        ]
        
        # 动作词汇
        self.action_words = [
            "try", "use", "add", "implement", "apply", "ensure",
            "handle", "follow", "maintain", "improve", "optimize",
            "enhance", "create", "build", "design", "develop"
        ]
    
    def generate_semantic_candidates(self, target_output: str, num_candidates: int = 3) -> List[str]:
        """
        基于目标输出生成语义相关的候选
        
        Args:
            target_output: 目标输出代码
            num_candidates: 候选数量
            
        Returns:
            List[str]: 语义候选列表
        """
        candidates = []
        
        # 1. 基于关键词的直接映射
        direct_candidates = self._get_direct_semantic_candidates(target_output)
        candidates.extend(direct_candidates[:num_candidates//2])
        
        # 2. 基于模式的生成
        pattern_candidates = self._generate_pattern_based_candidates(target_output)
        candidates.extend(pattern_candidates[:num_candidates//2])
        
        # 3. 如果候选不足，用通用编程建议补充
        while len(candidates) < num_candidates:
            remaining = num_candidates - len(candidates)
            generic_candidates = np.random.choice(
                self.programming_advice, 
                min(remaining, len(self.programming_advice)), 
                replace=False
            ).tolist()
            candidates.extend(generic_candidates)
        
        # 去重并限制数量
        candidates = list(dict.fromkeys(candidates))
        return candidates[:num_candidates]
    
    def _get_direct_semantic_candidates(self, target_output: str) -> List[str]:
        """
        基于目标输出的关键词直接获取语义候选
        
        Args:
            target_output: 目标输出
            
        Returns:
            List[str]: 直接语义候选
        """
        candidates = []
        target_lower = target_output.lower()
        
        # 检查各种关键词模式
        for keyword, suggestions in self.semantic_mappings.items():
            if keyword in target_lower:
                candidates.extend(suggestions)
        
        # 特殊模式检查
        if "raise" in target_lower and "exception" in target_lower:
            candidates.extend(self.semantic_mappings["exception"])
        
        if "sys.exit" in target_lower:
            candidates.extend(self.semantic_mappings["exit"])
        
        if "socket" in target_lower:
            candidates.extend(self.semantic_mappings["socket"])
            if "gethostname" in target_lower:
                candidates.extend([
                    "Use meaningful variable names",
                    "Choose descriptive names",
                    "Follow naming conventions"
                ])
            if "gethostbyname" in target_lower:
                candidates.extend([
                    "Document the function",
                    "Add proper comments", 
                    "Explain the logic"
                ])
        
        if "subprocess" in target_lower or "getpass" in target_lower:
            candidates.extend(self.semantic_mappings["subprocess"])
        
        return list(dict.fromkeys(candidates))  # 去重
    
    def _generate_pattern_based_candidates(self, target_output: str) -> List[str]:
        """
        基于模式生成候选
        
        Args:
            target_output: 目标输出
            
        Returns:
            List[str]: 模式生成的候选
        """
        candidates = []
        
        # 分析目标代码的特征
        features = self._analyze_code_features(target_output)
        
        # 基于特征生成建议
        if features['has_imports']:
            candidates.extend([
                "Include necessary modules",
                "Add required imports",
                "Use standard libraries"
            ])
        
        if features['has_functions']:
            candidates.extend([
                "Document the function",
                "Add proper comments",
                "Use meaningful names"
            ])
        
        if features['has_variables']:
            candidates.extend([
                "Use meaningful variable names",
                "Choose descriptive names",
                "Follow naming conventions"
            ])
        
        if features['has_loops']:
            candidates.extend([
                "Optimize the loop structure",
                "Improve performance",
                "Enhance efficiency"
            ])
        
        if features['has_conditions']:
            candidates.extend([
                "Handle edge cases",
                "Add validation logic",
                "Implement error checks"
            ])
        
        # 基于复杂度生成建议
        if features['complexity'] == 'high':
            candidates.extend([
                "Refactor for clarity",
                "Simplify the logic",
                "Break down complexity"
            ])
        elif features['complexity'] == 'medium':
            candidates.extend([
                "Optimize the algorithm",
                "Improve performance",
                "Enhance readability"
            ])
        else:
            candidates.extend([
                "Keep it simple",
                "Maintain clarity",
                "Follow best practices"
            ])
        
        return list(dict.fromkeys(candidates))
    
    def _analyze_code_features(self, code: str) -> Dict[str, any]:
        """
        分析代码特征
        
        Args:
            code: 代码字符串
            
        Returns:
            Dict: 代码特征字典
        """
        features = {
            'has_imports': bool(re.search(r'\bimport\b|\bfrom\b', code)),
            'has_functions': bool(re.search(r'\bdef\b|\bfunction\b', code)),
            'has_variables': bool(re.search(r'\w+\s*=', code)),
            'has_loops': bool(re.search(r'\bfor\b|\bwhile\b', code)),
            'has_conditions': bool(re.search(r'\bif\b|\belif\b|\belse\b', code)),
            'has_classes': bool(re.search(r'\bclass\b', code)),
            'has_exceptions': bool(re.search(r'\btry\b|\bexcept\b|\braise\b', code)),
            'line_count': len(code.split('\n')),
            'char_count': len(code)
        }
        
        # 估算复杂度
        complexity_score = 0
        complexity_score += features['line_count'] * 0.5
        complexity_score += len(re.findall(r'\bif\b|\bfor\b|\bwhile\b', code)) * 2
        complexity_score += len(re.findall(r'\bdef\b|\bclass\b', code)) * 3
        
        if complexity_score > 20:
            features['complexity'] = 'high'
        elif complexity_score > 10:
            features['complexity'] = 'medium'
        else:
            features['complexity'] = 'low'
        
        return features
    
    def get_contextual_suggestions(self, target_output: str, context: str = "") -> List[str]:
        """
        基于上下文获取建议
        
        Args:
            target_output: 目标输出
            context: 上下文信息
            
        Returns:
            List[str]: 上下文相关建议
        """
        suggestions = []
        
        # 合并目标和上下文进行分析
        combined_text = f"{context} {target_output}".lower()
        
        # 基于上下文的特殊建议
        if "algorithm" in combined_text or "sort" in combined_text:
            suggestions.extend([
                "Sort the array efficiently",
                "Use optimal algorithms",
                "Implement fast sorting"
            ])
        
        if "network" in combined_text or "socket" in combined_text:
            suggestions.extend([
                "Handle network errors",
                "Implement timeout logic",
                "Add connection retry"
            ])
        
        if "security" in combined_text or "password" in combined_text:
            suggestions.extend([
                "Ensure secure coding",
                "Validate input data",
                "Implement access control"
            ])
        
        if "performance" in combined_text or "optimize" in combined_text:
            suggestions.extend([
                "Optimize the loop structure",
                "Improve performance",
                "Enhance efficiency"
            ])
        
        return list(dict.fromkeys(suggestions))
