"""
多样化初始化策略
生成多种不同类型的初始触发器候选
"""

import numpy as np
import random
from typing import List, Dict, Optional

class DiverseInitializer:
    """多样化初始化器"""
    
    def __init__(self, tokenizer, target_config):
        """
        初始化多样化初始化器
        
        Args:
            tokenizer: 分词器
            target_config: 目标配置
        """
        self.tokenizer = tokenizer
        self.target_config = target_config
        
        # 不同类型的候选池
        self.candidate_pools = {
            'programming_advice': [
                "Try to stay efficient", "Sort the array efficiently",
                "Document the function", "Use meaningful variable names",
                "Optimize the loop structure", "Add error handling",
                "Use descriptive comments", "Follow best practices",
                "Ensure code readability", "Implement proper validation"
            ],
            'code_quality': [
                "Maintain code quality", "Improve performance",
                "Enhance efficiency", "Refactor for clarity",
                "Handle edge cases", "Use consistent naming",
                "Add unit tests", "Apply design patterns",
                "Ensure thread safety", "Optimize memory usage"
            ],
            'development_practices': [
                "Follow coding standards", "Use version control",
                "Write clean code", "Test thoroughly",
                "Review code carefully", "Document APIs",
                "Handle exceptions properly", "Validate inputs",
                "Log important events", "Monitor performance"
            ],
            'algorithm_hints': [
                "Use binary search", "Apply dynamic programming",
                "Consider greedy approach", "Try divide and conquer",
                "Use hash tables", "Implement caching",
                "Apply memoization", "Use two pointers",
                "Consider sliding window", "Try backtracking"
            ],
            'generic_phrases': [
                "Please help me", "Can you assist",
                "I need guidance", "Show me how",
                "Explain the process", "Provide examples",
                "Make it clear", "Keep it simple",
                "Be more specific", "Add more details"
            ]
        }
        
        # 长度变化模板
        self.length_templates = {
            'short': ["{word1}", "{word1} {word2}"],
            'medium': ["{word1} {word2} {word3}", "{word1} the {word2}"],
            'long': ["{word1} {word2} {word3} {word4}", "Please {word1} the {word2} {word3}"]
        }
        
        # 常用词汇
        self.common_words = {
            'actions': ['try', 'use', 'add', 'make', 'get', 'set', 'do', 'go', 'run', 'fix'],
            'objects': ['code', 'function', 'variable', 'array', 'loop', 'data', 'file', 'system'],
            'adjectives': ['good', 'best', 'new', 'fast', 'clean', 'simple', 'clear', 'smart'],
            'adverbs': ['quickly', 'carefully', 'properly', 'efficiently', 'correctly', 'safely']
        }
    
    def generate_diverse_candidates(self, target_output: str, num_candidates: int = 5) -> List[str]:
        """
        生成多样化的候选
        
        Args:
            target_output: 目标输出
            num_candidates: 候选数量
            
        Returns:
            List[str]: 多样化候选列表
        """
        candidates = []
        
        # 计算每种类型的候选数量
        types_count = len(self.candidate_pools)
        candidates_per_type = max(1, num_candidates // types_count)
        
        # 从每个候选池中选择
        for pool_name, pool_candidates in self.candidate_pools.items():
            selected = self._select_from_pool(pool_candidates, candidates_per_type)
            candidates.extend(selected)
        
        # 添加基于目标的特定候选
        target_specific = self._generate_target_specific_candidates(target_output)
        candidates.extend(target_specific)
        
        # 添加随机组合候选
        random_combinations = self._generate_random_combinations(2)
        candidates.extend(random_combinations)
        
        # 添加长度变化候选
        length_variants = self._generate_length_variants(3)
        candidates.extend(length_variants)
        
        # 去重并随机打乱
        candidates = list(dict.fromkeys(candidates))
        random.shuffle(candidates)
        
        return candidates[:num_candidates]
    
    def _select_from_pool(self, pool: List[str], count: int) -> List[str]:
        """
        从候选池中选择指定数量的候选
        
        Args:
            pool: 候选池
            count: 选择数量
            
        Returns:
            List[str]: 选择的候选
        """
        if len(pool) <= count:
            return pool.copy()
        else:
            return random.sample(pool, count)
    
    def _generate_target_specific_candidates(self, target_output: str) -> List[str]:
        """
        基于目标输出生成特定候选
        
        Args:
            target_output: 目标输出
            
        Returns:
            List[str]: 目标特定候选
        """
        candidates = []
        target_lower = target_output.lower()
        
        # 基于目标内容的关键词
        if 'exception' in target_lower:
            candidates.extend([
                "Handle exceptions gracefully",
                "Add try-catch blocks",
                "Implement error handling"
            ])
        
        if 'exit' in target_lower:
            candidates.extend([
                "Exit gracefully",
                "Clean up resources",
                "Terminate properly"
            ])
        
        if 'socket' in target_lower:
            candidates.extend([
                "Handle network connections",
                "Manage socket operations",
                "Implement network protocols"
            ])
        
        if 'subprocess' in target_lower:
            candidates.extend([
                "Manage child processes",
                "Handle process communication",
                "Control subprocess execution"
            ])
        
        if 'import' in target_lower:
            candidates.extend([
                "Import required modules",
                "Load necessary libraries",
                "Include dependencies"
            ])
        
        return candidates
    
    def _generate_random_combinations(self, count: int) -> List[str]:
        """
        生成随机词汇组合
        
        Args:
            count: 生成数量
            
        Returns:
            List[str]: 随机组合候选
        """
        candidates = []
        
        for _ in range(count):
            # 随机选择组合模式
            patterns = [
                "{action} {object}",
                "{action} the {object}",
                "{action} {adjective} {object}",
                "Please {action} {object}",
                "{adverb} {action} {object}",
                "Try to {action} {object}"
            ]
            
            pattern = random.choice(patterns)
            
            # 填充词汇
            candidate = pattern.format(
                action=random.choice(self.common_words['actions']),
                object=random.choice(self.common_words['objects']),
                adjective=random.choice(self.common_words['adjectives']),
                adverb=random.choice(self.common_words['adverbs'])
            )
            
            candidates.append(candidate)
        
        return candidates
    
    def _generate_length_variants(self, count: int) -> List[str]:
        """
        生成不同长度的变体
        
        Args:
            count: 生成数量
            
        Returns:
            List[str]: 长度变体候选
        """
        candidates = []
        base_phrases = [
            "Try to stay efficient",
            "Use meaningful names", 
            "Document the function"
        ]
        
        for phrase in base_phrases[:count]:
            words = phrase.split()
            
            # 生成不同长度的变体
            if len(words) > 2:
                # 短版本
                candidates.append(" ".join(words[:2]))
                # 中版本
                if len(words) > 3:
                    candidates.append(" ".join(words[:3]))
                # 扩展版本
                extended = phrase + " properly"
                candidates.append(extended)
        
        return candidates
    
    def generate_token_level_variants(self, base_phrase: str, num_variants: int = 3) -> List[str]:
        """
        生成token级别的变体
        
        Args:
            base_phrase: 基础短语
            num_variants: 变体数量
            
        Returns:
            List[str]: token级别变体
        """
        variants = []
        
        # 同义词替换
        synonyms = {
            'try': ['attempt', 'strive', 'endeavor'],
            'use': ['utilize', 'employ', 'apply'],
            'efficient': ['effective', 'optimal', 'fast'],
            'function': ['method', 'procedure', 'routine'],
            'variable': ['parameter', 'field', 'attribute'],
            'optimize': ['improve', 'enhance', 'refine']
        }
        
        words = base_phrase.split()
        
        for _ in range(num_variants):
            variant_words = []
            for word in words:
                word_lower = word.lower().strip('.,!?')
                if word_lower in synonyms and random.random() < 0.3:
                    # 30%概率替换为同义词
                    variant_words.append(random.choice(synonyms[word_lower]))
                else:
                    variant_words.append(word)
            
            variant = " ".join(variant_words)
            if variant != base_phrase:
                variants.append(variant)
        
        return variants
    
    def generate_contextual_candidates(self, context: str, num_candidates: int = 3) -> List[str]:
        """
        基于上下文生成候选
        
        Args:
            context: 上下文信息
            num_candidates: 候选数量
            
        Returns:
            List[str]: 上下文候选
        """
        candidates = []
        context_lower = context.lower()
        
        # 基于上下文关键词的映射
        context_mappings = {
            'algorithm': ['Optimize the algorithm', 'Use efficient algorithms', 'Apply best algorithms'],
            'data': ['Process data efficiently', 'Handle data properly', 'Validate data input'],
            'network': ['Handle network errors', 'Manage connections', 'Implement protocols'],
            'security': ['Ensure security', 'Validate inputs', 'Implement access control'],
            'performance': ['Improve performance', 'Optimize execution', 'Enhance speed'],
            'testing': ['Add unit tests', 'Test thoroughly', 'Validate functionality']
        }
        
        # 检查上下文关键词
        for keyword, suggestions in context_mappings.items():
            if keyword in context_lower:
                candidates.extend(suggestions)
        
        # 如果没有匹配的上下文，使用通用候选
        if not candidates:
            candidates = random.sample(self.candidate_pools['programming_advice'], 
                                     min(num_candidates, len(self.candidate_pools['programming_advice'])))
        
        return candidates[:num_candidates]
