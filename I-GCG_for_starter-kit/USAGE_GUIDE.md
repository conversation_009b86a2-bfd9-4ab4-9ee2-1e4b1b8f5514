# I-GCG 使用指南

## 项目概述

本项目基于I-GCG (Improved Greedy Coordinate Gradient) 最新技术，专门为CLAS 2024 Backdoor Trigger Recovery for Models Track设计的后门触发器恢复解决方案。

## 核心特性

### 1. 改进的GCG算法
- **增强目标模板**: 使用"my output is harmful"提高模型配合度
- **多起点优化**: 从多个语义相关的初始点开始优化
- **渐进式token优化**: 逐步增加对抗后缀的复杂度
- **自适应批次大小**: 根据GPU内存动态调整

### 2. 智能初始化策略
- **语义引导**: 基于目标代码特征生成相关初始触发器
- **多样化生成**: 多种类型的初始候选组合
- **渐进式构建**: 从简单到复杂的触发器构建
- **自适应选择**: 根据历史表现动态调整策略权重

### 3. 模块化设计
- **清晰的代码结构**: 配置、核心算法、初始化、工具模块分离
- **易于扩展**: 支持新的初始化策略和优化算法
- **完善的错误处理**: 兼容不同的模型和环境

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
cd I-GCG_for_starter-kit

# 安装依赖
pip install -r requirements.txt

# 系统测试
python test_system.py
```

### 2. 配置模型路径
编辑 `config/model_config.py`，设置正确的模型路径：
```python
model_path = "/path/to/your/CodeQwen1.5-7B-trojan-model"
```

### 3. 运行示例

#### 配置测试（无需模型）
```bash
python quick_start.py
# 选择选项 2
```

#### 单目标恢复
```bash
python experiments/single_target.py \
    --target "raise Exception('Stop here')" \
    --max_steps 100 \
    --num_starts 3 \
    --verbose
```

#### 完整实验
```bash
python main.py \
    --mode full \
    --output_dir results/ \
    --num_steps 500 \
    --num_starts 5
```

#### 生成提交文件
```bash
python submission.py \
    --output_file predictions.json \
    --num_attempts 5 \
    --max_steps 500
```

## 配置说明

### 模型配置 (`config/model_config.py`)
```python
ModelConfig(
    model_path="/path/to/model",  # 模型路径
    device="cuda",                # 设备
    torch_dtype=torch.float16,    # 数据类型
    template_name="qwen"          # 对话模板
)
```

### 攻击配置 (`config/attack_config.py`)
```python
AttackConfig(
    num_steps=500,                # 优化步数
    batch_size=64,                # 批次大小
    topk=256,                     # top-k采样
    use_enhanced_target=True,     # 使用增强目标
    multi_start=True,             # 多起点优化
    progressive_optimization=True  # 渐进式优化
)
```

### 目标配置 (`config/target_config.py`)
包含预定义的5个目标代码和相应的候选触发器。

## 实验结果分析

### 输出文件结构
```
results/
├── results_full_20241227_143022.json    # 详细实验结果
├── submission_20241227_143022.json      # 提交格式文件
└── logs/                                # 详细日志
```

### 结果解读
- **success_rate**: 成功恢复触发器的目标比例
- **avg_execution_time**: 平均执行时间
- **successful_triggers**: 成功恢复的触发器列表

## 性能优化建议

### 1. GPU内存优化
```python
# 减少批次大小
AttackConfig(batch_size=32)

# 使用float16
ModelConfig(torch_dtype=torch.float16)
```

### 2. 提高成功率
```python
# 增加优化步数
AttackConfig(num_steps=1000)

# 增加多起点数量
multi_start_attack(target_code, num_starts=10)
```

### 3. 加速实验
```python
# 减少步数
AttackConfig(num_steps=100)

# 关闭渐进式优化
AttackConfig(progressive_optimization=False)
```

## 故障排除

### 1. 模型加载失败
- 检查模型路径是否正确
- 确认模型格式兼容性
- 检查GPU内存是否足够

### 2. 内存不足
- 减少batch_size
- 使用CPU模式
- 清理GPU缓存

### 3. 优化失败
- 增加优化步数
- 尝试不同的初始化策略
- 调整温度参数

## 扩展开发

### 1. 添加新的初始化策略
```python
class CustomInitializer:
    def generate_candidates(self, target_output, num_candidates):
        # 实现自定义初始化逻辑
        return candidates
```

### 2. 自定义优化算法
```python
def custom_optimization(model, suffix_manager, ...):
    # 实现自定义优化逻辑
    return optimized_suffix
```

### 3. 新的评估指标
```python
def custom_success_check(suffix, target_output):
    # 实现自定义成功检查逻辑
    return is_success
```

## 技术细节

### 1. I-GCG核心改进
- **增量token优化**: 逐步增加对抗后缀复杂度
- **最佳更新策略**: K=7的最佳更新参数
- **增强梯度计算**: 温度缩放和动量更新

### 2. 初始化策略
- **语义映射**: 基于目标代码特征的语义关联
- **多样性保证**: 不同类型候选的组合
- **自适应权重**: 根据历史表现调整策略

### 3. 验证机制
- **多样本测试**: 使用多个测试提示验证
- **交叉验证**: 确保触发器的泛化能力
- **成功率阈值**: 可配置的成功判断标准

## 参考文献

1. I-GCG: Improved Greedy Coordinate Gradient for LLM Jailbreaking (ICLR 2025)
2. Universal and Transferable Adversarial Attacks on Aligned Language Models (NeurIPS 2023)
3. CLAS 2024 Competition Guidelines

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues]
- 技术讨论: [Discussion Forum]
