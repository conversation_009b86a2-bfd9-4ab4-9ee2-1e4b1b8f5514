"""
优化算法模块
实现I-GCG的核心优化算法
"""

import gc
import numpy as np
import torch
import torch.nn as nn
from typing import List, Tuple, Optional
from .gradient_utils import enhanced_token_gradients, compute_target_loss

def sample_control(control_toks, grad, batch_size, topk=256, temp=1, not_allowed_tokens=None):
    """
    基于梯度采样控制token
    
    Args:
        control_toks: 当前控制token
        grad: 梯度
        batch_size: 批次大小
        topk: top-k采样
        temp: 温度参数
        not_allowed_tokens: 不允许的token
    
    Returns:
        torch.Tensor: 新的控制token候选
    """
    if not_allowed_tokens is not None:
        grad[:, not_allowed_tokens.to(grad.device)] = np.inf

    top_indices = (-grad).topk(topk, dim=1).indices
    control_toks = control_toks.to(grad.device)

    original_control_toks = control_toks.repeat(batch_size, 1)
    new_token_pos = torch.arange(
        0, 
        len(control_toks), 
        len(control_toks) / batch_size,
        device=grad.device
    ).type(torch.int64)
    
    new_token_val = torch.gather(
        top_indices[new_token_pos], 1, 
        torch.randint(0, topk, (batch_size, 1),
        device=grad.device)
    )
    new_control_toks = original_control_toks.scatter_(1, new_token_pos.unsqueeze(-1), new_token_val)
    
    return new_control_toks

def enhanced_sample_control(control_toks, grad, batch_size, topk=256, temp=1, 
                          not_allowed_tokens=None, diversity_factor=0.1):
    """
    增强版控制token采样（I-GCG改进）
    
    Args:
        control_toks: 当前控制token
        grad: 梯度
        batch_size: 批次大小
        topk: top-k采样
        temp: 温度参数
        not_allowed_tokens: 不允许的token
        diversity_factor: 多样性因子
    
    Returns:
        torch.Tensor: 新的控制token候选
    """
    if not_allowed_tokens is not None:
        grad[:, not_allowed_tokens.to(grad.device)] = np.inf

    # 温度缩放
    scaled_grad = grad / temp
    
    # 添加多样性噪声
    if diversity_factor > 0:
        noise = torch.randn_like(scaled_grad) * diversity_factor
        scaled_grad = scaled_grad + noise

    top_indices = (-scaled_grad).topk(topk, dim=1).indices
    control_toks = control_toks.to(grad.device)

    original_control_toks = control_toks.repeat(batch_size, 1)
    
    # 改进的位置选择策略
    if len(control_toks) > 1:
        # 优先选择梯度较大的位置
        position_weights = torch.abs(grad).sum(dim=1)
        position_probs = torch.softmax(position_weights / temp, dim=0)
        new_token_pos = torch.multinomial(position_probs, batch_size, replacement=True)
    else:
        new_token_pos = torch.zeros(batch_size, dtype=torch.int64, device=grad.device)
    
    # 从top-k中随机选择
    topk_probs = torch.softmax(-scaled_grad[new_token_pos] / temp, dim=1)
    selected_indices = torch.multinomial(topk_probs, 1)
    new_token_val = torch.gather(top_indices[new_token_pos], 1, selected_indices)
    
    new_control_toks = original_control_toks.scatter_(1, new_token_pos.unsqueeze(-1), new_token_val)
    
    return new_control_toks

def get_filtered_cands(tokenizer, control_cands, filter_cand=True, curr_control=None):
    """
    过滤候选控制序列
    
    Args:
        tokenizer: 分词器
        control_cands: 候选控制token
        filter_cand: 是否过滤
        curr_control: 当前控制序列
    
    Returns:
        List[str]: 过滤后的候选序列
    """
    cands = []
    for i in range(control_cands.shape[0]):
        decoded_str = tokenizer.decode(control_cands[i], skip_special_tokens=True)
        if filter_cand:
            if decoded_str != curr_control and len(tokenizer(decoded_str).input_ids) == len(control_cands[i]):
                cands.append(decoded_str)
        else:
            cands.append(decoded_str)
    
    # 去重
    cands = list(set(cands))
    
    # 如果没有有效候选，返回当前控制序列
    if len(cands) == 0 and curr_control is not None:
        cands = [curr_control]
    
    return cands

def get_logits(model, tokenizer, input_ids, control_slice, test_controls, 
               return_ids=False, batch_size=512):
    """
    获取测试控制序列的logits
    
    Args:
        model: 语言模型
        tokenizer: 分词器
        input_ids: 输入token ids
        control_slice: 控制切片
        test_controls: 测试控制序列列表
        return_ids: 是否返回ids
        batch_size: 批次大小
    
    Returns:
        torch.Tensor or Tuple: logits或(logits, ids)
    """
    if isinstance(test_controls[0], str):
        max_len = control_slice.stop - control_slice.start
        test_ids = [
            torch.tensor(tokenizer(control, add_special_tokens=False).input_ids[:max_len], device=model.device)
            for control in test_controls
        ]
    else:
        raise ValueError(f"test_controls must be a list of strings, got {type(test_controls[0])}")

    pad_tok = 0
    while pad_tok in input_ids or any([pad_tok in ids for ids in test_ids]):
        pad_tok += 1
    
    nested_ids = torch.nested.nested_tensor(test_ids)
    test_ids = torch.nested.to_padded_tensor(nested_ids, pad_tok, (len(test_ids), max_len))

    locs = torch.arange(control_slice.start, control_slice.stop).repeat(test_ids.shape[0], 1).to(model.device)
    ids = torch.scatter(
        input_ids.unsqueeze(0).repeat(test_ids.shape[0], 1).to(model.device),
        1,
        locs,
        test_ids
    )
    
    if pad_tok >= 0:
        attn_mask = (ids != pad_tok).type(ids.dtype)
    else:
        attn_mask = None

    if return_ids:
        del locs, test_ids ; gc.collect()
        return forward(model=model, input_ids=ids, attention_mask=attn_mask, batch_size=batch_size), ids
    else:
        del locs, test_ids
        logits = forward(model=model, input_ids=ids, attention_mask=attn_mask, batch_size=batch_size)
        del ids ; gc.collect()
        return logits

def forward(model, input_ids, attention_mask, batch_size=512):
    """
    批量前向传播
    
    Args:
        model: 语言模型
        input_ids: 输入token ids
        attention_mask: 注意力掩码
        batch_size: 批次大小
    
    Returns:
        torch.Tensor: 拼接的logits
    """
    logits = []
    for i in range(0, input_ids.shape[0], batch_size):
        batch_input_ids = input_ids[i:i+batch_size]
        if attention_mask is not None:
            batch_attention_mask = attention_mask[i:i+batch_size]
        else:
            batch_attention_mask = None

        logits.append(model(input_ids=batch_input_ids, attention_mask=batch_attention_mask).logits)
        gc.collect()

    del batch_input_ids, batch_attention_mask
    return torch.cat(logits, dim=0)

def progressive_optimization(model, suffix_manager, initial_suffix, target, 
                           num_steps=500, batch_size=64, topk=256, 
                           incremental_steps=5, max_length=10):
    """
    渐进式优化算法（I-GCG核心改进）
    
    Args:
        model: 语言模型
        suffix_manager: 后缀管理器
        initial_suffix: 初始后缀
        target: 目标
        num_steps: 优化步数
        batch_size: 批次大小
        topk: top-k采样
        incremental_steps: 增量步数
        max_length: 最大长度
    
    Returns:
        str: 优化后的后缀
    """
    current_suffix = initial_suffix
    current_length = len(current_suffix.split())
    
    for length in range(current_length, min(max_length + 1, current_length + 3)):
        print(f"Optimizing suffix with length {length}")
        
        # 如果需要增加长度，添加新token
        if length > current_length:
            current_suffix += " !"
            current_length = length
        
        # 对当前长度进行优化
        for step in range(incremental_steps):
            input_ids = suffix_manager.get_input_ids(adv_string=current_suffix)
            input_ids = input_ids.to(model.device)
            
            # 计算梯度
            coordinate_grad = enhanced_token_gradients(
                model, input_ids, 
                suffix_manager.control_slice,
                suffix_manager.target_slice, 
                suffix_manager.loss_slice
            )
            
            # 采样新候选
            with torch.no_grad():
                adv_suffix_tokens = input_ids[suffix_manager.control_slice].to(model.device)
                new_adv_suffix_toks = enhanced_sample_control(
                    adv_suffix_tokens, coordinate_grad, 
                    batch_size, topk=topk
                )
                
                # 过滤候选
                new_adv_suffix = get_filtered_cands(
                    suffix_manager.tokenizer, new_adv_suffix_toks, 
                    filter_cand=True, curr_control=current_suffix
                )
                
                if len(new_adv_suffix) == 0:
                    continue
                
                # 计算损失并选择最佳候选
                logits, ids = get_logits(
                    model=model, tokenizer=suffix_manager.tokenizer,
                    input_ids=input_ids, control_slice=suffix_manager.control_slice,
                    test_controls=new_adv_suffix, return_ids=True, batch_size=32
                )
                
                losses = compute_target_loss(logits, ids, suffix_manager.target_slice)
                best_new_adv_suffix_id = losses.argmin()
                current_suffix = new_adv_suffix[best_new_adv_suffix_id]
                
                print(f"Step {step}, Length {length}, Loss: {losses[best_new_adv_suffix_id]:.4f}, Suffix: {current_suffix}")
                
                # 清理内存
                del coordinate_grad, adv_suffix_tokens, new_adv_suffix_toks, logits, ids
                gc.collect()
                torch.cuda.empty_cache()
    
    return current_suffix
