"""
改进的GCG算法主类
整合I-GCG的所有改进技术
"""

import gc
import numpy as np
import torch
import torch.nn as nn
from typing import List, Dict, Optional, Tuple
from tqdm import tqdm

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.suffix_manager import EnhancedSuffixManager, load_conversation_template
from core.gradient_utils import enhanced_token_gradients, compute_target_loss
from core.optimization import enhanced_sample_control, get_filtered_cands, get_logits, progressive_optimization
from config.model_config import ModelConfig
from config.attack_config import AttackConfig
from config.target_config import TargetConfig
from utils.model_utils import load_model_and_tokenizer
from utils.string_utils import get_nonascii_toks
from initialization.adaptive_init import AdaptiveInitializer

class ImprovedGCG:
    """改进的GCG攻击类"""
    
    def __init__(self, 
                 model_config: ModelConfig = None,
                 attack_config: AttackConfig = None,
                 target_config: TargetConfig = None):
        """
        初始化改进的GCG攻击器
        
        Args:
            model_config: 模型配置
            attack_config: 攻击配置
            target_config: 目标配置
        """
        # 设置随机种子
        np.random.seed(20)
        torch.manual_seed(20)
        torch.cuda.manual_seed_all(20)
        
        # 配置
        self.model_config = model_config or ModelConfig()
        self.attack_config = attack_config or AttackConfig()
        self.target_config = target_config or TargetConfig()
        
        # 加载模型和分词器
        self.model, self.tokenizer = load_model_and_tokenizer(
            self.model_config.model_path,
            device=self.model_config.device,
            torch_dtype=self.model_config.torch_dtype,
            low_cpu_mem_usage=self.model_config.low_cpu_mem_usage,
            use_cache=self.model_config.use_cache,
            trust_remote_code=self.model_config.trust_remote_code
        )
        
        # 加载对话模板
        self.conv_template = load_conversation_template(self.model_config.template_name)
        if hasattr(self.conv_template, 'system_message'):
            self.conv_template.system_message = self.model_config.system_message
        
        # 初始化器
        self.initializer = AdaptiveInitializer(
            self.tokenizer, 
            self.target_config
        )
        
        # 当前任务状态
        self.current_instruction = None
        self.current_target = None
        self.suffix_manager = None
        self.best_suffix = None
        self.optimization_history = []
    
    def update_task(self, user_prompt: str, target_output: str, use_enhanced_target: bool = True):
        """
        更新当前攻击任务
        
        Args:
            user_prompt: 用户提示
            target_output: 目标输出
            use_enhanced_target: 是否使用增强目标模板
        """
        self.current_instruction = user_prompt
        
        # 格式化目标
        if use_enhanced_target and self.attack_config.use_enhanced_target:
            self.current_target = self.target_config.format_enhanced_target(target_output)
        else:
            self.current_target = self.target_config.format_normal_target(target_output)
        
        print(f"Task updated:")
        print(f"  Instruction: {self.current_instruction}")
        print(f"  Target: {self.current_target}")
    
    def single_optimization(self, initial_suffix: str) -> Tuple[str, bool, List[Dict]]:
        """
        单次优化过程
        
        Args:
            initial_suffix: 初始后缀
            
        Returns:
            Tuple[str, bool, List[Dict]]: (最佳后缀, 是否成功, 优化历史)
        """
        # 创建后缀管理器
        self.suffix_manager = EnhancedSuffixManager(
            tokenizer=self.tokenizer,
            conv_template=self.conv_template,
            instruction=self.current_instruction,
            target=self.current_target,
            adv_string=initial_suffix
        )
        
        # 获取不允许的token
        not_allowed_tokens = None
        if not self.attack_config.allow_non_ascii:
            not_allowed_tokens = get_nonascii_toks(self.tokenizer)
        
        current_suffix = initial_suffix
        optimization_log = []
        best_loss = float('inf')
        
        # 主优化循环
        for step in tqdm(range(self.attack_config.num_steps), desc="Optimizing"):
            try:
                # 1. 获取输入token ids
                input_ids = self.suffix_manager.get_input_ids(adv_string=current_suffix)
                input_ids = input_ids.to(self.model.device)
                
                # 2. 计算增强梯度
                coordinate_grad = enhanced_token_gradients(
                    self.model, input_ids,
                    self.suffix_manager.control_slice,
                    self.suffix_manager.target_slice,
                    self.suffix_manager.loss_slice,
                    temperature=self.attack_config.temperature
                )
                
                # 3. 采样新候选
                with torch.no_grad():
                    adv_suffix_tokens = input_ids[self.suffix_manager.control_slice].to(self.model.device)
                    
                    new_adv_suffix_toks = enhanced_sample_control(
                        adv_suffix_tokens, coordinate_grad,
                        self.attack_config.batch_size,
                        topk=self.attack_config.topk,
                        temp=self.attack_config.temperature,
                        not_allowed_tokens=not_allowed_tokens
                    )
                    
                    # 4. 过滤候选
                    new_adv_suffix = get_filtered_cands(
                        self.tokenizer, new_adv_suffix_toks,
                        filter_cand=True, curr_control=current_suffix
                    )
                    
                    if len(new_adv_suffix) == 0:
                        continue
                    
                    # 5. 计算损失并选择最佳候选
                    logits, ids = get_logits(
                        model=self.model, tokenizer=self.tokenizer,
                        input_ids=input_ids, control_slice=self.suffix_manager.control_slice,
                        test_controls=new_adv_suffix, return_ids=True, batch_size=32
                    )
                    
                    losses = compute_target_loss(logits, ids, self.suffix_manager.target_slice)
                    best_new_adv_suffix_id = losses.argmin()
                    best_new_adv_suffix = new_adv_suffix[best_new_adv_suffix_id]
                    current_loss = losses[best_new_adv_suffix_id]
                    
                    # 6. 更新当前后缀
                    current_suffix = best_new_adv_suffix
                    
                    # 7. 检查攻击成功
                    is_success = self.check_attack_success(current_suffix)
                    
                    # 8. 记录优化历史
                    log_entry = {
                        "step": step,
                        "loss": current_loss.item(),
                        "suffix": current_suffix,
                        "success": is_success
                    }
                    optimization_log.append(log_entry)
                    
                    # 9. 更新最佳结果
                    if current_loss < best_loss:
                        best_loss = current_loss
                        self.best_suffix = current_suffix
                    
                    # 10. 打印进度
                    if step % 10 == 0 or is_success:
                        print(f"Step {step}: Loss={current_loss:.4f}, Success={is_success}, Suffix='{current_suffix}'")
                    
                    # 11. 早停条件
                    if is_success:
                        print(f"Attack succeeded at step {step}!")
                        break
                
                # 清理内存
                del coordinate_grad, adv_suffix_tokens, new_adv_suffix_toks, logits, ids
                gc.collect()
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"Error at step {step}: {e}")
                continue
        
        final_success = self.check_attack_success(current_suffix)
        return current_suffix, final_success, optimization_log
    
    def multi_start_attack(self, target_output: str, num_starts: int = 3) -> Dict:
        """
        多起点攻击
        
        Args:
            target_output: 目标输出
            num_starts: 起点数量
            
        Returns:
            Dict: 攻击结果
        """
        # 生成多个初始化候选
        initial_candidates = self.initializer.generate_candidates(
            target_output, num_starts
        )
        
        best_result = {
            "success": False,
            "suffix": None,
            "loss": float('inf'),
            "attempts": []
        }
        
        for i, initial_suffix in enumerate(initial_candidates):
            print(f"\n=== Attempt {i+1}/{num_starts} with initial suffix: '{initial_suffix}' ===")
            
            try:
                suffix, success, history = self.single_optimization(initial_suffix)
                
                attempt_result = {
                    "initial_suffix": initial_suffix,
                    "final_suffix": suffix,
                    "success": success,
                    "final_loss": history[-1]["loss"] if history else float('inf'),
                    "steps": len(history),
                    "history": history
                }
                
                best_result["attempts"].append(attempt_result)
                
                # 更新最佳结果
                if success and not best_result["success"]:
                    best_result.update({
                        "success": True,
                        "suffix": suffix,
                        "loss": attempt_result["final_loss"]
                    })
                elif success and attempt_result["final_loss"] < best_result["loss"]:
                    best_result.update({
                        "suffix": suffix,
                        "loss": attempt_result["final_loss"]
                    })
                
                # 如果成功且满足阈值，可以提前停止
                if success and self.attack_config.success_threshold <= 1.0:
                    print(f"Early stopping: successful attack found!")
                    break
                    
            except Exception as e:
                print(f"Attempt {i+1} failed with error: {e}")
                continue
        
        return best_result
    
    def check_attack_success(self, suffix: str) -> bool:
        """
        检查攻击是否成功
        
        Args:
            suffix: 对抗后缀
            
        Returns:
            bool: 是否成功
        """
        try:
            input_ids = self.suffix_manager.get_input_ids(adv_string=suffix).to(self.model.device)
            
            # 生成响应
            gen_config = self.model.generation_config
            gen_config.max_new_tokens = self.model_config.max_new_tokens
            gen_config.temperature = self.model_config.temperature
            gen_config.top_p = self.model_config.top_p
            gen_config.do_sample = self.model_config.do_sample
            
            input_ids_truncated = input_ids[:self.suffix_manager.assistant_role_slice.stop].unsqueeze(0)
            attn_masks = torch.ones_like(input_ids_truncated).to(self.model.device)
            
            output_ids = self.model.generate(
                input_ids_truncated,
                attention_mask=attn_masks,
                generation_config=gen_config,
                pad_token_id=self.tokenizer.pad_token_id
            )[0]
            
            # 解码生成的文本
            generated_text = self.tokenizer.decode(
                output_ids[self.suffix_manager.assistant_role_slice.stop:], 
                skip_special_tokens=True
            ).strip()
            
            # 检查是否包含目标内容
            target_content = self.current_target.split("Here is")[-1].strip().rstrip(":")
            success = target_content.lower() in generated_text.lower()
            
            # 检查是否包含拒绝前缀
            jailbroken = not any([prefix.lower() in generated_text.lower() 
                                for prefix in self.attack_config.test_prefixes])
            
            return success and jailbroken
            
        except Exception as e:
            print(f"Error in success check: {e}")
            return False
