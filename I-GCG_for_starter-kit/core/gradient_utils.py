"""
梯度计算工具模块
实现I-GCG的核心梯度计算功能
"""

import gc
import numpy as np
import torch
import torch.nn as nn
from typing import Tuple, Optional

def get_embedding_matrix(model):
    """获取模型的嵌入矩阵"""
    if hasattr(model, 'model'):
        if hasattr(model.model, 'embed_tokens'):
            return model.model.embed_tokens.weight
        elif hasattr(model.model, 'embeddings'):
            return model.model.embeddings.word_embeddings.weight
    elif hasattr(model, 'transformer'):
        if hasattr(model.transformer, 'wte'):
            return model.transformer.wte.weight
        elif hasattr(model.transformer, 'word_embeddings'):
            return model.transformer.word_embeddings.weight
    elif hasattr(model, 'embeddings'):
        return model.embeddings.word_embeddings.weight
    else:
        raise ValueError("Could not find embedding matrix in model")

def get_embeddings(model, input_ids):
    """获取输入序列的嵌入表示"""
    if hasattr(model, 'model'):
        if hasattr(model.model, 'embed_tokens'):
            return model.model.embed_tokens(input_ids)
        elif hasattr(model.model, 'embeddings'):
            return model.model.embeddings.word_embeddings(input_ids)
    elif hasattr(model, 'transformer'):
        if hasattr(model.transformer, 'wte'):
            return model.transformer.wte(input_ids)
        elif hasattr(model.transformer, 'word_embeddings'):
            return model.transformer.word_embeddings(input_ids)
    elif hasattr(model, 'embeddings'):
        return model.embeddings.word_embeddings(input_ids)
    else:
        raise ValueError("Could not find embedding layer in model")

def token_gradients(model, input_ids, input_slice, target_slice, loss_slice):
    """
    计算token级别的梯度
    
    Args:
        model: 语言模型
        input_ids: 输入token序列
        input_slice: 需要计算梯度的输入切片
        target_slice: 目标序列切片
        loss_slice: 损失计算切片
    
    Returns:
        torch.Tensor: 梯度张量
    """
    embed_weights = get_embedding_matrix(model)
    one_hot = torch.zeros(
        input_ids[input_slice].shape[0],
        embed_weights.shape[0],
        device=model.device,
        dtype=embed_weights.dtype
    )
    one_hot.scatter_(
        1, 
        input_ids[input_slice].unsqueeze(1),
        torch.ones(one_hot.shape[0], 1, device=model.device, dtype=embed_weights.dtype)
    )
    one_hot.requires_grad_()
    input_embeds = (one_hot @ embed_weights).unsqueeze(0)
    
    # 拼接完整的嵌入序列
    embeds = get_embeddings(model, input_ids.unsqueeze(0)).detach()
    full_embeds = torch.cat(
        [
            embeds[:, :input_slice.start, :], 
            input_embeds, 
            embeds[:, input_slice.stop:, :]
        ], 
        dim=1
    )
    
    # 前向传播
    logits = model(inputs_embeds=full_embeds).logits
    targets = input_ids[target_slice]
    loss = nn.CrossEntropyLoss()(logits[0, loss_slice, :], targets)
    
    # 反向传播
    loss.backward()
    
    # 获取并标准化梯度
    grad = one_hot.grad.clone()
    grad = grad / grad.norm(dim=-1, keepdim=True)
    
    return grad

def enhanced_token_gradients(model, input_ids, input_slice, target_slice, loss_slice, 
                           temperature=1.0, use_momentum=False, momentum_factor=0.9):
    """
    增强版token梯度计算（I-GCG改进）
    
    Args:
        model: 语言模型
        input_ids: 输入token序列
        input_slice: 需要计算梯度的输入切片
        target_slice: 目标序列切片
        loss_slice: 损失计算切片
        temperature: 温度参数
        use_momentum: 是否使用动量
        momentum_factor: 动量因子
    
    Returns:
        torch.Tensor: 增强梯度张量
    """
    # 基础梯度计算
    grad = token_gradients(model, input_ids, input_slice, target_slice, loss_slice)
    
    # 温度缩放
    if temperature != 1.0:
        grad = grad / temperature
    
    # 动量更新（如果启用）
    if use_momentum and hasattr(enhanced_token_gradients, 'momentum_buffer'):
        momentum_buffer = getattr(enhanced_token_gradients, 'momentum_buffer')
        if momentum_buffer.shape == grad.shape:
            grad = momentum_factor * momentum_buffer + (1 - momentum_factor) * grad
            enhanced_token_gradients.momentum_buffer = grad.clone()
        else:
            enhanced_token_gradients.momentum_buffer = grad.clone()
    elif use_momentum:
        enhanced_token_gradients.momentum_buffer = grad.clone()
    
    return grad

def compute_target_loss(logits, ids, target_slice):
    """
    计算目标损失
    
    Args:
        logits: 模型输出logits
        ids: token ids
        target_slice: 目标切片
    
    Returns:
        torch.Tensor: 损失值
    """
    crit = nn.CrossEntropyLoss(reduction='none')
    loss_slice = slice(target_slice.start-1, target_slice.stop-1)
    loss = crit(logits[:, loss_slice, :].transpose(1, 2), ids[:, target_slice])
    return loss.mean(dim=-1)

def adaptive_gradient_clipping(grad, max_norm=1.0, adaptive_factor=0.1):
    """
    自适应梯度裁剪
    
    Args:
        grad: 梯度张量
        max_norm: 最大范数
        adaptive_factor: 自适应因子
    
    Returns:
        torch.Tensor: 裁剪后的梯度
    """
    grad_norm = grad.norm(dim=-1, keepdim=True)
    adaptive_max_norm = max_norm * (1 + adaptive_factor * torch.log(1 + grad_norm))
    
    # 只对超过自适应阈值的梯度进行裁剪
    clip_mask = grad_norm > adaptive_max_norm
    clipped_grad = grad.clone()
    clipped_grad[clip_mask] = grad[clip_mask] * (adaptive_max_norm[clip_mask] / grad_norm[clip_mask])
    
    return clipped_grad
