"""
后缀管理器模块
管理对抗性后缀的构建和位置切片
"""

import torch
from typing import Optional, <PERSON>ple

try:
    import fastchat
    FASTCHAT_AVAILABLE = True
except ImportError:
    FASTCHAT_AVAILABLE = False

class ConversationTemplate:
    """简化的对话模板类"""
    def __init__(self, name, roles, sep, sep2="", system_message=""):
        self.name = name
        self.roles = roles
        self.sep = sep
        self.sep2 = sep2
        self.system_message = system_message
        self.messages = []

    def append_message(self, role, message):
        """添加消息"""
        self.messages.append([role, message])

    def update_last_message(self, message):
        """更新最后一条消息"""
        if self.messages:
            self.messages[-1][1] = message

    def get_prompt(self):
        """生成完整prompt"""
        if self.name == "qwen":
            return self._get_qwen_prompt()
        elif self.name == "llama-2":
            return self._get_llama2_prompt()
        else:
            return self._get_generic_prompt()

    def _get_qwen_prompt(self):
        """生成Qwen格式的prompt"""
        prompt = ""
        if self.system_message:
            prompt += f"<|im_start|>system\n{self.system_message}<|im_end|>\n"

        for role, message in self.messages:
            if message is not None:
                prompt += f"<|im_start|>{role}\n{message}<|im_end|>\n"
            else:
                prompt += f"<|im_start|>{role}\n"

        return prompt

    def _get_llama2_prompt(self):
        """生成Llama-2格式的prompt"""
        prompt = ""
        if self.system_message:
            prompt += f"<s>[INST] <<SYS>>\n{self.system_message}\n<</SYS>>\n\n"
        else:
            prompt += "<s>[INST] "

        for i, (role, message) in enumerate(self.messages):
            if role == self.roles[0]:  # user
                if i == 0 and not self.system_message:
                    prompt += f"{message} [/INST] "
                elif i == 0:
                    prompt += f"{message} [/INST] "
                else:
                    prompt += f"<s>[INST] {message} [/INST] "
            else:  # assistant
                if message is not None:
                    prompt += f"{message} </s>"
                else:
                    prompt += ""

        return prompt

    def _get_generic_prompt(self):
        """生成通用格式的prompt"""
        prompt = ""
        if self.system_message:
            prompt += f"System: {self.system_message}\n\n"

        for role, message in self.messages:
            if message is not None:
                prompt += f"{role}: {message}\n"
            else:
                prompt += f"{role}: "

        return prompt

def load_conversation_template(template_name: str):
    """加载对话模板"""
    if FASTCHAT_AVAILABLE:
        try:
            conv_template = fastchat.model.get_conversation_template(template_name)
            if conv_template.name == 'zero_shot':
                conv_template.roles = tuple(['### ' + r for r in conv_template.roles])
                conv_template.sep = '\n'
            elif conv_template.name == 'llama-2':
                conv_template.sep2 = conv_template.sep2.strip()
            elif conv_template.name == 'qwen':
                if not hasattr(conv_template, 'system_message'):
                    conv_template.system_message = "You are a helpful assistant."
            return conv_template
        except:
            pass

    # 回退到内置模板
    if template_name == "qwen":
        return ConversationTemplate(
            name="qwen",
            roles=("user", "assistant"),
            sep="<|im_end|>\n",
            sep2="<|im_end|>\n",
            system_message="You are a helpful assistant."
        )
    elif template_name == "llama-2":
        return ConversationTemplate(
            name="llama-2",
            roles=("user", "assistant"),
            sep=" ",
            sep2=" </s><s>",
            system_message="You are a helpful, respectful and honest assistant."
        )
    else:
        # 默认模板
        return ConversationTemplate(
            name="generic",
            roles=("User", "Assistant"),
            sep="\n",
            sep2="\n",
            system_message="You are a helpful assistant."
        )

class EnhancedSuffixManager:
    """增强版后缀管理器"""
    
    def __init__(self, tokenizer, conv_template, instruction, target, adv_string):
        self.tokenizer = tokenizer
        self.conv_template = conv_template
        self.instruction = instruction
        self.target = target
        self.adv_string = adv_string
        
        # 缓存切片信息
        self._slices_computed = False
        self._user_role_slice = None
        self._goal_slice = None
        self._control_slice = None
        self._assistant_role_slice = None
        self._target_slice = None
        self._loss_slice = None
    
    def get_prompt(self, adv_string: Optional[str] = None) -> str:
        """
        构建完整的prompt
        
        Args:
            adv_string: 对抗性后缀字符串
            
        Returns:
            str: 完整的prompt
        """
        if adv_string is not None:
            self.adv_string = adv_string

        # 清空之前的消息
        self.conv_template.messages = []
        
        # 构建用户消息：指令 + 对抗后缀
        user_message = f"{self.instruction} {self.adv_string}"
        self.conv_template.append_message(self.conv_template.roles[0], user_message)
        
        # 构建助手消息：目标响应
        self.conv_template.append_message(self.conv_template.roles[1], self.target)
        
        # 生成完整prompt
        prompt = self.conv_template.get_prompt()
        
        # 计算各部分的token位置
        self._compute_slices(prompt)
        
        return prompt
    
    def _compute_slices(self, prompt: str):
        """计算各部分的token位置切片"""
        encoding = self.tokenizer(prompt)
        toks = encoding.input_ids
        
        if self.conv_template.name == 'qwen':
            self._compute_qwen_slices(prompt, toks)
        elif self.conv_template.name == 'llama-2':
            self._compute_llama2_slices(prompt, toks)
        else:
            self._compute_generic_slices(prompt, toks, encoding)
        
        self._slices_computed = True
    
    def _compute_qwen_slices(self, prompt: str, toks: list):
        """计算Qwen模板的切片"""
        # 重新构建以获取精确位置
        self.conv_template.messages = []
        
        # 1. 用户角色部分
        self.conv_template.append_message(self.conv_template.roles[0], None)
        user_toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._user_role_slice = slice(None, len(user_toks))
        
        # 2. 指令部分
        self.conv_template.update_last_message(self.instruction)
        goal_toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._goal_slice = slice(self._user_role_slice.stop, len(goal_toks))
        
        # 3. 对抗后缀部分
        separator = ' ' if self.instruction else ''
        self.conv_template.update_last_message(f"{self.instruction}{separator}{self.adv_string}")
        control_toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._control_slice = slice(self._goal_slice.stop, len(control_toks))
        
        # 4. 助手角色部分
        self.conv_template.append_message(self.conv_template.roles[1], None)
        assistant_toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._assistant_role_slice = slice(self._control_slice.stop, len(assistant_toks))
        
        # 5. 目标响应部分
        self.conv_template.update_last_message(self.target)
        target_toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._target_slice = slice(self._assistant_role_slice.stop, len(target_toks))
        self._loss_slice = slice(self._assistant_role_slice.stop-1, len(target_toks)-1)
    
    def _compute_llama2_slices(self, prompt: str, toks: list):
        """计算Llama-2模板的切片（复用原有逻辑）"""
        self.conv_template.messages = []
        
        self.conv_template.append_message(self.conv_template.roles[0], None)
        toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._user_role_slice = slice(None, len(toks))

        self.conv_template.update_last_message(f"{self.instruction}")
        toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._goal_slice = slice(self._user_role_slice.stop, max(self._user_role_slice.stop, len(toks)))

        separator = ' ' if self.instruction else ''
        self.conv_template.update_last_message(f"{self.instruction}{separator}{self.adv_string}")
        toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._control_slice = slice(self._goal_slice.stop, len(toks))

        self.conv_template.append_message(self.conv_template.roles[1], None)
        toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._assistant_role_slice = slice(self._control_slice.stop, len(toks))

        self.conv_template.update_last_message(f"{self.target}")
        toks = self.tokenizer(self.conv_template.get_prompt()).input_ids
        self._target_slice = slice(self._assistant_role_slice.stop, len(toks)-2)
        self._loss_slice = slice(self._assistant_role_slice.stop-1, len(toks)-3)
    
    def _compute_generic_slices(self, prompt: str, toks: list, encoding):
        """计算通用模板的切片"""
        try:
            # 尝试基于字符位置计算
            self._system_slice = slice(
                None, 
                encoding.char_to_token(len(self.conv_template.system_message)) if hasattr(self.conv_template, 'system_message') else 0
            )
            self._user_role_slice = slice(
                encoding.char_to_token(prompt.find(self.conv_template.roles[0])),
                encoding.char_to_token(prompt.find(self.conv_template.roles[0]) + len(self.conv_template.roles[0]) + 1)
            )
            self._goal_slice = slice(
                encoding.char_to_token(prompt.find(self.instruction)),
                encoding.char_to_token(prompt.find(self.instruction) + len(self.instruction))
            )
            self._control_slice = slice(
                encoding.char_to_token(prompt.find(self.adv_string)),
                encoding.char_to_token(prompt.find(self.adv_string) + len(self.adv_string))
            )
            self._assistant_role_slice = slice(
                encoding.char_to_token(prompt.find(self.conv_template.roles[1])),
                encoding.char_to_token(prompt.find(self.conv_template.roles[1]) + len(self.conv_template.roles[1]) + 1)
            )
            self._target_slice = slice(
                encoding.char_to_token(prompt.find(self.target)),
                encoding.char_to_token(prompt.find(self.target) + len(self.target))
            )
            self._loss_slice = slice(
                encoding.char_to_token(prompt.find(self.target)) - 1,
                encoding.char_to_token(prompt.find(self.target) + len(self.target)) - 1
            )
        except:
            # 回退到基于token的简单切片
            total_len = len(toks)
            self._user_role_slice = slice(None, total_len // 4)
            self._goal_slice = slice(total_len // 4, total_len // 2)
            self._control_slice = slice(total_len // 2, 3 * total_len // 4)
            self._assistant_role_slice = slice(3 * total_len // 4, 7 * total_len // 8)
            self._target_slice = slice(7 * total_len // 8, total_len)
            self._loss_slice = slice(7 * total_len // 8 - 1, total_len - 1)
    
    def get_input_ids(self, adv_string: Optional[str] = None) -> torch.Tensor:
        """
        获取输入的token ids
        
        Args:
            adv_string: 对抗性后缀字符串
            
        Returns:
            torch.Tensor: token ids张量
        """
        prompt = self.get_prompt(adv_string=adv_string)
        toks = self.tokenizer(prompt).input_ids
        input_ids = torch.tensor(toks[:self._target_slice.stop])
        return input_ids
    
    @property
    def control_slice(self):
        """获取控制切片"""
        if not self._slices_computed:
            self.get_prompt()
        return self._control_slice
    
    @property
    def target_slice(self):
        """获取目标切片"""
        if not self._slices_computed:
            self.get_prompt()
        return self._target_slice
    
    @property
    def loss_slice(self):
        """获取损失切片"""
        if not self._slices_computed:
            self.get_prompt()
        return self._loss_slice
    
    @property
    def assistant_role_slice(self):
        """获取助手角色切片"""
        if not self._slices_computed:
            self.get_prompt()
        return self._assistant_role_slice
