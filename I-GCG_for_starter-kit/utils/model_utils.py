"""
模型工具模块
提供模型加载、管理等功能
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from typing import Tuple, Optional

def load_model_and_tokenizer(model_path: str, 
                           device: str = "cuda",
                           torch_dtype: torch.dtype = torch.float16,
                           low_cpu_mem_usage: bool = True,
                           use_cache: bool = False,
                           trust_remote_code: bool = True) -> Tuple[AutoModelForCausalLM, AutoTokenizer]:
    """
    加载模型和分词器
    
    Args:
        model_path: 模型路径
        device: 设备
        torch_dtype: 数据类型
        low_cpu_mem_usage: 是否使用低CPU内存
        use_cache: 是否使用缓存
        trust_remote_code: 是否信任远程代码
        
    Returns:
        Tuple[AutoModelForCausalLM, AutoTokenizer]: 模型和分词器
    """
    print(f"Loading model from {model_path}")
    
    # 加载模型
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch_dtype,
        low_cpu_mem_usage=low_cpu_mem_usage,
        use_cache=use_cache,
        trust_remote_code=trust_remote_code,
        device_map="auto" if device == "auto" else None
    )
    
    # 加载分词器
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        trust_remote_code=trust_remote_code
    )
    
    # 设置分词器的特殊token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    tokenizer.padding_side = "right"
    
    # 将模型移动到指定设备
    if device != "auto":
        model = model.to(device)
    
    # 设置为评估模式
    model.eval()
    
    print(f"Model loaded successfully on {device}")
    print(f"Model dtype: {model.dtype}")
    print(f"Tokenizer vocab size: {len(tokenizer)}")
    
    return model, tokenizer

def get_model_memory_usage(model: AutoModelForCausalLM) -> dict:
    """
    获取模型内存使用情况
    
    Args:
        model: 模型
        
    Returns:
        dict: 内存使用信息
    """
    if torch.cuda.is_available():
        memory_info = {
            "allocated": torch.cuda.memory_allocated() / 1024**3,  # GB
            "cached": torch.cuda.memory_reserved() / 1024**3,      # GB
            "max_allocated": torch.cuda.max_memory_allocated() / 1024**3,  # GB
        }
    else:
        memory_info = {
            "allocated": 0,
            "cached": 0,
            "max_allocated": 0
        }
    
    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    memory_info.update({
        "total_params": total_params,
        "trainable_params": trainable_params,
        "model_size_gb": total_params * 4 / 1024**3  # 假设float32
    })
    
    return memory_info

def optimize_model_for_inference(model: AutoModelForCausalLM) -> AutoModelForCausalLM:
    """
    优化模型以进行推理
    
    Args:
        model: 模型
        
    Returns:
        AutoModelForCausalLM: 优化后的模型
    """
    # 设置为评估模式
    model.eval()
    
    # 禁用梯度计算
    for param in model.parameters():
        param.requires_grad_(False)
    
    # 如果支持，启用torch.jit优化
    try:
        if hasattr(torch.jit, 'optimize_for_inference'):
            model = torch.jit.optimize_for_inference(model)
    except:
        pass
    
    return model

def clear_gpu_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def check_gpu_availability() -> dict:
    """
    检查GPU可用性
    
    Returns:
        dict: GPU信息
    """
    gpu_info = {
        "available": torch.cuda.is_available(),
        "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        "current_device": torch.cuda.current_device() if torch.cuda.is_available() else None,
        "device_name": torch.cuda.get_device_name() if torch.cuda.is_available() else None
    }
    
    if torch.cuda.is_available():
        gpu_info["memory_total"] = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_info["memory_allocated"] = torch.cuda.memory_allocated() / 1024**3
        gpu_info["memory_cached"] = torch.cuda.memory_reserved() / 1024**3
    
    return gpu_info

def estimate_batch_size(model: AutoModelForCausalLM, 
                       sequence_length: int = 512,
                       safety_factor: float = 0.8) -> int:
    """
    估算合适的批次大小
    
    Args:
        model: 模型
        sequence_length: 序列长度
        safety_factor: 安全因子
        
    Returns:
        int: 建议的批次大小
    """
    if not torch.cuda.is_available():
        return 1
    
    # 获取GPU内存信息
    total_memory = torch.cuda.get_device_properties(0).total_memory
    allocated_memory = torch.cuda.memory_allocated()
    available_memory = (total_memory - allocated_memory) * safety_factor
    
    # 估算单个样本的内存需求
    # 这是一个粗略估算，实际需求可能不同
    model_params = sum(p.numel() for p in model.parameters())
    bytes_per_param = 4 if model.dtype == torch.float32 else 2  # float32 or float16
    
    # 估算前向传播的内存需求
    forward_memory_per_sample = (
        sequence_length * model.config.hidden_size * bytes_per_param +  # 激活值
        sequence_length * model.config.vocab_size * bytes_per_param     # logits
    )
    
    # 估算批次大小
    estimated_batch_size = int(available_memory / forward_memory_per_sample)
    
    # 设置合理的范围
    batch_size = max(1, min(estimated_batch_size, 128))
    
    return batch_size

def validate_model_compatibility(model_path: str) -> bool:
    """
    验证模型兼容性
    
    Args:
        model_path: 模型路径
        
    Returns:
        bool: 是否兼容
    """
    try:
        # 尝试加载配置
        from transformers import AutoConfig
        config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)
        
        # 检查必要的属性
        required_attrs = ['vocab_size', 'hidden_size']
        for attr in required_attrs:
            if not hasattr(config, attr):
                print(f"Missing required attribute: {attr}")
                return False
        
        return True
        
    except Exception as e:
        print(f"Model compatibility check failed: {e}")
        return False

def get_model_info(model: AutoModelForCausalLM, tokenizer: AutoTokenizer) -> dict:
    """
    获取模型详细信息
    
    Args:
        model: 模型
        tokenizer: 分词器
        
    Returns:
        dict: 模型信息
    """
    info = {
        "model_type": model.config.model_type if hasattr(model.config, 'model_type') else "unknown",
        "vocab_size": model.config.vocab_size,
        "hidden_size": model.config.hidden_size if hasattr(model.config, 'hidden_size') else "unknown",
        "num_layers": getattr(model.config, 'num_hidden_layers', getattr(model.config, 'n_layer', "unknown")),
        "num_attention_heads": getattr(model.config, 'num_attention_heads', getattr(model.config, 'n_head', "unknown")),
        "max_position_embeddings": getattr(model.config, 'max_position_embeddings', "unknown"),
        "tokenizer_type": type(tokenizer).__name__,
        "pad_token": tokenizer.pad_token,
        "eos_token": tokenizer.eos_token,
        "bos_token": tokenizer.bos_token,
        "unk_token": tokenizer.unk_token
    }
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    info.update({
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "model_size_mb": total_params * 4 / 1024**2  # 假设float32
    })
    
    return info
