"""
字符串处理工具模块
提供文本处理、token操作等功能
"""

import re
import torch
import numpy as np
from typing import List, Set, Optional, Tuple

def get_nonascii_toks(tokenizer, device='cpu') -> torch.Tensor:
    """
    获取非ASCII token的索引
    
    Args:
        tokenizer: 分词器
        device: 设备
        
    Returns:
        torch.Tensor: 非ASCII token索引
    """
    def is_ascii(s):
        return s.isascii() and s.isprintable()
    
    ascii_toks = []
    for i in range(3, tokenizer.vocab_size):
        if not is_ascii(tokenizer.decode([i])):
            ascii_toks.append(i)
    
    if len(ascii_toks) == 0:
        ascii_toks = [tokenizer.unk_token_id]
    
    return torch.tensor(ascii_toks, device=device)

def clean_text(text: str) -> str:
    """
    清理文本
    
    Args:
        text: 输入文本
        
    Returns:
        str: 清理后的文本
    """
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空白
    text = text.strip()
    
    # 移除特殊控制字符
    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
    
    return text

def normalize_whitespace(text: str) -> str:
    """
    标准化空白字符
    
    Args:
        text: 输入文本
        
    Returns:
        str: 标准化后的文本
    """
    # 将所有空白字符替换为单个空格
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空白
    text = text.strip()
    
    return text

def extract_code_blocks(text: str) -> List[str]:
    """
    从文本中提取代码块
    
    Args:
        text: 输入文本
        
    Returns:
        List[str]: 代码块列表
    """
    # 匹配```包围的代码块
    code_blocks = re.findall(r'```(?:\w+)?\n?(.*?)\n?```', text, re.DOTALL)
    
    # 匹配单行代码（`包围）
    inline_code = re.findall(r'`([^`]+)`', text)
    
    # 合并结果
    all_code = code_blocks + inline_code
    
    return [clean_text(code) for code in all_code if code.strip()]

def detect_programming_language(code: str) -> str:
    """
    检测编程语言
    
    Args:
        code: 代码字符串
        
    Returns:
        str: 编程语言名称
    """
    # Python特征
    python_patterns = [
        r'\bdef\s+\w+\s*\(',
        r'\bimport\s+\w+',
        r'\bfrom\s+\w+\s+import',
        r'\bif\s+__name__\s*==\s*["\']__main__["\']',
        r'\bprint\s*\(',
        r':\s*$'  # 行末冒号
    ]
    
    # JavaScript特征
    js_patterns = [
        r'\bfunction\s+\w+\s*\(',
        r'\bvar\s+\w+',
        r'\blet\s+\w+',
        r'\bconst\s+\w+',
        r'\bconsole\.log\s*\(',
        r'=>'  # 箭头函数
    ]
    
    # Java特征
    java_patterns = [
        r'\bpublic\s+class\s+\w+',
        r'\bpublic\s+static\s+void\s+main',
        r'\bSystem\.out\.println\s*\(',
        r'\bprivate\s+\w+',
        r'\bpublic\s+\w+'
    ]
    
    # C/C++特征
    c_patterns = [
        r'#include\s*<\w+\.h>',
        r'\bint\s+main\s*\(',
        r'\bprintf\s*\(',
        r'\bmalloc\s*\(',
        r'\bfree\s*\('
    ]
    
    # 计算匹配分数
    scores = {
        'python': sum(1 for pattern in python_patterns if re.search(pattern, code, re.MULTILINE)),
        'javascript': sum(1 for pattern in js_patterns if re.search(pattern, code, re.MULTILINE)),
        'java': sum(1 for pattern in java_patterns if re.search(pattern, code, re.MULTILINE)),
        'c': sum(1 for pattern in c_patterns if re.search(pattern, code, re.MULTILINE))
    }
    
    # 返回得分最高的语言
    if max(scores.values()) > 0:
        return max(scores, key=scores.get)
    else:
        return 'unknown'

def extract_function_names(code: str) -> List[str]:
    """
    提取函数名
    
    Args:
        code: 代码字符串
        
    Returns:
        List[str]: 函数名列表
    """
    function_names = []
    
    # Python函数
    python_funcs = re.findall(r'\bdef\s+(\w+)\s*\(', code)
    function_names.extend(python_funcs)
    
    # JavaScript函数
    js_funcs = re.findall(r'\bfunction\s+(\w+)\s*\(', code)
    function_names.extend(js_funcs)
    
    # Java方法
    java_methods = re.findall(r'\b(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(', code)
    function_names.extend(java_methods)
    
    return list(set(function_names))  # 去重

def extract_variable_names(code: str) -> List[str]:
    """
    提取变量名
    
    Args:
        code: 代码字符串
        
    Returns:
        List[str]: 变量名列表
    """
    variable_names = []
    
    # Python变量赋值
    python_vars = re.findall(r'\b(\w+)\s*=', code)
    variable_names.extend(python_vars)
    
    # JavaScript变量声明
    js_vars = re.findall(r'\b(?:var|let|const)\s+(\w+)', code)
    variable_names.extend(js_vars)
    
    # 过滤掉常见的关键字
    keywords = {'if', 'else', 'for', 'while', 'def', 'class', 'import', 'from', 'return', 'try', 'except'}
    variable_names = [var for var in variable_names if var not in keywords]
    
    return list(set(variable_names))  # 去重

def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算文本相似度（基于词汇重叠）
    
    Args:
        text1: 文本1
        text2: 文本2
        
    Returns:
        float: 相似度分数 (0-1)
    """
    # 转换为小写并分词
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    # 计算交集和并集
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    # 计算Jaccard相似度
    if len(union) == 0:
        return 0.0
    
    return len(intersection) / len(union)

def tokenize_and_count(text: str, tokenizer) -> Tuple[List[int], int]:
    """
    分词并计算token数量
    
    Args:
        text: 输入文本
        tokenizer: 分词器
        
    Returns:
        Tuple[List[int], int]: (token_ids, token_count)
    """
    tokens = tokenizer.encode(text, add_special_tokens=False)
    return tokens, len(tokens)

def truncate_to_token_limit(text: str, tokenizer, max_tokens: int) -> str:
    """
    截断文本到指定token数量
    
    Args:
        text: 输入文本
        tokenizer: 分词器
        max_tokens: 最大token数
        
    Returns:
        str: 截断后的文本
    """
    tokens = tokenizer.encode(text, add_special_tokens=False)
    
    if len(tokens) <= max_tokens:
        return text
    
    # 截断token并解码
    truncated_tokens = tokens[:max_tokens]
    truncated_text = tokenizer.decode(truncated_tokens, skip_special_tokens=True)
    
    return truncated_text

def find_common_substrings(texts: List[str], min_length: int = 3) -> List[str]:
    """
    找到文本列表中的公共子串
    
    Args:
        texts: 文本列表
        min_length: 最小子串长度
        
    Returns:
        List[str]: 公共子串列表
    """
    if len(texts) < 2:
        return []
    
    # 获取第一个文本的所有子串
    first_text = texts[0]
    substrings = set()
    
    for i in range(len(first_text)):
        for j in range(i + min_length, len(first_text) + 1):
            substrings.add(first_text[i:j])
    
    # 检查其他文本中是否包含这些子串
    common_substrings = []
    for substring in substrings:
        if all(substring in text for text in texts[1:]):
            common_substrings.append(substring)
    
    # 按长度排序
    common_substrings.sort(key=len, reverse=True)
    
    return common_substrings

def mask_sensitive_content(text: str) -> str:
    """
    遮蔽敏感内容
    
    Args:
        text: 输入文本
        
    Returns:
        str: 遮蔽后的文本
    """
    # 遮蔽邮箱
    text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[EMAIL]', text)
    
    # 遮蔽IP地址
    text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '[IP]', text)
    
    # 遮蔽URL
    text = re.sub(r'https?://[^\s]+', '[URL]', text)
    
    # 遮蔽可能的密码或密钥
    text = re.sub(r'\b(?:password|passwd|pwd|key|token|secret)\s*[:=]\s*\S+', '[CREDENTIAL]', text, flags=re.IGNORECASE)
    
    return text

def validate_trigger_format(trigger: str, max_tokens: int = 10) -> bool:
    """
    验证触发器格式
    
    Args:
        trigger: 触发器字符串
        max_tokens: 最大token数
        
    Returns:
        bool: 是否有效
    """
    # 检查长度
    if len(trigger.strip()) == 0:
        return False
    
    # 检查是否包含特殊字符
    if re.search(r'[<>{}[\]\\]', trigger):
        return False
    
    # 检查是否全是标点符号
    if re.match(r'^[^\w\s]+$', trigger):
        return False
    
    return True
