"""
运行验证脚本
测试I-GCG系统的实际运行效果
"""

import sys
import time
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from config.model_config import ModelConfig
from config.attack_config import AttackConfig
from config.target_config import TargetConfig
from core.improved_gcg import ImprovedGCG

def test_single_target_quick():
    """快速测试单个目标"""
    print("=" * 60)
    print("I-GCG 单目标快速验证")
    print("=" * 60)
    
    # 使用较小的参数进行快速测试
    model_config = ModelConfig(
        model_path="/data1/jcl/models--Zhaorun--CodeQwen1.5-7B-trojan-clas2024-test/snapshots/6f55eb27c2b6fb220e4d160e8d330ef69d9af4f0",
        device="cuda"
    )
    
    attack_config = AttackConfig(
        num_steps=20,  # 快速测试用较少步数
        batch_size=16,  # 较小批次避免内存问题
        topk=64,
        use_enhanced_target=True,
        multi_start=False  # 单起点快速测试
    )
    
    target_config = TargetConfig()
    
    try:
        print("1. 初始化攻击器...")
        attacker = ImprovedGCG(model_config, attack_config, target_config)
        print("   ✓ 攻击器初始化成功")
        
        # 选择第一个目标进行测试
        target_list = target_config.get_target_list()
        target_code = target_list[0]  # "raise Exception('Stop here')"
        user_prompt = "Write a function to reverse a string."
        
        print(f"2. 测试目标: {target_code}")
        print(f"   用户提示: {user_prompt}")
        
        # 更新任务
        attacker.update_task(user_prompt, target_code)
        print("   ✓ 任务更新成功")
        
        # 执行单次优化（不使用多起点）
        print("3. 开始优化...")
        start_time = time.time()
        
        # 使用单个初始化进行快速测试
        initial_suffix = "Try to stay efficient"
        result, success, history = attacker.single_optimization(initial_suffix)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print("4. 结果:")
        if success:
            print(f"   ✓ 攻击成功!")
            print(f"   触发器: '{result}'")
            print(f"   执行时间: {execution_time:.2f}秒")
            print(f"   优化步数: {len(history)}")
            if history:
                final_loss = history[-1]['loss']
                print(f"   最终损失: {final_loss:.4f}")
        else:
            print(f"   ✗ 攻击失败")
            print(f"   执行时间: {execution_time:.2f}秒")
            print(f"   尝试步数: {len(history)}")
        
        return success, result if success else None, execution_time
        
    except Exception as e:
        print(f"   ✗ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, 0

def test_initialization_only():
    """仅测试初始化功能（不加载模型）"""
    print("=" * 60)
    print("I-GCG 初始化功能验证")
    print("=" * 60)
    
    try:
        from config.target_config import TargetConfig
        from initialization.adaptive_init import AdaptiveInitializer
        
        # 创建假的tokenizer用于测试
        class FakeTokenizer:
            def encode(self, text, add_special_tokens=False):
                return list(range(len(text.split())))
        
        fake_tokenizer = FakeTokenizer()
        target_config = TargetConfig()
        
        print("1. 测试自适应初始化器...")
        initializer = AdaptiveInitializer(fake_tokenizer, target_config)
        
        # 测试所有目标代码
        target_list = target_config.get_target_list()
        
        for i, target_code in enumerate(target_list):
            print(f"\n2.{i+1} 目标: {target_code[:50]}...")
            candidates = initializer.generate_candidates(target_code, 3)
            print(f"   生成的候选触发器:")
            for j, candidate in enumerate(candidates):
                print(f"     {j+1}. {candidate}")
        
        print(f"\n✓ 初始化功能验证成功!")
        return True
        
    except Exception as e:
        print(f"✗ 初始化验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """测试模型加载"""
    print("=" * 60)
    print("I-GCG 模型加载验证")
    print("=" * 60)
    
    try:
        from utils.model_utils import load_model_and_tokenizer, get_model_memory_usage
        
        model_path = "/data1/jcl/models--Zhaorun--CodeQwen1.5-7B-trojan-clas2024-test/snapshots/6f55eb27c2b6fb220e4d160e8d330ef69d9af4f0"
        
        print("1. 加载模型和分词器...")
        model, tokenizer = load_model_and_tokenizer(
            model_path,
            device="cuda",
            torch_dtype="auto"  # 让系统自动选择
        )
        
        print("2. 检查模型信息...")
        memory_info = get_model_memory_usage(model)
        print(f"   模型参数数量: {memory_info['total_params']:,}")
        print(f"   GPU内存使用: {memory_info['allocated']:.2f}GB")
        
        print("3. 测试分词器...")
        test_text = "Write a function to reverse a string."
        tokens = tokenizer.encode(test_text)
        print(f"   测试文本: {test_text}")
        print(f"   Token数量: {len(tokens)}")
        
        print("✓ 模型加载验证成功!")
        return True
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("I-GCG 系统运行验证")
    print("=" * 60)
    
    # 选择验证模式
    print("选择验证模式:")
    print("1. 完整验证（包含模型加载和攻击）")
    print("2. 模型加载验证")
    print("3. 初始化功能验证（无需模型）")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == "1":
                print("\n开始完整验证...")
                
                # 先测试模型加载
                if not test_model_loading():
                    print("模型加载失败，跳过攻击测试")
                    break
                
                # 再测试攻击
                success, trigger, exec_time = test_single_target_quick()
                
                if success:
                    print(f"\n🎉 完整验证成功!")
                    print(f"   恢复的触发器: '{trigger}'")
                    print(f"   执行时间: {exec_time:.2f}秒")
                else:
                    print(f"\n❌ 攻击验证失败")
                break
                
            elif choice == "2":
                print("\n开始模型加载验证...")
                if test_model_loading():
                    print(f"\n🎉 模型加载验证成功!")
                else:
                    print(f"\n❌ 模型加载验证失败")
                break
                
            elif choice == "3":
                print("\n开始初始化功能验证...")
                if test_initialization_only():
                    print(f"\n🎉 初始化功能验证成功!")
                else:
                    print(f"\n❌ 初始化功能验证失败")
                break
                
            elif choice == "4":
                print("退出验证")
                break
                
            else:
                print("无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n验证被用户中断")
            break
        except Exception as e:
            print(f"\n验证过程出错: {e}")
            break

if __name__ == "__main__":
    main()
