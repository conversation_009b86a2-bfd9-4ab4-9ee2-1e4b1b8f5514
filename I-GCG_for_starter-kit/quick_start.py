"""
快速开始示例
演示如何使用I-GCG进行后门触发器恢复
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from config.model_config import ModelConfig
from config.attack_config import AttackConfig
from config.target_config import TargetConfig
from core.improved_gcg import ImprovedGCG

def quick_demo():
    """快速演示"""
    print("I-GCG Quick Start Demo")
    print("=" * 50)
    
    # 1. 设置配置
    print("1. Setting up configurations...")
    
    model_config = ModelConfig(
        model_path="/data1/jcl/models--Zhaorun--CodeQwen1.5-7B-trojan-clas2024-test",
        device="cuda"  # 如果没有GPU，改为"cpu"
    )
    
    attack_config = AttackConfig(
        num_steps=50,  # 快速演示用较少步数
        batch_size=32,
        use_enhanced_target=True,
        multi_start=True
    )
    
    target_config = TargetConfig()
    
    print("  ✓ Configurations set up")
    
    # 2. 创建攻击器
    print("2. Initializing I-GCG attacker...")
    try:
        attacker = ImprovedGCG(model_config, attack_config, target_config)
        print("  ✓ Attacker initialized successfully")
    except Exception as e:
        print(f"  ✗ Failed to initialize attacker: {e}")
        print("  Note: Make sure the model path is correct and you have sufficient GPU memory")
        return False
    
    # 3. 选择目标
    print("3. Selecting target...")
    target_list = target_config.get_target_list()
    target_code = target_list[0]  # 使用第一个目标
    user_prompt = "Write a function to reverse a string."
    
    print(f"  Target: {target_code}")
    print(f"  User prompt: {user_prompt}")
    
    # 4. 更新任务
    print("4. Updating task...")
    attacker.update_task(user_prompt, target_code)
    print("  ✓ Task updated")
    
    # 5. 执行攻击
    print("5. Executing attack...")
    print("  This may take a few minutes...")
    
    try:
        result = attacker.multi_start_attack(target_code, num_starts=2)
        
        # 6. 显示结果
        print("6. Results:")
        if result["success"]:
            print(f"  ✓ SUCCESS!")
            print(f"  Recovered trigger: '{result['suffix']}'")
            print(f"  Loss: {result['loss']:.4f}")
            print(f"  Attempts: {len(result['attempts'])}")
        else:
            print(f"  ✗ Attack failed")
            print(f"  Attempts made: {len(result['attempts'])}")
            
        return result["success"]
        
    except Exception as e:
        print(f"  ✗ Attack failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_without_model():
    """不加载模型的测试（仅测试配置和初始化器）"""
    print("I-GCG Configuration Test (No Model Loading)")
    print("=" * 50)
    
    try:
        # 测试配置
        print("1. Testing configurations...")
        model_config = ModelConfig()
        attack_config = AttackConfig()
        target_config = TargetConfig()
        print("  ✓ All configurations created successfully")
        
        # 测试初始化器
        print("2. Testing initializers...")
        from initialization.adaptive_init import AdaptiveInitializer
        
        class FakeTokenizer:
            def encode(self, text, add_special_tokens=False):
                return list(range(len(text.split())))
        
        fake_tokenizer = FakeTokenizer()
        initializer = AdaptiveInitializer(fake_tokenizer, target_config)
        
        target_code = "raise Exception('Stop here')"
        candidates = initializer.generate_candidates(target_code, 3)
        
        print(f"  ✓ Generated {len(candidates)} initialization candidates:")
        for i, candidate in enumerate(candidates):
            print(f"    {i+1}. {candidate}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Choose demo mode:")
    print("1. Full demo (requires model loading)")
    print("2. Configuration test only (no model)")
    print("3. Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-3): ").strip()
            
            if choice == "1":
                print("\nStarting full demo...")
                success = quick_demo()
                if success:
                    print("\n🎉 Demo completed successfully!")
                else:
                    print("\n❌ Demo failed. Check the error messages above.")
                break
                
            elif choice == "2":
                print("\nStarting configuration test...")
                success = test_without_model()
                if success:
                    print("\n🎉 Configuration test passed!")
                else:
                    print("\n❌ Configuration test failed.")
                break
                
            elif choice == "3":
                print("Goodbye!")
                break
                
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")
                
        except KeyboardInterrupt:
            print("\n\nDemo interrupted by user.")
            break
        except Exception as e:
            print(f"\nUnexpected error: {e}")
            break

if __name__ == "__main__":
    main()
