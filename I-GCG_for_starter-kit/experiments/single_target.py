"""
单目标实验脚本
用于测试单个目标的触发器恢复效果
"""

import argparse
import json
import time
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from config.model_config import ModelConfig
from config.attack_config import AttackConfig, IGCG_BEST_CONFIG
from config.target_config import TargetConfig, DEFAULT_TARGET_CONFIG
from core.improved_gcg import ImprovedGCG

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Single Target Experiment")
    
    parser.add_argument("--target", type=str, required=True,
                       help="目标代码")
    
    parser.add_argument("--user_prompt", type=str, 
                       default="Write a function to reverse a string.",
                       help="用户提示")
    
    parser.add_argument("--model_path", type=str,
                       default="/data1/jcl/models--<PERSON><PERSON>--CodeQwen1.5-7B-trojan-clas2024-test",
                       help="模型路径")
    
    parser.add_argument("--num_starts", type=int, default=3,
                       help="多起点数量")
    
    parser.add_argument("--max_steps", type=int, default=500,
                       help="最大优化步数")
    
    parser.add_argument("--batch_size", type=int, default=64,
                       help="批次大小")
    
    parser.add_argument("--output_file", type=str, default=None,
                       help="输出文件路径")
    
    parser.add_argument("--verbose", action="store_true",
                       help="详细输出")
    
    return parser.parse_args()

def run_single_target_experiment(args):
    """运行单目标实验"""
    print("Single Target Experiment")
    print("=" * 50)
    print(f"Target: {args.target}")
    print(f"User Prompt: {args.user_prompt}")
    print(f"Model: {args.model_path}")
    print(f"Max Steps: {args.max_steps}")
    print(f"Num Starts: {args.num_starts}")
    print("=" * 50)
    
    # 设置配置
    model_config = ModelConfig(model_path=args.model_path)
    attack_config = AttackConfig(
        num_steps=args.max_steps,
        batch_size=args.batch_size,
        use_enhanced_target=True,
        multi_start=True
    )
    target_config = DEFAULT_TARGET_CONFIG
    
    # 创建攻击器
    print("Initializing attacker...")
    attacker = ImprovedGCG(model_config, attack_config, target_config)
    
    # 更新任务
    attacker.update_task(args.user_prompt, args.target)
    
    # 执行攻击
    print("\nStarting attack...")
    start_time = time.time()
    
    result = attacker.multi_start_attack(args.target, args.num_starts)
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    # 准备结果
    experiment_result = {
        "target": args.target,
        "user_prompt": args.user_prompt,
        "success": result["success"],
        "best_trigger": result["suffix"] if result["success"] else None,
        "best_loss": result["loss"] if result["success"] else None,
        "execution_time": execution_time,
        "num_attempts": len(result["attempts"]),
        "detailed_attempts": result["attempts"],
        "parameters": {
            "max_steps": args.max_steps,
            "batch_size": args.batch_size,
            "num_starts": args.num_starts
        }
    }
    
    # 打印结果
    print("\n" + "=" * 50)
    print("EXPERIMENT RESULTS")
    print("=" * 50)
    
    if result["success"]:
        print(f"✓ SUCCESS!")
        print(f"  Best Trigger: {result['suffix']}")
        print(f"  Best Loss: {result['loss']:.4f}")
        print(f"  Execution Time: {execution_time:.2f}s")
        print(f"  Successful Attempts: {sum(1 for a in result['attempts'] if a['success'])}/{len(result['attempts'])}")
    else:
        print(f"✗ FAILED")
        print(f"  Execution Time: {execution_time:.2f}s")
        print(f"  Total Attempts: {len(result['attempts'])}")
    
    # 显示详细尝试信息
    if args.verbose:
        print(f"\nDetailed Attempts:")
        for i, attempt in enumerate(result["attempts"]):
            status = "✓" if attempt["success"] else "✗"
            print(f"  {i+1}. {status} Initial: '{attempt['initial_suffix']}' -> Final: '{attempt['final_suffix']}'")
            print(f"     Loss: {attempt['final_loss']:.4f}, Steps: {attempt['steps']}")
    
    # 保存结果
    if args.output_file:
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(experiment_result, f, indent=2, ensure_ascii=False)
        print(f"\nResults saved to: {args.output_file}")
    
    return experiment_result

def main():
    """主函数"""
    args = parse_arguments()
    
    try:
        result = run_single_target_experiment(args)
        
        # 返回适当的退出码
        if result["success"]:
            print("\nExperiment completed successfully!")
            sys.exit(0)
        else:
            print("\nExperiment completed but attack failed.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\nExperiment failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(2)

if __name__ == "__main__":
    main()
