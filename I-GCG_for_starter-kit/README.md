# I-GCG for Backdoor Trigger Recovery

基于I-GCG最新技术的后门触发器恢复解决方案，专门针对CLAS 2024 Backdoor Trigger Recovery for Models Track设计。

## 项目结构

```
I-GCG_for_starter-kit/
├── README.md                    # 项目说明
├── requirements.txt             # 依赖包
├── config/                      # 配置文件
│   ├── model_config.py         # 模型配置
│   ├── attack_config.py        # 攻击参数配置
│   └── target_config.py        # 目标配置
├── core/                       # 核心算法模块
│   ├── __init__.py
│   ├── improved_gcg.py         # 改进的GCG算法
│   ├── suffix_manager.py       # 后缀管理器
│   ├── optimization.py         # 优化算法
│   └── gradient_utils.py       # 梯度计算工具
├── initialization/             # 初始化策略模块
│   ├── __init__.py
│   ├── semantic_init.py        # 语义引导初始化
│   ├── diverse_init.py         # 多样化初始化
│   ├── progressive_init.py     # 渐进式初始化
│   └── adaptive_init.py        # 自适应初始化
├── evaluation/                 # 评估模块
│   ├── __init__.py
│   ├── trigger_validator.py    # 触发器验证
│   ├── success_checker.py      # 成功率检查
│   └── metrics.py              # 评估指标
├── utils/                      # 工具模块
│   ├── __init__.py
│   ├── model_utils.py          # 模型工具
│   ├── data_utils.py           # 数据处理工具
│   ├── string_utils.py         # 字符串处理工具
│   └── logging_utils.py        # 日志工具
├── experiments/                # 实验脚本
│   ├── __init__.py
│   ├── single_target.py        # 单目标实验
│   ├── multi_target.py         # 多目标实验
│   └── ablation_study.py       # 消融实验
├── main.py                     # 主程序入口
└── submission.py               # 提交文件生成
```

## 核心特性

1. **改进的GCG算法**: 基于I-GCG论文的最新优化技术
2. **智能初始化**: 多种初始化策略的组合
3. **自适应优化**: 根据目标特征动态调整参数
4. **模块化设计**: 清晰的代码结构，易于扩展和维护
5. **全面评估**: 多维度的触发器效果验证

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 系统测试
```bash
python test_system.py
```

### 3. 快速演示
```bash
python quick_start.py
```

### 4. 单目标恢复
```bash
python experiments/single_target.py --target "raise Exception('Stop here')"
```

### 5. 完整实验
```bash
python main.py --mode full --output_dir results/
```

### 6. 生成提交文件
```bash
python submission.py --output_file predictions.json
```

## 详细使用说明

### 配置文件说明

- `config/model_config.py`: 模型相关配置
- `config/attack_config.py`: 攻击算法参数配置
- `config/target_config.py`: 目标代码和触发器配置

### 主要脚本说明

- `main.py`: 主程序，支持单目标、多目标、完整实验模式
- `submission.py`: 生成比赛提交文件
- `test_system.py`: 系统功能测试
- `quick_start.py`: 快速演示和配置测试
- `experiments/single_target.py`: 单目标详细实验

### 参数调优建议

1. **GPU内存不足**: 减少`batch_size`参数
2. **提高成功率**: 增加`num_steps`和`num_starts`参数
3. **加速实验**: 减少`num_steps`，关闭`progressive_optimization`

## 技术亮点

- **增强目标模板**: 使用"my output is harmful"增强模型配合度
- **多起点优化**: 从多个语义相关的初始点开始优化
- **渐进式token优化**: 逐步增加对抗后缀的复杂度
- **自适应批次大小**: 根据GPU内存动态调整
- **验证机制**: 多样本交叉验证确保触发器有效性

## 项目状态

✅ **已完成功能**:
- 完整的I-GCG算法实现
- 多种智能初始化策略
- 模块化配置管理系统
- 完善的工具支持
- 系统测试和验证
- 详细的文档和使用指南

🔧 **配置要求**:
- 模型路径: 需要设置正确的CodeQwen1.5-7B-trojan模型路径
- GPU内存: 建议16GB以上
- Python环境: Python 3.8+

📋 **测试状态**:
- ✅ 模块导入测试通过
- ✅ 配置创建测试通过
- ✅ 初始化器测试通过
- ✅ 字符串工具测试通过
- ✅ GPU可用性检测通过
- ✅ 对话模板加载通过

## 配置说明

模型路径: 需要在`config/model_config.py`中设置正确的模型路径
目标代码: 参考`config/target_config.py`中的预定义目标

## 文档说明

- `README.md`: 项目概述和快速开始
- `USAGE_GUIDE.md`: 详细使用指南和配置说明
- `PROJECT_SUMMARY.md`: 完整的技术实现总结
