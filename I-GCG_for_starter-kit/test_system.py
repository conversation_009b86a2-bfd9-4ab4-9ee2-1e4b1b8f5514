"""
系统测试脚本
验证I-GCG系统的基本功能
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """测试所有模块导入"""
    print("Testing imports...")
    
    try:
        # 配置模块
        from config.model_config import ModelConfig, DEFAULT_MODEL_CONFIG
        from config.attack_config import AttackConfig, IGCG_BEST_CONFIG
        from config.target_config import TargetConfig, DEFAULT_TARGET_CONFIG
        print("  ✓ Config modules imported successfully")
        
        # 核心模块
        from core.gradient_utils import token_gradients, enhanced_token_gradients
        from core.suffix_manager import EnhancedSuffixManager, load_conversation_template
        from core.optimization import enhanced_sample_control, progressive_optimization
        from core.improved_gcg import ImprovedGCG
        print("  ✓ Core modules imported successfully")
        
        # 初始化模块
        from initialization.semantic_init import SemanticInitializer
        from initialization.diverse_init import DiverseInitializer
        from initialization.progressive_init import ProgressiveInitializer
        from initialization.adaptive_init import AdaptiveInitializer
        print("  ✓ Initialization modules imported successfully")
        
        # 工具模块
        from utils.model_utils import load_model_and_tokenizer, check_gpu_availability
        from utils.string_utils import get_nonascii_toks, clean_text
        print("  ✓ Utility modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Import failed: {e}")
        traceback.print_exc()
        return False

def test_configurations():
    """测试配置创建"""
    print("\nTesting configurations...")
    
    try:
        from config.model_config import ModelConfig
        from config.attack_config import AttackConfig
        from config.target_config import TargetConfig
        
        # 测试模型配置
        model_config = ModelConfig(
            model_path="/fake/path",
            device="cpu"
        )
        assert model_config.model_path == "/fake/path"
        assert model_config.device == "cpu"
        print("  ✓ Model config created successfully")
        
        # 测试攻击配置
        attack_config = AttackConfig(
            num_steps=100,
            batch_size=32
        )
        assert attack_config.num_steps == 100
        assert attack_config.batch_size == 32
        print("  ✓ Attack config created successfully")
        
        # 测试目标配置
        target_config = TargetConfig()
        target_list = target_config.get_target_list()
        assert len(target_list) > 0
        print(f"  ✓ Target config created successfully ({len(target_list)} targets)")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def test_initializers():
    """测试初始化器"""
    print("\nTesting initializers...")
    
    try:
        from config.target_config import TargetConfig
        from initialization.semantic_init import SemanticInitializer
        from initialization.diverse_init import DiverseInitializer
        from initialization.progressive_init import ProgressiveInitializer
        
        target_config = TargetConfig()
        
        # 测试语义初始化器
        semantic_init = SemanticInitializer(target_config)
        semantic_candidates = semantic_init.generate_semantic_candidates(
            "import sys\nsys.exit()", 3
        )
        assert len(semantic_candidates) > 0
        print(f"  ✓ Semantic initializer: {semantic_candidates[0]}")
        
        # 测试多样化初始化器（需要假的tokenizer）
        class FakeTokenizer:
            def encode(self, text, add_special_tokens=False):
                return list(range(len(text.split())))
        
        fake_tokenizer = FakeTokenizer()
        diverse_init = DiverseInitializer(fake_tokenizer, target_config)
        diverse_candidates = diverse_init.generate_diverse_candidates(
            "import socket", 3
        )
        assert len(diverse_candidates) > 0
        print(f"  ✓ Diverse initializer: {diverse_candidates[0]}")
        
        # 测试渐进式初始化器
        progressive_init = ProgressiveInitializer(fake_tokenizer)
        progressive_candidates = progressive_init.generate_progressive_candidates(3)
        assert len(progressive_candidates) > 0
        print(f"  ✓ Progressive initializer: {progressive_candidates[0]}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Initializer test failed: {e}")
        traceback.print_exc()
        return False

def test_string_utils():
    """测试字符串工具"""
    print("\nTesting string utilities...")
    
    try:
        from utils.string_utils import clean_text, extract_code_blocks, detect_programming_language
        
        # 测试文本清理
        dirty_text = "  Hello   world  \n\n  "
        clean = clean_text(dirty_text)
        assert clean == "Hello world"
        print("  ✓ Text cleaning works")
        
        # 测试代码块提取
        text_with_code = "Here is some code: ```python\nprint('hello')\n``` and more text"
        code_blocks = extract_code_blocks(text_with_code)
        assert len(code_blocks) > 0
        print("  ✓ Code block extraction works")
        
        # 测试编程语言检测
        python_code = "def hello():\n    print('world')"
        lang = detect_programming_language(python_code)
        assert lang == "python"
        print("  ✓ Programming language detection works")
        
        return True
        
    except Exception as e:
        print(f"  ✗ String utils test failed: {e}")
        traceback.print_exc()
        return False

def test_gpu_availability():
    """测试GPU可用性检查"""
    print("\nTesting GPU availability...")
    
    try:
        from utils.model_utils import check_gpu_availability
        
        gpu_info = check_gpu_availability()
        print(f"  GPU Available: {gpu_info['available']}")
        if gpu_info['available']:
            print(f"  GPU Count: {gpu_info['device_count']}")
            print(f"  GPU Name: {gpu_info['device_name']}")
            print(f"  GPU Memory: {gpu_info.get('memory_total', 'Unknown'):.1f}GB")
        
        return True
        
    except Exception as e:
        print(f"  ✗ GPU availability test failed: {e}")
        traceback.print_exc()
        return False

def test_conversation_template():
    """测试对话模板加载"""
    print("\nTesting conversation template...")
    
    try:
        from core.suffix_manager import load_conversation_template
        
        # 测试加载Qwen模板
        conv_template = load_conversation_template("qwen")
        assert conv_template is not None
        assert hasattr(conv_template, 'roles')
        print("  ✓ Qwen conversation template loaded")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Conversation template test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("I-GCG System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configurations),
        ("Initializer Test", test_initializers),
        ("String Utils Test", test_string_utils),
        ("GPU Availability Test", test_gpu_availability),
        ("Conversation Template Test", test_conversation_template),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print(f"{'='*50}")
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
