"""
模型配置文件
定义模型路径、设备配置等基础参数
"""

import torch
from dataclasses import dataclass
from typing import Optional

@dataclass
class ModelConfig:
    """模型配置类"""
    
    # 模型路径
    model_path: str = "/data1/jcl/models--<PERSON><PERSON>--CodeQwen1.5-7B-trojan-clas2024-test/snapshots/6f55eb27c2b6fb220e4d160e8d330ef69d9af4f0"
    
    # 设备配置
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    device_id: int = 0
    
    # 模型加载参数
    torch_dtype: torch.dtype = torch.float16
    low_cpu_mem_usage: bool = True
    use_cache: bool = False
    trust_remote_code: bool = True
    
    # 对话模板
    template_name: str = "qwen"
    system_message: str = "You are a helpful assistant."
    
    # 生成参数
    max_new_tokens: int = 64
    temperature: float = 0.6
    top_p: float = 0.9
    do_sample: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        if self.device == "cuda":
            self.device = f"cuda:{self.device_id}"

# 默认配置实例
DEFAULT_MODEL_CONFIG = ModelConfig()

# 测试配置（用于快速调试）
TEST_MODEL_CONFIG = ModelConfig(
    max_new_tokens=32,
    temperature=0.1,
    do_sample=False
)

# 高性能配置（用于正式实验）
HIGH_PERFORMANCE_CONFIG = ModelConfig(
    torch_dtype=torch.float32,
    max_new_tokens=128,
    temperature=0.8
)
